{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.tsx"], "sourcesContent": ["export function EnvironmentNameLabel({\n  environmentName,\n}: {\n  environmentName: string\n}) {\n  return <span data-nextjs-environment-name-label>{environmentName}</span>\n}\n\nexport const ENVIRONMENT_NAME_LABEL_STYLES = `\n  [data-nextjs-environment-name-label] {\n    padding: 2px 6px;\n    margin: 0;\n    border-radius: var(--rounded-md-2);\n    background: var(--color-gray-100);\n    font-weight: 600;\n    font-size: var(--size-12);\n    color: var(--color-gray-900);\n    font-family: var(--font-stack-monospace);\n    line-height: var(--size-20);\n  }\n`\n"], "names": ["ENVIRONMENT_NAME_LABEL_STYLES", "EnvironmentNameLabel", "environmentName", "span", "data-nextjs-environment-name-label"], "mappings": ";;;;;;;;;;;;;;;IAQaA,6BAA6B;eAA7BA;;IARGC,oBAAoB;eAApBA;;;;AAAT,SAASA,qBAAqB,KAIpC;IAJoC,IAAA,EACnCC,eAAe,EAGhB,GAJoC;IAKnC,qBAAO,qBAACC;QAAKC,oCAAkC;kBAAEF;;AACnD;AAEO,MAAMF,gCAAiC"}