"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/sections/PilotCampaign.tsx":
/*!****************************************!*\
  !*** ./app/sections/PilotCampaign.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ScaleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst PilotCampaign = ()=>{\n    const campaignDetails = [\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: \"12 Week Duration\",\n            description: \"Extended pilot to measure deep market impact and ROI\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"7 Posts Per Week\",\n            description: \"High-quality content across TikTok, Instagram & LinkedIn\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"±2M IDR/Week\",\n            description: \"Professional editor + enhanced posting budget\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Advanced Analytics\",\n            description: \"Google Data Studio + TikTok Analytics + IG Insights\"\n        }\n    ];\n    const contentPillars = [\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Legal & Process\",\n            topics: [\n                \"Leasehold vs. freehold\",\n                \"Transfer requirements\",\n                \"Legal fees\",\n                \"Taxes\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Material & Build Quality\",\n            topics: [\n                \"Timber vs. concrete pros/cons\",\n                \"Roofing options\",\n                \"Energy-proofing\",\n                \"Finishes\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Cost Breakdown\",\n            topics: [\n                \"Build cost per m²\",\n                \"Hidden costs\",\n                \"Infrastructure\",\n                \"Permits\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"Tenant & ROI\",\n            topics: [\n                \"Rental yield vs. capital growth\",\n                \"Case examples\",\n                \"Short vs. long-term rentals\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Investor Profiles\",\n            topics: [\n                \"Private expats & families\",\n                \"Small investor funds\",\n                \"Minimum investment\",\n                \"Expected returns\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Local Trust Network\",\n            topics: [\n                \"Vetted architects & lawyers\",\n                \"Trusted funders\",\n                \"Builder verification\",\n                \"FAQs\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.span, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            className: \"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6\",\n                            children: \"Content Strategy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            className: \"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"12-Week\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Content Strategy\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            className: \"text-xl text-muted max-w-3xl mx-auto\",\n                            children: \"Deep content pillars and multi-platform approach to establish market authority and build trust with potential investors.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-default text-center mb-8\",\n                            children: \"Campaign Structure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: campaignDetails.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1 * index\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(detail.icon, {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-default mb-2\",\n                                            children: detail.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted\",\n                                            children: detail.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.5\n                                },\n                                className: \"bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-6 border border-primary/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-xl font-bold text-default mb-6\",\n                                        children: \"\\uD83D\\uDCA1 Content Pillars (Deep & Broad)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: contentPillars.map((pillar, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.4,\n                                                    delay: 0.6 + index * 0.1\n                                                },\n                                                className: \"border-l-4 border-primary pl-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(pillar.icon, {\n                                                                className: \"w-4 h-4 text-primary mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"font-semibold text-default text-sm\",\n                                                                children: pillar.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: pillar.topics.map((topic, topicIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-white/50 text-muted px-2 py-1 rounded-full\",\n                                                                children: topic\n                                                            }, topicIndex, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-default mb-8\",\n                                    children: \"Content Strategy & Timeline\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-2 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 mr-2 bg-white rounded-sm flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-gradient-to-br from-purple-400 via-pink-500 to-orange-500 rounded-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold\",\n                                                    children: \"Instagram\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-black text-white px-4 py-2 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 mr-2 bg-white rounded-sm flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-black rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold\",\n                                                    children: \"TikTok\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-primary to-accent h-48 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-bold mb-2\",\n                                                        children: \"Build Cost Breakdown\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm opacity-90\",\n                                                        children: \"Villa construction: What €200k really gets you\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-sm\",\n                                                            children: \"indonesian_paradise_property\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted\",\n                                                    children: [\n                                                        \"Clients often ask what a €200k budget actually builds in Bali. Our legal experts share real-world cost ranges and hidden expenses to watch out for.\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-primary\",\n                                                            children: \"#BaliProperty #BuildCosts #Investment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PilotCampaign;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PilotCampaign);\nvar _c;\n$RefreshReg$(_c, \"PilotCampaign\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/sections/PilotCampaign.tsx\n"));

/***/ })

});