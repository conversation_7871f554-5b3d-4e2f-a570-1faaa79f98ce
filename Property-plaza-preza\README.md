# Property Plaza × Paradise Indonesia
## Strategic Partnership Presentation

A professional, scroll-based interactive presentation showcasing the strategic collaboration between Property Plaza and Paradise Indonesia for empowering property decisions in Bali.

## 🚀 Live Demo

[View Live Presentation](https://your-vercel-url.vercel.app)

## 🎯 Project Overview

This immersive presentation demonstrates how Property Plaza's innovative technology platform can partner with Paradise Indonesia's trusted expertise to create transparency and confidence in Bali's property market.

### Key Features

- **9 Interactive Sections**: Complete story from problem to solution
- **Scroll-based Navigation**: Smooth transitions between sections
- **Professional Branding**: Property Plaza brand colors and typography
- **Responsive Design**: Optimized for all devices
- **Framer Motion Animations**: Engaging micro-interactions
- **Real Business Content**: Actual partnership proposal with metrics

## 🛠 Tech Stack

- **Framework**: Next.js 15.3.5
- **Styling**: TailwindCSS + Custom CSS
- **Animations**: Framer Motion
- **Icons**: Heroicons
- **Typography**: Inter Font
- **Deployment**: Vercel

## 📋 Presentation Sections

1. **Hero** - Immersive opening with brand introduction
2. **Market Problem** - Visual representation of current challenges
3. **Four Pillars** - Interactive cards showcasing core values
4. **About Property Plaza** - Platform stats and achievements
5. **Why Paradise Indonesia** - Partnership rationale
6. **Synergy** - Combined value proposition
7. **Pilot Campaign** - 4-week campaign proposal
8. **Metrics & KPIs** - Success measurement framework
9. **Final CTA** - Call-to-action with contact information

## 🎨 Brand Guidelines

- **Primary Color**: #b78b4c (Warm Gold)
- **Accent Color**: #8a6b3c (Deep Bronze)
- **Background**: #f7f1eb (Light Sand)
- **Text**: #1e1e1e (Rich Black)
- **Muted**: #666666 (Warm Gray)

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd property-plaza-presentation

# Install dependencies
npm install

# Run development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the presentation.

## 📦 Deployment

### Deploy to Vercel

1. Push your code to GitHub
2. Connect your GitHub repository to Vercel
3. Deploy automatically on every push

```bash
# Add and commit files
git add .
git commit -m "Add missing files for deployment"
git push origin main
```

## 🎯 Business Context

This presentation supports a strategic partnership proposal between:

- **Property Plaza**: Modern property platform with AI-assisted workflows
- **Paradise Indonesia**: Established legal expertise in Bali real estate

**Pilot Campaign**: 4-week content collaboration (±1.5M IDR/week budget)

## 📊 Expected Metrics

- **Leads Generated**: 50+
- **WhatsApp Inquiries**: 25+
- **Content Reach**: 10K+ views
- **Audience Growth**: 15% increase

## 📞 Contact

- **Email**: <EMAIL>
- **WhatsApp**: +62 123 456 7890
- **Website**: [property-plaza.com](https://property-plaza.com)

---

*Built with ❤️ for transparent property decisions in Bali*
