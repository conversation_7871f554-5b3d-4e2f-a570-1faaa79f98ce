{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/client.ts"], "sourcesContent": ["import * as Bus from './bus'\nimport { parseStack } from '../utils/parse-stack'\nimport { parseComponentStack } from '../utils/parse-component-stack'\nimport { storeHydrationErrorStateFromConsoleArgs } from '../../errors/hydration-error-info'\nimport {\n  ACTION_BEFORE_REFRESH,\n  ACTION_BUILD_ERROR,\n  ACTION_BUILD_OK,\n  ACTION_DEV_INDICATOR,\n  ACTION_REFRESH,\n  ACTION_STATIC_INDICATOR,\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n  ACTION_VERSION_INFO,\n} from '../shared'\nimport type { VersionInfo } from '../../../../server/dev/parse-version-info'\nimport { attachHydrationErrorState } from '../../errors/attach-hydration-error-state'\nimport type { DevIndicatorServerState } from '../../../../server/dev/dev-indicator-server-state'\n\nlet isRegistered = false\n\nfunction handleError(error: unknown) {\n  if (!error || !(error instanceof Error) || typeof error.stack !== 'string') {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  attachHydrationErrorState(error)\n\n  const componentStackTrace = (error as any)._componentStack\n  const componentStackFrames =\n    typeof componentStackTrace === 'string'\n      ? parseComponentStack(componentStackTrace)\n      : undefined\n\n  // Skip ModuleBuildError and ModuleNotFoundError, as it will be sent through onBuildError callback.\n  // This is to avoid same error as different type showing up on client to cause flashing.\n  if (\n    error.name !== 'ModuleBuildError' &&\n    error.name !== 'ModuleNotFoundError'\n  ) {\n    Bus.emit({\n      type: ACTION_UNHANDLED_ERROR,\n      reason: error,\n      frames: parseStack(error.stack),\n      componentStackFrames,\n    })\n  }\n}\n\nlet origConsoleError = console.error\nfunction nextJsHandleConsoleError(...args: any[]) {\n  // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n  const error = process.env.NODE_ENV !== 'production' ? args[1] : args[0]\n  storeHydrationErrorStateFromConsoleArgs(...args)\n  handleError(error)\n  origConsoleError.apply(window.console, args)\n}\n\nfunction onUnhandledError(event: ErrorEvent) {\n  const error = event?.error\n  handleError(error)\n}\n\nfunction onUnhandledRejection(ev: PromiseRejectionEvent) {\n  const reason = ev?.reason\n  if (\n    !reason ||\n    !(reason instanceof Error) ||\n    typeof reason.stack !== 'string'\n  ) {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  const e = reason\n  Bus.emit({\n    type: ACTION_UNHANDLED_REJECTION,\n    reason: reason,\n    frames: parseStack(e.stack!),\n  })\n}\n\nexport function register() {\n  if (isRegistered) {\n    return\n  }\n  isRegistered = true\n\n  try {\n    Error.stackTraceLimit = 50\n  } catch {}\n\n  window.addEventListener('error', onUnhandledError)\n  window.addEventListener('unhandledrejection', onUnhandledRejection)\n  window.console.error = nextJsHandleConsoleError\n}\n\nexport function onBuildOk() {\n  Bus.emit({ type: ACTION_BUILD_OK })\n}\n\nexport function onBuildError(message: string) {\n  Bus.emit({ type: ACTION_BUILD_ERROR, message })\n}\n\nexport function onRefresh() {\n  Bus.emit({ type: ACTION_REFRESH })\n}\n\nexport function onBeforeRefresh() {\n  Bus.emit({ type: ACTION_BEFORE_REFRESH })\n}\n\nexport function onVersionInfo(versionInfo: VersionInfo) {\n  Bus.emit({ type: ACTION_VERSION_INFO, versionInfo })\n}\n\nexport function onStaticIndicator(isStatic: boolean) {\n  Bus.emit({ type: ACTION_STATIC_INDICATOR, staticIndicator: isStatic })\n}\n\nexport function onDevIndicator(devIndicatorsState: DevIndicatorServerState) {\n  Bus.emit({ type: ACTION_DEV_INDICATOR, devIndicator: devIndicatorsState })\n}\n\nexport { getErrorByType } from '../utils/get-error-by-type'\nexport { getServerError } from '../utils/node-stack-frames'\n"], "names": ["getErrorByType", "getServerError", "onBeforeRefresh", "onBuildError", "onBuildOk", "onDevIndicator", "onRefresh", "onStaticIndicator", "onVersionInfo", "register", "isRegistered", "handleError", "error", "Error", "stack", "attachHydrationErrorState", "componentStackTrace", "_componentStack", "componentStackFrames", "parseComponentStack", "undefined", "name", "Bus", "emit", "type", "ACTION_UNHANDLED_ERROR", "reason", "frames", "parseStack", "origConsoleError", "console", "nextJsHandleConsoleError", "args", "process", "env", "NODE_ENV", "storeHydrationErrorStateFromConsoleArgs", "apply", "window", "onUnhandledError", "event", "onUnhandledRejection", "ev", "e", "ACTION_UNHANDLED_REJECTION", "stackTraceLimit", "addEventListener", "ACTION_BUILD_OK", "message", "ACTION_BUILD_ERROR", "ACTION_REFRESH", "ACTION_BEFORE_REFRESH", "versionInfo", "ACTION_VERSION_INFO", "isStatic", "ACTION_STATIC_INDICATOR", "staticIndicator", "devIndicatorsState", "ACTION_DEV_INDICATOR", "devIndicator"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IA8HSA,cAAc;eAAdA,8BAAc;;IACdC,cAAc;eAAdA,+BAAc;;IAjBPC,eAAe;eAAfA;;IARAC,YAAY;eAAZA;;IAJAC,SAAS;eAATA;;IAwBAC,cAAc;eAAdA;;IAhBAC,SAAS;eAATA;;IAYAC,iBAAiB;eAAjBA;;IAJAC,aAAa;eAAbA;;IA/BAC,QAAQ;eAARA;;;;+DAnFK;4BACM;qCACS;oCACoB;wBAWjD;2CAEmC;gCA8GX;iCACA;AA5G/B,IAAIC,eAAe;AAEnB,SAASC,YAAYC,KAAc;IACjC,IAAI,CAACA,SAAS,CAAEA,CAAAA,iBAAiBC,KAAI,KAAM,OAAOD,MAAME,KAAK,KAAK,UAAU;QAC1E,8DAA8D;QAC9D;IACF;IAEAC,IAAAA,oDAAyB,EAACH;IAE1B,MAAMI,sBAAsB,AAACJ,MAAcK,eAAe;IAC1D,MAAMC,uBACJ,OAAOF,wBAAwB,WAC3BG,IAAAA,wCAAmB,EAACH,uBACpBI;IAEN,mGAAmG;IACnG,wFAAwF;IACxF,IACER,MAAMS,IAAI,KAAK,sBACfT,MAAMS,IAAI,KAAK,uBACf;QACAC,KAAIC,IAAI,CAAC;YACPC,MAAMC,8BAAsB;YAC5BC,QAAQd;YACRe,QAAQC,IAAAA,sBAAU,EAAChB,MAAME,KAAK;YAC9BI;QACF;IACF;AACF;AAEA,IAAIW,mBAAmBC,QAAQlB,KAAK;AACpC,SAASmB;IAAyB,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAc;;IAC9C,iJAAiJ;IACjJ,MAAMpB,QAAQqB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAeH,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;IACvEI,IAAAA,2DAAuC,KAAIJ;IAC3CrB,YAAYC;IACZiB,iBAAiBQ,KAAK,CAACC,OAAOR,OAAO,EAAEE;AACzC;AAEA,SAASO,iBAAiBC,KAAiB;IACzC,MAAM5B,QAAQ4B,yBAAAA,MAAO5B,KAAK;IAC1BD,YAAYC;AACd;AAEA,SAAS6B,qBAAqBC,EAAyB;IACrD,MAAMhB,SAASgB,sBAAAA,GAAIhB,MAAM;IACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBb,KAAI,KACxB,OAAOa,OAAOZ,KAAK,KAAK,UACxB;QACA,8DAA8D;QAC9D;IACF;IAEA,MAAM6B,IAAIjB;IACVJ,KAAIC,IAAI,CAAC;QACPC,MAAMoB,kCAA0B;QAChClB,QAAQA;QACRC,QAAQC,IAAAA,sBAAU,EAACe,EAAE7B,KAAK;IAC5B;AACF;AAEO,SAASL;IACd,IAAIC,cAAc;QAChB;IACF;IACAA,eAAe;IAEf,IAAI;QACFG,MAAMgC,eAAe,GAAG;IAC1B,EAAE,UAAM,CAAC;IAETP,OAAOQ,gBAAgB,CAAC,SAASP;IACjCD,OAAOQ,gBAAgB,CAAC,sBAAsBL;IAC9CH,OAAOR,OAAO,CAAClB,KAAK,GAAGmB;AACzB;AAEO,SAAS3B;IACdkB,KAAIC,IAAI,CAAC;QAAEC,MAAMuB,uBAAe;IAAC;AACnC;AAEO,SAAS5C,aAAa6C,OAAe;IAC1C1B,KAAIC,IAAI,CAAC;QAAEC,MAAMyB,0BAAkB;QAAED;IAAQ;AAC/C;AAEO,SAAS1C;IACdgB,KAAIC,IAAI,CAAC;QAAEC,MAAM0B,sBAAc;IAAC;AAClC;AAEO,SAAShD;IACdoB,KAAIC,IAAI,CAAC;QAAEC,MAAM2B,6BAAqB;IAAC;AACzC;AAEO,SAAS3C,cAAc4C,WAAwB;IACpD9B,KAAIC,IAAI,CAAC;QAAEC,MAAM6B,2BAAmB;QAAED;IAAY;AACpD;AAEO,SAAS7C,kBAAkB+C,QAAiB;IACjDhC,KAAIC,IAAI,CAAC;QAAEC,MAAM+B,+BAAuB;QAAEC,iBAAiBF;IAAS;AACtE;AAEO,SAASjD,eAAeoD,kBAA2C;IACxEnC,KAAIC,IAAI,CAAC;QAAEC,MAAMkC,4BAAoB;QAAEC,cAAcF;IAAmB;AAC1E"}