"use client";

import { motion } from 'framer-motion';
import Image from 'next/image';

const Synergy = () => {
  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background py-20">
      <div className="max-w-6xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-3xl md:text-4xl font-bold text-default mb-8 leading-tight"
          >
            Together We Offer <span className="gradient-text">Certainty</span>
            <br />
            In An Uncertain Market
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            {/* Logo Merge Animation */}
            <div className="flex items-center justify-center mb-8">
              <motion.div
                className="flex items-center justify-center w-36 h-24 mr-8"
                whileHover={{ scale: 1.05, rotate: 2 }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  src="/images/Transparent -02.png"
                  alt="Property Plaza Logo"
                  width={300}
                  height={300}
                  className="object-contain drop-shadow-lg"
                />
              </motion.div>

              <motion.div
                className="text-3xl text-primary flex items-center justify-center mr-8"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                +
              </motion.div>

              <motion.div
                className="flex items-center justify-center w-36 h-24"
                whileHover={{ scale: 1.05, rotate: -2 }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  src="/images/paradise-indonesia.png"
                  alt="Paradise Indonesia Logo"
                  width={120}
                  height={120}
                  className="object-contain drop-shadow-lg"
                />
              </motion.div>
            </div>

            {/* Key Points */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="space-y-3 mb-6 max-w-2xl mx-auto"
            >
              <div className="text-base text-muted">🔗 Structure meets speed.</div>
              <div className="text-base text-muted">📱 Tech meets trust.</div>
              <div className="text-base text-muted">🧭 Legal certainty meets creative access.</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
              className="text-base text-muted max-w-2xl mx-auto border-t border-primary/20 pt-4"
            >
              When trusted legal foundations meet digital innovation, Bali's property market gets what it's always needed: access without risk.
              <br />
              <span className="text-primary font-medium">That's what happens when Paradise Indonesia partners with Property Plaza.</span>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Synergy;
