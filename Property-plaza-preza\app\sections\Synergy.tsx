"use client";

import { motion } from 'framer-motion';
import Image from 'next/image';

const Synergy = () => {
  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background py-20">
      <div className="max-w-6xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-3xl sm:text-4xl md:text-5xl font-bold text-default mb-8 md:mb-12 leading-tight"
          >
            Together We Offer <span className="gradient-text">Certainty</span>
            <br className="hidden sm:block" />
            <span className="sm:hidden"> </span>In An Uncertain Market
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            {/* Logo Merge Animation */}
            <div className="flex flex-col sm:flex-row items-center justify-center mb-8 md:mb-12 gap-4 sm:gap-8">
              <motion.div
                className="flex items-center justify-center w-32 h-24 sm:w-40 sm:h-28 md:w-48 md:h-32"
                whileHover={{ scale: 1.05, rotate: 2 }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  src="/images/Transparent -02.png"
                  alt="Property Plaza Logo"
                  width={400}
                  height={400}
                  className="object-contain drop-shadow-lg"
                />
              </motion.div>

              <motion.div
                className="text-2xl sm:text-3xl md:text-4xl text-primary flex items-center justify-center"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                +
              </motion.div>

              <motion.div
                className="flex items-center justify-center w-32 h-24 sm:w-40 sm:h-28 md:w-48 md:h-32"
                whileHover={{ scale: 1.05, rotate: -2 }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  src="/images/paradise-indonesia.png"
                  alt="Paradise Indonesia Logo"
                  width={160}
                  height={160}
                  className="object-contain drop-shadow-lg"
                />
              </motion.div>
            </div>

            {/* Key Points */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="space-y-2 sm:space-y-3 mb-4 sm:mb-6 max-w-2xl mx-auto px-4"
            >
              <div className="text-sm sm:text-base text-muted">🔗 Structure meets speed.</div>
              <div className="text-sm sm:text-base text-muted">📱 Tech meets trust.</div>
              <div className="text-sm sm:text-base text-muted">🧭 Legal certainty meets creative access.</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
              className="text-sm sm:text-base text-muted max-w-2xl mx-auto border-t border-primary/20 pt-4 px-4"
            >
              When trusted legal foundations meet digital innovation, Bali's property market gets what it's always needed: access without risk.
              <br className="hidden sm:block" />
              <span className="sm:hidden"> </span>
              <span className="text-primary font-medium">That's what happens when Paradise Indonesia partners with Property Plaza.</span>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Synergy;
