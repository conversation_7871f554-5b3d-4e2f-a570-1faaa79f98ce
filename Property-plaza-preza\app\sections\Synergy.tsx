"use client";

import { motion } from 'framer-motion';
import Image from 'next/image';

const Synergy = () => {
  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background py-20">
      <div className="max-w-6xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl md:text-5xl font-bold text-default mb-12 leading-tight"
          >
            Together We Offer <span className="gradient-text">Certainty</span>
            <br />
            In An Uncertain Market
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            {/* Logo Merge Animation */}
            <div className="flex items-center justify-center space-x-8 mb-12">
              <motion.div
                className="w-40 h-40 bg-white rounded-2xl flex items-center justify-center shadow-xl p-6"
                whileHover={{ scale: 1.05, rotate: 2 }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  src="/images/Transparent -02.png"
                  alt="Property Plaza Logo"
                  width={120}
                  height={120}
                  className="object-contain"
                />
              </motion.div>

              <motion.div
                className="text-4xl text-primary"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                +
              </motion.div>

              <motion.div
                className="w-40 h-40 bg-white rounded-2xl flex items-center justify-center shadow-xl p-6"
                whileHover={{ scale: 1.05, rotate: -2 }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  src="/images/paradise-indonesia.png"
                  alt="Paradise Indonesia Logo"
                  width={120}
                  height={120}
                  className="object-contain"
                />
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="text-xl text-muted max-w-3xl mx-auto"
            >
              When Property Plaza's innovative technology meets Paradise Indonesia's trusted expertise, 
              we create an unmatched foundation for confident property decisions in Bali.
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Synergy;
