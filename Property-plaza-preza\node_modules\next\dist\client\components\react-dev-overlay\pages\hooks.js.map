{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/hooks.ts"], "sourcesContent": ["import React from 'react'\nimport * as Bus from './bus'\nimport { useErrorOverlayReducer } from '../shared'\nimport { Router } from '../../../router'\n\nexport const usePagesDevOverlay = () => {\n  const [state, dispatch] = useErrorOverlayReducer('pages')\n\n  React.useEffect(() => {\n    Bus.on(dispatch)\n\n    const { handleStaticIndicator } =\n      require('./hot-reloader-client') as typeof import('./hot-reloader-client')\n\n    Router.events.on('routeChangeComplete', handleStaticIndicator)\n\n    return function () {\n      Router.events.off('routeChangeComplete', handleStaticIndicator)\n      Bus.off(dispatch)\n    }\n  }, [dispatch])\n\n  const onComponentError = React.useCallback(\n    (_error: Error, _componentStack: string | null) => {\n      // TODO: special handling\n    },\n    []\n  )\n\n  return {\n    state,\n    onComponentError,\n  }\n}\n"], "names": ["usePagesDevOverlay", "state", "dispatch", "useErrorOverlayReducer", "React", "useEffect", "Bus", "on", "handleStaticIndicator", "require", "Router", "events", "off", "onComponentError", "useCallback", "_error", "_componentStack"], "mappings": ";;;;+BAKaA;;;eAAAA;;;;;gEALK;+DACG;wBACkB;wBAChB;AAEhB,MAAMA,qBAAqB;IAChC,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,8BAAsB,EAAC;IAEjDC,cAAK,CAACC,SAAS,CAAC;QACdC,KAAIC,EAAE,CAACL;QAEP,MAAM,EAAEM,qBAAqB,EAAE,GAC7BC,QAAQ;QAEVC,cAAM,CAACC,MAAM,CAACJ,EAAE,CAAC,uBAAuBC;QAExC,OAAO;YACLE,cAAM,CAACC,MAAM,CAACC,GAAG,CAAC,uBAAuBJ;YACzCF,KAAIM,GAAG,CAACV;QACV;IACF,GAAG;QAACA;KAAS;IAEb,MAAMW,mBAAmBT,cAAK,CAACU,WAAW,CACxC,CAACC,QAAeC;IACd,yBAAyB;IAC3B,GACA,EAAE;IAGJ,OAAO;QACLf;QACAY;IACF;AACF"}