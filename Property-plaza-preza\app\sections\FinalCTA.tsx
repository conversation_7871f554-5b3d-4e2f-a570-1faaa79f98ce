"use client";

import { motion } from 'framer-motion';
import { CheckCircleIcon, PhoneIcon, EnvelopeIcon, CalendarIcon } from '@heroicons/react/24/outline';

const FinalCTA = () => {
  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-accent/10 py-20">
      <div className="max-w-6xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-4xl md:text-6xl font-bold text-default mb-8 leading-tight"
          >
            Next Step: Align + <span className="gradient-text">Kick‑off</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-xl text-muted mb-12 max-w-3xl mx-auto"
          >
            Let’s align on objectives & timelines, then start our pilot phase.
          </motion.p>

          {/* Main CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary text-m group relative overflow-hidden w-full sm:w-64"
            >
              <span className="relative z-10 flex items-center justify-center">
                <CheckCircleIcon className="w-6 h-6 mr-2" />
                Start the Pilot Together
              </span>
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-accent to-primary"
                initial={{ x: '-100%' }}
                whileHover={{ x: 0 }}
                transition={{ duration: 0.3 }}
              />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-secondary text-m group w-full sm:w-64"
            >
              <span className="flex items-center justify-center">
                <CalendarIcon className="w-6 h-6 mr-2" />
                Let's align on Details
              </span>
            </motion.button>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 max-w-4xl mx-auto"
          >
            <h3 className="text-2xl font-bold text-default mb-8">Let’s Talk Details</h3>
            
            <div className="grid md:grid-cols-3 gap-8">
              <motion.a
                href="mailto:<EMAIL>"
                whileHover={{ scale: 1.05 }}
                className="flex flex-col items-center p-6 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors group"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <EnvelopeIcon className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-semibold text-default mb-2">Email</h4>
                <p className="text-sm text-muted"><EMAIL></p>
              </motion.a>

              <motion.a
                href="https://wa.me/+6282341891047"
                whileHover={{ scale: 1.05 }}
                className="flex flex-col items-center p-6 rounded-xl bg-green-50 hover:bg-green-100 transition-colors group"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <PhoneIcon className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-semibold text-default mb-2">WhatsApp</h4>
                <p className="text-sm text-muted">+62 823 4189 1047</p>
              </motion.a>

              <motion.a
                href="https://calendly.com/propertyplaza"
                whileHover={{ scale: 1.05 }}
                className="flex flex-col items-center p-6 rounded-xl bg-primary/5 hover:bg-primary/10 transition-colors group"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <CalendarIcon className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-semibold text-default mb-2">Calendly</h4>
                <p className="text-sm text-muted">Schedule a meeting</p>
              </motion.a>
            </div>
          </motion.div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mt-12 text-center"
          >
            <p className="text-muted text-sm">
              © 2024 Property Plaza × Paradise Indonesia Strategic Partnership
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default FinalCTA;
