"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/sections/Synergy.tsx":
/*!**********************************!*\
  !*** ./app/sections/Synergy.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Synergy = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 30\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        className: \"text-4xl md:text-5xl font-bold text-default mb-12 leading-tight\",\n                        children: [\n                            \"Together We Offer \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"gradient-text\",\n                                children: \"Certainty\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, undefined),\n                            \"In An Uncertain Market\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-16 mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        className: \"flex items-center justify-center w-32 h-32\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            rotate: 2\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: \"/images/Transparent -02.png\",\n                                            alt: \"Property Plaza Logo\",\n                                            width: 400,\n                                            height: 400,\n                                            className: \"object-contain drop-shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        className: \"text-4xl text-primary flex items-center justify-center\",\n                                        animate: {\n                                            scale: [\n                                                1,\n                                                1.2,\n                                                1\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity\n                                        },\n                                        children: \"+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        className: \"flex items-center justify-center w-48 h-32\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            rotate: -2\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: \"/images/paradise-indonesia.png\",\n                                            alt: \"Paradise Indonesia Logo\",\n                                            width: 160,\n                                            height: 160,\n                                            className: \"object-contain drop-shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.6\n                                },\n                                className: \"text-xl text-muted max-w-3xl mx-auto\",\n                                children: \"When Property Plaza's innovative technology meets Paradise Indonesia's trusted expertise, we create an unmatched foundation for confident property decisions in Bali.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Synergy;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Synergy);\nvar _c;\n$RefreshReg$(_c, \"Synergy\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/sections/Synergy.tsx\n"));

/***/ })

});