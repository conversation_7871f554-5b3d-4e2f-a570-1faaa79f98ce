{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.tsx"], "sourcesContent": ["import type { VersionInfo } from '../../../../../../../server/dev/parse-version-info'\n\nimport { ErrorOverlayPagination } from '../error-overlay-pagination/error-overlay-pagination'\nimport { VersionStalenessInfo } from '../../version-staleness-info/version-staleness-info'\nimport type { ReadyRuntimeError } from '../../../../utils/get-error-by-type'\n\ntype ErrorOverlayNavProps = {\n  runtimeErrors?: ReadyRuntimeError[]\n  activeIdx?: number\n  setActiveIndex?: (index: number) => void\n  versionInfo?: VersionInfo\n  isTurbopack?: boolean\n}\n\nexport function ErrorOverlayNav({\n  runtimeErrors,\n  activeIdx,\n  setActiveIndex,\n  versionInfo,\n}: ErrorOverlayNavProps) {\n  const bundlerName = (process.env.__NEXT_BUNDLER || 'Webpack') as\n    | 'Webpack'\n    | 'Turbopack'\n    | 'Rspack'\n\n  return (\n    <div data-nextjs-error-overlay-nav>\n      <Notch side=\"left\">\n        {/* TODO: better passing data instead of nullish coalescing */}\n        <ErrorOverlayPagination\n          runtimeErrors={runtimeErrors ?? []}\n          activeIdx={activeIdx ?? 0}\n          onActiveIndexChange={setActiveIndex ?? (() => {})}\n        />\n      </Notch>\n      {versionInfo && (\n        <Notch side=\"right\">\n          <VersionStalenessInfo\n            versionInfo={versionInfo}\n            bundlerName={bundlerName}\n          />\n        </Notch>\n      )}\n    </div>\n  )\n}\n\nexport const styles = `\n  [data-nextjs-error-overlay-nav] {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    width: 100%;\n\n    position: relative;\n    z-index: 2;\n    outline: none;\n    translate: 1px 1px;\n    max-width: var(--next-dialog-max-width);\n\n    .error-overlay-notch {\n      --stroke-color: var(--color-gray-400);\n      --background-color: var(--color-background-100);\n\n      translate: -1px 0;\n      width: auto;\n      height: var(--next-dialog-notch-height);\n      padding: 12px;\n      background: var(--background-color);\n      border: 1px solid var(--stroke-color);\n      border-bottom: none;\n      position: relative;\n\n      &[data-side='left'] {\n        padding-right: 0;\n        border-radius: var(--rounded-xl) 0 0 0;\n\n        .error-overlay-notch-tail {\n          right: -54px;\n        }\n\n        > *:not(.error-overlay-notch-tail) {\n          margin-right: -10px;\n        }\n      }\n\n      &[data-side='right'] {\n        padding-left: 0;\n        border-radius: 0 var(--rounded-xl) 0 0;\n\n        .error-overlay-notch-tail {\n          left: -54px;\n          transform: rotateY(180deg);\n        }\n\n        > *:not(.error-overlay-notch-tail) {\n          margin-left: -12px;\n        }\n      }\n\n      .error-overlay-notch-tail {\n        position: absolute;\n        top: -1px;\n        pointer-events: none;\n        z-index: -1;\n        height: calc(100% + 1px);\n      }\n    }\n  }\n`\n\nfunction Notch({\n  children,\n  side = 'left',\n}: {\n  children: React.ReactNode\n  side?: 'left' | 'right'\n}) {\n  return (\n    <div className=\"error-overlay-notch\" data-side={side}>\n      {children}\n      <Tail />\n    </div>\n  )\n}\n\nfunction Tail() {\n  return (\n    <svg\n      width=\"60\"\n      height=\"42\"\n      viewBox=\"0 0 60 42\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className=\"error-overlay-notch-tail\"\n      preserveAspectRatio=\"none\"\n    >\n      <mask\n        id=\"error_overlay_nav_mask0_2667_14687\"\n        style={{\n          maskType: 'alpha',\n        }}\n        maskUnits=\"userSpaceOnUse\"\n        x=\"0\"\n        y=\"-1\"\n        width=\"60\"\n        height=\"43\"\n      >\n        <mask\n          id=\"error_overlay_nav_path_1_outside_1_2667_14687\"\n          maskUnits=\"userSpaceOnUse\"\n          x=\"0\"\n          y=\"-1\"\n          width=\"60\"\n          height=\"43\"\n          fill=\"black\"\n        >\n          <rect fill=\"white\" y=\"-1\" width=\"60\" height=\"43\" />\n          <path d=\"M1 0L8.0783 0C15.772 0 22.7836 4.41324 26.111 11.3501L34.8889 29.6498C38.2164 36.5868 45.228 41 52.9217 41H60H1L1 0Z\" />\n        </mask>\n        <path\n          d=\"M1 0L8.0783 0C15.772 0 22.7836 4.41324 26.111 11.3501L34.8889 29.6498C38.2164 36.5868 45.228 41 52.9217 41H60H1L1 0Z\"\n          fill=\"white\"\n        />\n        <path\n          d=\"M1 0V-1H0V0L1 0ZM1 41H0V42H1V41ZM34.8889 29.6498L33.9873 30.0823L34.8889 29.6498ZM26.111 11.3501L27.0127 10.9177L26.111 11.3501ZM1 1H8.0783V-1H1V1ZM60 40H1V42H60V40ZM2 41V0L0 0L0 41H2ZM25.2094 11.7826L33.9873 30.0823L35.7906 29.2174L27.0127 10.9177L25.2094 11.7826ZM52.9217 42H60V40H52.9217V42ZM33.9873 30.0823C37.4811 37.3661 44.8433 42 52.9217 42V40C45.6127 40 38.9517 35.8074 35.7906 29.2174L33.9873 30.0823ZM8.0783 1C15.3873 1 22.0483 5.19257 25.2094 11.7826L27.0127 10.9177C23.5188 3.6339 16.1567 -1 8.0783 -1V1Z\"\n          fill=\"black\"\n          mask=\"url(#error_overlay_nav_path_1_outside_1_2667_14687)\"\n        />\n      </mask>\n      <g mask=\"url(#error_overlay_nav_mask0_2667_14687)\">\n        <mask\n          id=\"error_overlay_nav_path_3_outside_2_2667_14687\"\n          maskUnits=\"userSpaceOnUse\"\n          x=\"-1\"\n          y=\"0.0244141\"\n          width=\"60\"\n          height=\"43\"\n          fill=\"black\"\n        >\n          <rect fill=\"white\" x=\"-1\" y=\"0.0244141\" width=\"60\" height=\"43\" />\n          <path d=\"M0 1.02441H7.0783C14.772 1.02441 21.7836 5.43765 25.111 12.3746L33.8889 30.6743C37.2164 37.6112 44.228 42.0244 51.9217 42.0244H59H0L0 1.02441Z\" />\n        </mask>\n        <path\n          d=\"M0 1.02441H7.0783C14.772 1.02441 21.7836 5.43765 25.111 12.3746L33.8889 30.6743C37.2164 37.6112 44.228 42.0244 51.9217 42.0244H59H0L0 1.02441Z\"\n          fill=\"var(--background-color)\"\n        />\n        <path\n          d=\"M0 1.02441L0 0.0244141H-1V1.02441H0ZM0 42.0244H-1V43.0244H0L0 42.0244ZM33.8889 30.6743L32.9873 31.1068L33.8889 30.6743ZM25.111 12.3746L26.0127 11.9421L25.111 12.3746ZM0 2.02441H7.0783V0.0244141H0L0 2.02441ZM59 41.0244H0L0 43.0244H59V41.0244ZM1 42.0244L1 1.02441H-1L-1 42.0244H1ZM24.2094 12.8071L32.9873 31.1068L34.7906 30.2418L26.0127 11.9421L24.2094 12.8071ZM51.9217 43.0244H59V41.0244H51.9217V43.0244ZM32.9873 31.1068C36.4811 38.3905 43.8433 43.0244 51.9217 43.0244V41.0244C44.6127 41.0244 37.9517 36.8318 34.7906 30.2418L32.9873 31.1068ZM7.0783 2.02441C14.3873 2.02441 21.0483 6.21699 24.2094 12.8071L26.0127 11.9421C22.5188 4.65831 15.1567 0.0244141 7.0783 0.0244141V2.02441Z\"\n          fill=\"var(--stroke-color)\"\n          mask=\"url(#error_overlay_nav_path_3_outside_2_2667_14687)\"\n        />\n      </g>\n    </svg>\n  )\n}\n"], "names": ["ErrorOverlayNav", "styles", "runtimeErrors", "activeIdx", "setActiveIndex", "versionInfo", "bundlerName", "process", "env", "__NEXT_BUNDLER", "div", "data-nextjs-error-overlay-nav", "Notch", "side", "ErrorOverlayPagination", "onActiveIndexChange", "VersionStalenessInfo", "children", "className", "data-side", "Tail", "svg", "width", "height", "viewBox", "fill", "xmlns", "preserveAspectRatio", "mask", "id", "style", "maskType", "maskUnits", "x", "y", "rect", "path", "d", "g"], "mappings": ";;;;;;;;;;;;;;;IAcgBA,eAAe;eAAfA;;IAiCHC,MAAM;eAANA;;;;wCA7C0B;sCACF;AAW9B,SAASD,gBAAgB,KAKT;IALS,IAAA,EAC9BE,aAAa,EACbC,SAAS,EACTC,cAAc,EACdC,WAAW,EACU,GALS;IAM9B,MAAMC,cAAeC,QAAQC,GAAG,CAACC,cAAc,IAAI;IAKnD,qBACE,sBAACC;QAAIC,+BAA6B;;0BAChC,qBAACC;gBAAMC,MAAK;0BAEV,cAAA,qBAACC,8CAAsB;oBACrBZ,eAAeA,wBAAAA,gBAAiB,EAAE;oBAClCC,WAAWA,oBAAAA,YAAa;oBACxBY,qBAAqBX,yBAAAA,iBAAmB,KAAO;;;YAGlDC,6BACC,qBAACO;gBAAMC,MAAK;0BACV,cAAA,qBAACG,0CAAoB;oBACnBX,aAAaA;oBACbC,aAAaA;;;;;AAMzB;AAEO,MAAML,SAAU;AAiEvB,SAASW,MAAM,KAMd;IANc,IAAA,EACbK,QAAQ,EACRJ,OAAO,MAAM,EAId,GANc;IAOb,qBACE,sBAACH;QAAIQ,WAAU;QAAsBC,aAAWN;;YAC7CI;0BACD,qBAACG;;;AAGP;AAEA,SAASA;IACP,qBACE,sBAACC;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;QACNR,WAAU;QACVS,qBAAoB;;0BAEpB,sBAACC;gBACCC,IAAG;gBACHC,OAAO;oBACLC,UAAU;gBACZ;gBACAC,WAAU;gBACVC,GAAE;gBACFC,GAAE;gBACFZ,OAAM;gBACNC,QAAO;;kCAEP,sBAACK;wBACCC,IAAG;wBACHG,WAAU;wBACVC,GAAE;wBACFC,GAAE;wBACFZ,OAAM;wBACNC,QAAO;wBACPE,MAAK;;0CAEL,qBAACU;gCAAKV,MAAK;gCAAQS,GAAE;gCAAKZ,OAAM;gCAAKC,QAAO;;0CAC5C,qBAACa;gCAAKC,GAAE;;;;kCAEV,qBAACD;wBACCC,GAAE;wBACFZ,MAAK;;kCAEP,qBAACW;wBACCC,GAAE;wBACFZ,MAAK;wBACLG,MAAK;;;;0BAGT,sBAACU;gBAAEV,MAAK;;kCACN,sBAACA;wBACCC,IAAG;wBACHG,WAAU;wBACVC,GAAE;wBACFC,GAAE;wBACFZ,OAAM;wBACNC,QAAO;wBACPE,MAAK;;0CAEL,qBAACU;gCAAKV,MAAK;gCAAQQ,GAAE;gCAAKC,GAAE;gCAAYZ,OAAM;gCAAKC,QAAO;;0CAC1D,qBAACa;gCAAKC,GAAE;;;;kCAEV,qBAACD;wBACCC,GAAE;wBACFZ,MAAK;;kCAEP,qBAACW;wBACCC,GAAE;wBACFZ,MAAK;wBACLG,MAAK;;;;;;AAKf"}