"use client";

import { motion } from 'framer-motion';
import { ShieldCheckIcon, StarIcon, LinkIcon, UserGroupIcon } from '@heroicons/react/24/outline';

const WhyParadiseIndonesia = () => {
  const features = [
    {
      icon: ShieldCheckIcon,
      title: "Legal Excellence",
      subtitle: "20+ years of experience in Bali real estate law",
      description: "You offer deep legal knowledge, compliance focus and an unmatched reputation in the local market.",
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: UserGroupIcon,
      title: "International Trust",
      subtitle: "A proven track record with global clients",
      description: "Your name carries weight — with investors, institutions and regulators alike.",
      color: "from-green-500 to-green-600"
    },
    {
      icon: StarIcon,
      title: "Ethical Foundation",
      subtitle: "Independent, client-first and transparent",
      description: "You don't chase commissions. You protect what matters — ownership and legal clarity.",
      color: "from-purple-500 to-purple-600"
    },
    {
      icon: LinkIcon,
      title: "Modern Synergy",
      subtitle: "Your credibility meets our modern capability",
      description: "While you bring the legal trust, Property Plaza adds creative reach, fast onboarding, and a tech-driven user journey.",
      color: "from-primary to-accent"
    }
  ];

  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-primary/5 py-12">
      <div className="max-w-6xl mx-auto px-4 lg:px-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-8"
        >
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6"
          >
            🤝 Strategic Partnership
          </motion.span>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-3xl md:text-4xl font-bold text-default mb-3 leading-tight"
          >
            Why <span className="gradient-text">Paradise Indonesia</span>?
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-lg text-muted mb-6"
          >
            Legal roots meet digital wings
          </motion.p>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-sm lg:text-base text-muted max-w-3xl mx-auto mb-8 leading-relaxed"
          >
            In an industry where most players still rely on offline tactics and broker-driven deals, we're building a partnership that bridges tradition and transformation.
          </motion.p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid lg:grid-cols-2 gap-4 lg:gap-6 mb-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
              viewport={{ once: true }}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-start space-x-3">
                <div className={`w-8 h-8 lg:w-10 lg:h-10 bg-gradient-to-br ${feature.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
                  <feature.icon className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center mb-1">
                    <span className="text-primary text-sm lg:text-base mr-2">🔹</span>
                    <h3 className="text-base lg:text-lg font-bold text-default">{feature.title}</h3>
                  </div>
                  <p className="text-primary font-semibold mb-1 lg:mb-2 text-xs lg:text-sm">
                    {feature.subtitle}
                  </p>
                  <p className="text-muted leading-relaxed text-xs lg:text-sm">
                    {feature.description}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quote Section */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-6 lg:p-8 border border-primary/20 text-center"
        >
          <blockquote className="text-lg lg:text-2xl xl:text-3xl font-bold text-default mb-3 lg:mb-4 leading-relaxed">
            "You bring <span className="gradient-text">trust and structure</span>.
            <br />
            We bring <span className="gradient-text">speed, access and innovation</span>."
          </blockquote>
          <p className="text-sm lg:text-base text-muted max-w-2xl mx-auto">
            Together, we're not just partnering — we're building a new era in Bali real estate.
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyParadiseIndonesia;
