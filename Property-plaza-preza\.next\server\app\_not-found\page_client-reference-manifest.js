globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"51":{"*":{"id":"5617","name":"*","chunks":[],"async":false}},"242":{"*":{"id":"2623","name":"*","chunks":[],"async":false}},"513":{"*":{"id":"5990","name":"*","chunks":[],"async":false}},"647":{"*":{"id":"7179","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1078":{"*":{"id":"6108","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"5656","name":"*","chunks":[],"async":false}},"6898":{"*":{"id":"8931","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"7912":{"*":{"id":"5097","name":"*","chunks":[],"async":false}},"8162":{"*":{"id":"4433","name":"*","chunks":[],"async":false}},"8893":{"*":{"id":"4120","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}},"9765":{"*":{"id":"1790","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\globals.css":{"id":9324,"name":"*","chunks":["177","static/chunks/app/layout-3de86254362878d7.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":8106,"name":"*","chunks":["177","static/chunks/app/layout-3de86254362878d7.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\components\\ScrollIndicator.tsx":{"id":51,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\AboutPropertyPlaza.tsx":{"id":9765,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\FinalCTA.tsx":{"id":513,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\FourPillars.tsx":{"id":242,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Hero.tsx":{"id":647,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\MarketProblem.tsx":{"id":7912,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Metrics.tsx":{"id":6898,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\PilotCampaign.tsx":{"id":8893,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Synergy.tsx":{"id":1078,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\WhyParadiseIndonesia.tsx":{"id":8162,"name":"*","chunks":["717","static/chunks/717-55240362a72b2ba0.js","974","static/chunks/app/page-f3b2334ada3d3ff4.js"],"async":false}},"entryCSSFiles":{"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\":[],"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\layout":[{"inlined":false,"path":"static/css/2f04414ff2773e69.css"}],"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\page":[],"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\_not-found\\page":[]},"rscModuleMapping":{"51":{"*":{"id":"6151","name":"*","chunks":[],"async":false}},"242":{"*":{"id":"3398","name":"*","chunks":[],"async":false}},"513":{"*":{"id":"5331","name":"*","chunks":[],"async":false}},"647":{"*":{"id":"677","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1078":{"*":{"id":"6289","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"8170","name":"*","chunks":[],"async":false}},"6898":{"*":{"id":"2674","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"7912":{"*":{"id":"2492","name":"*","chunks":[],"async":false}},"8162":{"*":{"id":"4056","name":"*","chunks":[],"async":false}},"8893":{"*":{"id":"2117","name":"*","chunks":[],"async":false}},"9324":{"*":{"id":"2704","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}},"9765":{"*":{"id":"1199","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}