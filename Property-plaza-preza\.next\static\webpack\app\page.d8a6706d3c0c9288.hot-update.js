"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/sections/MarketProblem.tsx":
/*!****************************************!*\
  !*** ./app/sections/MarketProblem.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,ExclamationTriangleIcon,ShieldExclamationIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,ExclamationTriangleIcon,ShieldExclamationIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,ExclamationTriangleIcon,ShieldExclamationIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldExclamationIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,ExclamationTriangleIcon,ShieldExclamationIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MarketProblem = ()=>{\n    const problems = [\n        {\n            icon: _barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: \"Legal Confusion\",\n            description: \"Buyers & renters are confused by leasehold vs ownership rules\",\n            color: \"text-red-500\"\n        },\n        {\n            icon: _barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"Limited Access\",\n            description: \"Sellers lack efficient access to foreign buyers & long-term investors/renters\",\n            color: \"text-orange-500\"\n        },\n        {\n            icon: _barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"Trust Issues\",\n            description: \"Trust in the real estate space is critically low & both parties are afraid to get scammed\",\n            color: \"text-yellow-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 border border-muted rounded-lg transform rotate-12\",\n                        animate: {\n                            rotate: [\n                                12,\n                                15,\n                                12\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute bottom-1/4 right-1/4 w-64 h-64 border border-muted rounded-lg transform -rotate-6\",\n                        animate: {\n                            rotate: [\n                                -6,\n                                -9,\n                                -6\n                            ]\n                        },\n                        transition: {\n                            duration: 6,\n                            repeat: Infinity\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-16 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-full text-sm font-semibold\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Market Challenge\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.3\n                                    },\n                                    className: \"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight\",\n                                    children: [\n                                        \"The \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"Problem\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \" We're Solving\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4\n                                    },\n                                    className: \"text-xl text-muted mb-12 leading-relaxed\",\n                                    children: \"Bali's property market is plagued by inefficiency, uncertainty, confusion, and mistrust. Both buyers and sellers struggle in an environment lacking transparency.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: problems.map((problem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.5 + index * 0.1\n                                            },\n                                            className: \"flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 \".concat(problem.color, \" bg-gray-50 rounded-lg flex items-center justify-center\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(problem.icon, {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-default mb-2\",\n                                                            children: problem.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted\",\n                                                            children: problem.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        [\n                                            ...Array(3)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute inset-0 bg-white border border-gray-200 rounded-lg shadow-lg\",\n                                                style: {\n                                                    transform: \"translate(\".concat(i * 8, \"px, \").concat(i * 8, \"px) rotate(\").concat(i * 2, \"deg)\"),\n                                                    zIndex: 3 - i\n                                                },\n                                                animate: {\n                                                    rotate: [\n                                                        i * 2,\n                                                        i * 2 + 2,\n                                                        i * 2\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 4 + i,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, i, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 bg-white border-2 border-red-200 rounded-lg p-8 h-96\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            className: \"w-8 h-8 text-red-500 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-default\",\n                                                            children: \"Legal Document\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 rounded animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 rounded animate-pulse w-3/4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-red-200 rounded animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 rounded animate-pulse w-1/2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-8 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-red-500 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                                            lineNumber: 148,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                                            lineNumber: 148,\n                                                                            columnNumber: 88\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-700 font-semibold text-sm\",\n                                                                            children: \"UNCLEAR TERMS\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                                            lineNumber: 149,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_ExclamationTriangleIcon_ShieldExclamationIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-red-500 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                                            lineNumber: 150,\n                                                                            columnNumber: 59\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-700 font-semibold text-sm\",\n                                                                            children: \"EFFECTS OF NON-COMPLIANCE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                                            lineNumber: 151,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 24\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                [\n                                    ...Array(3)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: \"absolute w-8 h-8 bg-red-100 text-red-500 rounded-full flex items-center justify-center font-bold text-lg\",\n                                        style: {\n                                            top: \"\".concat(20 + i * 25, \"%\"),\n                                            right: \"\".concat(-10 + i * 5, \"%\")\n                                        },\n                                        animate: {\n                                            y: [\n                                                0,\n                                                -10,\n                                                0\n                                            ],\n                                            rotate: [\n                                                0,\n                                                5,\n                                                0\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2 + i * 0.5,\n                                            repeat: Infinity,\n                                            delay: i * 0.3\n                                        },\n                                        children: \"?\"\n                                    }, i, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MarketProblem;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MarketProblem);\nvar _c;\n$RefreshReg$(_c, \"MarketProblem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/sections/MarketProblem.tsx\n"));

/***/ })

});