{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "b38f1249ec55c7b3dd67c75e89c1405e", "previewModeSigningKey": "80358a6f102042849b1fe9ba704e817091aef19fdbdf7066a7dd9981bdc9ccc1", "previewModeEncryptionKey": "90a06769dbb6c311ba58091b887ffe31bf048a0983777a4e52240b23048def36"}}