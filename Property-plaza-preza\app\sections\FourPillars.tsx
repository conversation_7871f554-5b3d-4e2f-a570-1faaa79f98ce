"use client";

import { motion } from 'framer-motion';
import { EyeIcon, AcademicCapIcon, LinkIcon, BoltIcon } from '@heroicons/react/24/outline';

const FourPillars = () => {
  const pillars = [
    {
      icon: EyeIcon,
      title: "Transparency",
      subtitle: "No surprises, clear legal & price structure",
      description: "Every property listing includes complete legal documentation, clear ownership structure, and transparent pricing with no hidden fees.",
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      textColor: "text-blue-600"
    },
    {
      icon: AcademicCapIcon,
      title: "Knowledge",
      subtitle: "Guidance in your language",
      description: "Expert guidance provided in Dutch, German, and English, ensuring you understand every aspect of your property investment.",
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      textColor: "text-green-600"
    },
    {
      icon: LinkIcon,
      title: "Connection",
      subtitle: "We link buyers, sellers & experts directly",
      description: "Direct connections between serious buyers, verified sellers, and trusted legal experts, eliminating unnecessary intermediaries.",
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      textColor: "text-purple-600"
    },
    {
      icon: BoltIcon,
      title: "Empowerment",
      subtitle: "When people understand, they act confidently",
      description: "Armed with complete information and expert guidance, you can make property decisions with complete confidence.",
      color: "from-primary to-accent",
      bgColor: "bg-primary/5",
      textColor: "text-primary"
    }
  ];

  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-background py-20">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6"
          >
            Our Foundation
          </motion.span>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl md:text-5xl font-bold text-default mb-6"
          >
            The <span className="gradient-text">Four Pillars</span> of Trust
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl text-muted max-w-3xl mx-auto"
          >
            Our mission is built on four fundamental principles that transform how property decisions are made in Bali.
          </motion.p>
        </motion.div>

        {/* Pillars Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {pillars.map((pillar, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="group cursor-pointer"
            >
              <div className={`${pillar.bgColor} rounded-2xl p-8 h-full border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-xl`}>
                {/* Icon */}
                <motion.div
                  className={`w-16 h-16 bg-gradient-to-br ${pillar.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}
                >
                  <pillar.icon className="w-8 h-8 text-white" />
                </motion.div>

                {/* Content */}
                <h3 className={`text-2xl font-bold ${pillar.textColor} mb-3`}>
                  {pillar.title}
                </h3>
                
                <p className="text-default font-semibold mb-4 text-sm">
                  {pillar.subtitle}
                </p>
                
                <p className="text-muted leading-relaxed text-sm">
                  {pillar.description}
                </p>

                {/* Hover Effect */}
                <motion.div
                  className={`mt-6 w-full h-1 bg-gradient-to-r ${pillar.color} rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                />
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="inline-flex items-center space-x-2 text-muted">
            <div className="flex space-x-1">
              {pillars.map((_, index) => (
                <motion.div
                  key={index}
                  className="w-2 h-2 bg-primary rounded-full"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, delay: index * 0.2, repeat: Infinity }}
                />
              ))}
            </div>
            <span className="text-sm font-medium">Building trust through action</span>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FourPillars;
