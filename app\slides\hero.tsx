"use client";

import { motion } from "framer-motion";

export default function Hero() {
  return (
    <section className="h-screen flex items-center justify-center text-center" style={{backgroundColor: '#f7f1eb'}}>
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <h1 className="text-4xl md:text-6xl font-bold text-primary">Empower Property Decisions in Bali</h1>
        <p className="mt-4 text-muted text-lg">Transparency. Knowledge. Connection. Empowerment.</p>
        <button className="mt-6 px-6 py-3 bg-primary text-white rounded-full hover:bg-accent transition">
          Start the Experience
        </button>
      </motion.div>
    </section>
  );
}
