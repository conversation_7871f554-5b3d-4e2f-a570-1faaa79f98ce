"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/sections/WhyParadiseIndonesia.tsx":
/*!***********************************************!*\
  !*** ./app/sections/WhyParadiseIndonesia.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_LinkIcon_ShieldCheckIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=LinkIcon,ShieldCheckIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_LinkIcon_ShieldCheckIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=LinkIcon,ShieldCheckIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_LinkIcon_ShieldCheckIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=LinkIcon,ShieldCheckIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_LinkIcon_ShieldCheckIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=LinkIcon,ShieldCheckIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LinkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst WhyParadiseIndonesia = ()=>{\n    const features = [\n        {\n            icon: _barrel_optimize_names_LinkIcon_ShieldCheckIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: \"Legal Excellence\",\n            subtitle: \"20+ years of experience in Bali real estate law\",\n            description: \"You offer deep legal knowledge, compliance focus and an unmatched reputation in the local market.\",\n            color: \"from-blue-500 to-blue-600\"\n        },\n        {\n            icon: _barrel_optimize_names_LinkIcon_ShieldCheckIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"International Trust\",\n            subtitle: \"A proven track record with global clients\",\n            description: \"Your name carries weight — with investors, institutions and regulators alike.\",\n            color: \"from-green-500 to-green-600\"\n        },\n        {\n            icon: _barrel_optimize_names_LinkIcon_ShieldCheckIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"Ethical Foundation\",\n            subtitle: \"Independent, client-first and transparent\",\n            description: \"You don't chase commissions. You protect what matters — ownership and legal clarity.\",\n            color: \"from-purple-500 to-purple-600\"\n        },\n        {\n            icon: _barrel_optimize_names_LinkIcon_ShieldCheckIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Modern Synergy\",\n            subtitle: \"Your credibility meets our modern capability\",\n            description: \"While you bring the legal trust, Property Plaza adds creative reach, fast onboarding, and a tech-driven user journey.\",\n            color: \"from-primary to-accent\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-primary/5 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 lg:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            className: \"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6\",\n                            children: \"\\uD83E\\uDD1D Strategic Partnership\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            className: \"text-3xl md:text-4xl font-bold text-default mb-3 leading-tight\",\n                            children: [\n                                \"Why \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Paradise Indonesia\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, undefined),\n                                \"?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            className: \"text-lg text-muted mb-6\",\n                            children: \"Legal roots meet digital wings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.5\n                            },\n                            className: \"text-sm lg:text-base text-muted max-w-3xl mx-auto mb-8 leading-relaxed\",\n                            children: \"In an industry where most players still rely on offline tactics and broker-driven deals, we're building a partnership that bridges tradition and transformation.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-6 lg:gap-8 mb-12\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1 * index\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white rounded-xl p-6 lg:p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br \".concat(feature.color, \" rounded-lg flex items-center justify-center flex-shrink-0\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                            className: \"w-5 h-5 lg:w-6 lg:h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-primary text-base lg:text-lg mr-2\",\n                                                        children: \"\\uD83D\\uDD39\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg lg:text-xl font-bold text-default\",\n                                                        children: feature.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary font-semibold mb-2 lg:mb-3 text-xs lg:text-sm\",\n                                                children: feature.subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted leading-relaxed text-sm lg:text-base\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, undefined)\n                        }, index, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.9\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-8 lg:p-12 border border-primary/20 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                            className: \"text-2xl lg:text-3xl xl:text-4xl font-bold text-default mb-4 lg:mb-6 leading-relaxed\",\n                            children: [\n                                '\"You bring ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"trust and structure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 24\n                                }, undefined),\n                                \".\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"We bring \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"speed, access and innovation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 22\n                                }, undefined),\n                                '.\"'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-base lg:text-lg text-muted max-w-2xl lg:max-w-3xl mx-auto\",\n                            children: \"Together, we're not just partnering — we're building a new era in Bali real estate.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_c = WhyParadiseIndonesia;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhyParadiseIndonesia);\nvar _c;\n$RefreshReg$(_c, \"WhyParadiseIndonesia\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/sections/WhyParadiseIndonesia.tsx\n"));

/***/ })

});