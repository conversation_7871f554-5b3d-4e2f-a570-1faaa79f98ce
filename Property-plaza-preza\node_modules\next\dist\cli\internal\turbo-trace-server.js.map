{"version": 3, "sources": ["../../../src/cli/internal/turbo-trace-server.ts"], "sourcesContent": ["import { loadBindings } from '../../build/swc'\n\nexport async function startTurboTraceServerCli(file: string) {\n  let bindings = await loadBindings()\n  bindings.turbo.startTurbopackTraceServer(file)\n}\n"], "names": ["startTurboTraceServerCli", "file", "bindings", "loadBindings", "turbo", "startTurbopackTraceServer"], "mappings": ";;;;+BAEsBA;;;eAAAA;;;qBAFO;AAEtB,eAAeA,yBAAyBC,IAAY;IACzD,IAAIC,WAAW,MAAMC,IAAAA,iBAAY;IACjCD,SAASE,KAAK,CAACC,yBAAyB,CAACJ;AAC3C"}