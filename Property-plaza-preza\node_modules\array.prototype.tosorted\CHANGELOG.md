# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.4](https://github.com/es-shims/Array.prototype.toSorted/compare/v1.1.3...v1.1.4) - 2024-06-02

### Fixed

- [Refactor] update implementation to match latest spec [`#3`](https://github.com/es-shims/Array.prototype.toSorted/issues/3)

### Commits

- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `hasown`, `tape` [`159d118`](https://github.com/es-shims/Array.prototype.toSorted/commit/159d11848629e0468f9f10a1e36001f937c0c6ea)
- [Deps] update `call-bind`, `es-abstract`, `es-errors` [`7d32e67`](https://github.com/es-shims/Array.prototype.toSorted/commit/7d32e67417f436d5e8a66bf8286b9ead9296c4f1)
- [meta] add missing `engines.node` [`b49466b`](https://github.com/es-shims/Array.prototype.toSorted/commit/b49466b473d116b5a53209491163bfd2c3aa89bc)

## [v1.1.3](https://github.com/es-shims/Array.prototype.toSorted/compare/v1.1.2...v1.1.3) - 2024-02-04

### Commits

- [Refactor] use `es-errors` where possible, so things that only need those do not need `get-intrinsic` [`6c089a5`](https://github.com/es-shims/Array.prototype.toSorted/commit/6c089a5712a25008daa5d3d9dbc9d4fa7c908a64)
- [Deps] update `call-bind`, `define-properties`, `es-abstract`, `es-shim-unscopables`, `get-intrinsic` [`d096b56`](https://github.com/es-shims/Array.prototype.toSorted/commit/d096b56d30bb5f5b09649f285eee21e22e2db8f9)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`43cea6d`](https://github.com/es-shims/Array.prototype.toSorted/commit/43cea6dfaa05669f6a60bef860f2d6fbf86f167e)
- [Dev Deps] use `hasown` instead of `has` [`cefe5dc`](https://github.com/es-shims/Array.prototype.toSorted/commit/cefe5dc1bbc2699ca03c3e4ae9ce7fc696a76ca8)
- [Dev Deps] update `tape` [`ad8446e`](https://github.com/es-shims/Array.prototype.toSorted/commit/ad8446ecbca6f724458af81da3aa3ffcd3552825)

## [v1.1.2](https://github.com/es-shims/Array.prototype.toSorted/compare/v1.1.1...v1.1.2) - 2023-09-07

### Commits

- [actions] use reusable workflows [`b3d4f1d`](https://github.com/es-shims/Array.prototype.toSorted/commit/b3d4f1dff2d0fcd30e5bb7591836f5c5133ef9be)
- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`35ce967`](https://github.com/es-shims/Array.prototype.toSorted/commit/35ce96705f9201f9a416629446c7f8b739a3d0a7)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`1a17c6a`](https://github.com/es-shims/Array.prototype.toSorted/commit/1a17c6a3492fbbb99f3543c70036c02e981d974a)

## [v1.1.1](https://github.com/es-shims/Array.prototype.toSorted/compare/v1.1.0...v1.1.1) - 2022-11-03

### Commits

- [Deps] update `es-abstract`, `get-intrinsic` [`b10a2a3`](https://github.com/es-shims/Array.prototype.toSorted/commit/b10a2a30772369ed3640741345225799af108e97)
- [actions] update rebase action to use reusable workflow [`8f49d78`](https://github.com/es-shims/Array.prototype.toSorted/commit/8f49d78ac5d679c052d544a7051c3b8e5c449052)
- [Dev Deps] update `aud`, `tape` [`2d2741b`](https://github.com/es-shims/Array.prototype.toSorted/commit/2d2741b6a0e08d1b2dbe675759f33dc3db4924a2)

## [v1.1.0](https://github.com/es-shims/Array.prototype.toSorted/compare/v1.0.0...v1.1.0) - 2022-08-14

### Commits

- [Tests] add coverage from https://github.com/tc39/test262/pull/3464 [`2172830`](https://github.com/es-shims/Array.prototype.toSorted/commit/21728306e552c80868753b0147dc5637e57ffd2b)
- [meta] use `npmignore` to autogenerate an npmignore file [`972f761`](https://github.com/es-shims/Array.prototype.toSorted/commit/972f761599aaf97049a005974caa2d9b24581119)
- [New] `shim`/`auto`: add `toSorted` to `Symbol.unscopables` [`2ad9bad`](https://github.com/es-shims/Array.prototype.toSorted/commit/2ad9bad51ab7d2e7cc579f6681809fe495682163)
- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`e7b229d`](https://github.com/es-shims/Array.prototype.toSorted/commit/e7b229dbb0c199661f785dfa0d5403b81ed7811e)
- [Dev Deps] update `@ljharb/eslint-config`, `functions-have-names`, `tape` [`2bccb92`](https://github.com/es-shims/Array.prototype.toSorted/commit/2bccb92d5314e3b86bb3ffc1144f0c86cdca285a)
- [readme] fix link to spec [`0d024e6`](https://github.com/es-shims/Array.prototype.toSorted/commit/0d024e68e3d41b3ec8dbc8aa47e99d8987c91fea)

## v1.0.0 - 2022-03-31

### Commits

- initial implementation, tests, readme [`a2882bf`](https://github.com/es-shims/Array.prototype.toSorted/commit/a2882bf9f2a5d0533450a37df13ca3c1b8178bef)
- Initial commit [`f9d05c4`](https://github.com/es-shims/Array.prototype.toSorted/commit/f9d05c4275eeeb841f357c487606cf7c83235651)
- [meta] do not publish workflow files [`ceed80a`](https://github.com/es-shims/Array.prototype.toSorted/commit/ceed80acc95688c872dd8c69292a30589a8a9020)
- [Tests] temporarily use actions instead of composable workflows [`496789b`](https://github.com/es-shims/Array.prototype.toSorted/commit/496789bbfb7da7e2b2cac3398491e6b58b1f169f)
- npm init [`9405760`](https://github.com/es-shims/Array.prototype.toSorted/commit/9405760c2f52001035087a0d60f4e06465e82546)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`474180c`](https://github.com/es-shims/Array.prototype.toSorted/commit/474180c1250ec0a0ffabc80aa2733fe4abe65036)
- [meta] add version script [`49a3b80`](https://github.com/es-shims/Array.prototype.toSorted/commit/49a3b802020c54ead862d49365555a67ac786636)
- Only apps should have lockfiles [`0db3a5a`](https://github.com/es-shims/Array.prototype.toSorted/commit/0db3a5a7607be2a7d11fa78ae9c43907e59bdf92)
- [Deps] update `es-abstract` [`35de1c5`](https://github.com/es-shims/Array.prototype.toSorted/commit/35de1c532245469b50bd7296ca8c19470385c622)
