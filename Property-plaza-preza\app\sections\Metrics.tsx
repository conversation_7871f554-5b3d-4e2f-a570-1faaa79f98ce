'use client';

import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  ArrowTrendingUpIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

const Metrics = () => {
  const metrics = [
    {
      icon: <ArrowTrendingUpIcon className="w-8 h-8" />,
      title: "Reach Growth",
      value: "5K+",
      description: "Average reach per post",
      trend: "+45%"
    },
    {
      icon: <UserGroupIcon className="w-8 h-8" />,
      title: "Engagement",
      value: "400+",
      description: "Total engagements",
      trend: "+60%"
    },
    {
      icon: <EyeIcon className="w-8 h-8" />,
      title: "Qualified Leads",
      value: "75+",
      description: "Qualified inquiries",
      trend: "+80%"
    }
  ];



  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-gray-50 py-20">
      <div className="max-w-7xl mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6"
          >
            Results & Timeline
          </motion.span>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl md:text-5xl font-bold text-default mb-6 leading-tight"
          >
            <span className="gradient-text">Campaign Results</span> & Timeline
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl text-muted max-w-3xl mx-auto"
          >
            Measurable outcomes and structured timeline for the 12-week campaign with comprehensive analytics tracking.
          </motion.p>
        </motion.div>



        {/* Main Content Grid - Results & Timeline Side by Side */}
        <div className="grid lg:grid-cols-3 gap-8 items-stretch">
          {/* Expected Results - 1/3 width */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="lg:col-span-1 flex"
          >
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6 w-full flex flex-col">
              <h3 className="text-xl font-bold text-default mb-6 text-center">Expected Campaign Results</h3>

              <div className="space-y-6 flex-1 flex flex-col justify-center">
                {metrics.map((metric, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
                    className="text-center p-4 bg-gradient-to-br from-gray-50 to-white rounded-xl border border-gray-100"
                  >
                    <div className="text-primary mb-3 flex justify-center">
                      {metric.icon}
                    </div>
                    <div className="text-2xl font-bold text-default mb-1">{metric.value}</div>
                    <div className="text-xs text-muted mb-2">{metric.description}</div>
                    <div className="text-xs font-semibold text-green-600">{metric.trend}</div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Timeline Section - 2/3 width */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="lg:col-span-2 flex"
          >
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6 w-full flex flex-col">
              <h3 className="text-xl font-bold text-default mb-6 text-center">12-Week Campaign Timeline</h3>

              <div className="space-y-6 flex-1">
            {/* Week 1-4 */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="flex items-start"
            >
              <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm">
                1-4
              </div>
              <div className="flex-1">
                <h4 className="text-xl font-semibold text-default mb-3">Legal & Process Foundation</h4>
                <p className="text-muted mb-4 leading-relaxed">
                  Establish credibility through legal expertise and process transparency. Build trust with potential investors through detailed legal guidance.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Licensing & Permits</span>
                  <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Transfer Requirements</span>
                  <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Legal Risks</span>
                  <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Trust Building</span>
                </div>
              </div>
            </motion.div>

            {/* Week 5-8 */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex items-start"
            >
              <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-accent to-primary rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm">
                5-8
              </div>
              <div className="flex-1">
                <h4 className="text-xl font-semibold text-default mb-3">Material & Build Quality</h4>
                <p className="text-muted mb-4 leading-relaxed">
                  Deep-dive into construction quality, materials, and cost breakdowns. Showcase expertise in sustainable building practices.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">Timber vs Concrete</span>
                  <span className="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">Roofing Systems</span>
                  <span className="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">Energy Efficiency</span>
                  <span className="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">Finishes</span>
                </div>
              </div>
            </motion.div>

            {/* Week 9-12 */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="flex items-start"
            >
              <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm">
                9-12
              </div>
              <div className="flex-1">
                <h4 className="text-xl font-semibold text-default mb-3">ROI & Trust Network</h4>
                <p className="text-muted mb-4 leading-relaxed">
                  Focus on investment returns and local partnership network. Demonstrate proven track record and investor success stories.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Rental Yield Growth</span>
                  <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Case Examples</span>
                  <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Short vs Long-term Rentals</span>
                  <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Investor Profiles</span>
                </div>
              </div>
            </motion.div>
          </div>


            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Metrics;
