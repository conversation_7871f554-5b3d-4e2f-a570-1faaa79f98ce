'use client';

import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  ArrowTrendingUpIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

const Metrics = () => {
  const metrics = [
    {
      icon: <ArrowTrendingUpIcon className="w-8 h-8" />,
      title: "Reach Growth",
      value: "5K+",
      description: "Average reach per post",
      trend: ""
    },
    {
      icon: <UserGroupIcon className="w-8 h-8" />,
      title: "Engagement",
      value: "400+",
      description: "Total engagements",
      trend: ""
    },
    {
      icon: <EyeIcon className="w-8 h-8" />,
      title: "Qualified Leads",
      value: "75+",
      description: "Qualified inquiries",
      trend: ""
    }
  ];

  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-gray-50 py-12 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12 sm:mb-16"
        >
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-3 sm:px-4 py-2 bg-primary/10 text-primary rounded-full text-xs sm:text-sm font-semibold mb-4 sm:mb-6"
          >
            Results & Timeline
          </motion.span>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-3xl sm:text-4xl md:text-5xl font-bold text-default mb-4 sm:mb-6 leading-tight"
          >
            <span className="gradient-text">Campaign Results</span> & Timeline
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-lg sm:text-xl text-muted max-w-3xl mx-auto px-4"
          >
            Measurable outcomes and structured timeline for the 12-week campaign.
          </motion.p>
        </motion.div>

        {/* Main Content Grid - Timeline & Results Side by Side */}
        <div className="grid lg:grid-cols-3 gap-6 sm:gap-8 items-stretch">
          {/* Timeline Section - 2/3 width */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="lg:col-span-2"
          >
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 h-full">
              <h3 className="text-2xl font-bold text-default mb-8 text-center">12-Week Campaign Timeline</h3>
              
              <div className="space-y-8">
                {/* Week 1-4 */}
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="flex items-start"
                >
                  <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm">
                    1-4
                  </div>
                  <div className="flex-1">
                    <h4 className="text-xl font-semibold text-default mb-3">Legal & Process Foundation</h4>
                    <p className="text-muted mb-4 leading-relaxed">
                      Establish credibility with investors through clear leasehold terms, developer-issued contracts, and legal due diligence.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Leasehold Duration (50+ years)</span>
                      <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Developer-issued</span>
                      <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Licensing & Permits</span>
                      <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Trust Building</span>
                    </div>
                  </div>
                </motion.div>

                {/* Week 5-8 */}
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="flex items-start"
                >
                  <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-accent to-primary rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm">
                    5-8
                  </div>
                  <div className="flex-1">
                    <h4 className="text-xl font-semibold text-default mb-3">Build Quality & Material Focus</h4>
                    <p className="text-muted mb-4 leading-relaxed">
                      Showcase construction standards, material choices, and quality assurance processes that differentiate premium developments.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">Concrete vs Timber</span>
                      <span className="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">Roofing Solutions</span>
                      <span className="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">Energy Efficiency</span>
                      <span className="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">Finishes</span>
                    </div>
                  </div>
                </motion.div>

                {/* Week 9-12 */}
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="flex items-start"
                >
                  <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm">
                    9-12
                  </div>
                  <div className="flex-1">
                    <h4 className="text-xl font-semibold text-default mb-3">ROI & Investment Strategy</h4>
                    <p className="text-muted mb-4 leading-relaxed">
                      Present concrete ROI data, rental yields, and investment strategies with real case studies and market analysis.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">Rental Yields</span>
                      <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">Capital Growth</span>
                      <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">Case Studies</span>
                      <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">Market Analysis</span>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Results Section - 1/3 width */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="lg:col-span-1"
          >
            <div className="bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10 h-full">
              <h3 className="text-2xl font-bold text-default mb-8 text-center">Expected Results</h3>
              
              <div className="space-y-6">
                {metrics.map((metric, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 * index }}
                    className="text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100"
                  >
                    <div className="flex justify-center mb-4 text-primary">
                      {metric.icon}
                    </div>
                    <div className="text-3xl font-bold text-default mb-2">
                      {metric.value}
                    </div>
                    <div className="text-sm font-semibold text-primary mb-2">
                      {metric.title}
                    </div>
                    <div className="text-xs text-muted">
                      {metric.description}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Call to Action */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="mt-8 text-center"
              >
                <div className="bg-white rounded-xl p-6 border border-primary/20">
                  <h4 className="text-lg font-semibold text-default mb-3">Ready to Launch?</h4>
                  <p className="text-sm text-muted mb-4">
                    Let's start building trust and driving qualified leads together.
                  </p>
                  <div className="inline-flex items-center text-primary text-sm font-semibold">
                    <span>12 weeks to measurable results</span>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Metrics;
