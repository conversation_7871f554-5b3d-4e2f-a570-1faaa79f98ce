"use client";

import { motion } from 'framer-motion';
import { ChartBarIcon, UserGroupIcon, ChatBubbleLeftRightIcon, EyeIcon } from '@heroicons/react/24/outline';

const Metrics = () => {
  const kpis = [
    {
      icon: UserGroupIcon,
      title: "Leads Generated",
      target: "50+",
      description: "Qualified property inquiries"
    },
    {
      icon: ChatBubbleLeftRightIcon,
      title: "WhatsApp Inquiries",
      target: "25+",
      description: "Direct consultation requests"
    },
    {
      icon: EyeIcon,
      title: "Views & Impressions",
      target: "10K+",
      description: "Total content reach"
    },
    {
      icon: ChartBarIcon,
      title: "Audience Growth",
      target: "15%",
      description: "Instagram/Facebook followers"
    }
  ];

  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-gray-50 py-20">
      <div className="max-w-7xl mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6"
          >
            Success Metrics
          </motion.span>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl md:text-5xl font-bold text-default mb-6 leading-tight"
          >
            <span className="gradient-text">Metrics</span> & KPIs
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl text-muted max-w-3xl mx-auto"
          >
            Tracked via Google Data Studio for complete transparency and real-time insights.
          </motion.p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - KPIs */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="grid grid-cols-2 gap-6">
              {kpis.map((kpi, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                  className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4">
                    <kpi.icon className="w-6 h-6 text-white" />
                  </div>
                  
                  <div className="text-3xl font-bold text-primary mb-2">{kpi.target}</div>
                  <h3 className="text-lg font-semibold text-default mb-2">{kpi.title}</h3>
                  <p className="text-sm text-muted">{kpi.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Right Side - Dashboard Mockup */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
              {/* Dashboard Header */}
              <div className="bg-gradient-to-r from-primary to-accent p-6 text-white">
                <h3 className="text-xl font-bold mb-2">Campaign Analytics Dashboard</h3>
                <p className="text-sm opacity-90">Real-time performance tracking</p>
              </div>

              {/* Dashboard Content */}
              <div className="p-6">
                {/* Chart Area */}
                <div className="bg-gray-50 rounded-lg h-32 mb-6 flex items-center justify-center">
                  <div className="text-center text-muted">
                    <ChartBarIcon className="w-8 h-8 mx-auto mb-2" />
                    <span className="text-sm">Google Data Studio Integration</span>
                  </div>
                </div>

                {/* Metrics Grid */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">↗ 24%</div>
                    <div className="text-sm text-green-700">Engagement Rate</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">8.2K</div>
                    <div className="text-sm text-blue-700">Total Reach</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">42</div>
                    <div className="text-sm text-purple-700">New Leads</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">18</div>
                    <div className="text-sm text-orange-700">WhatsApp Chats</div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Metrics;
