"use client";

import { motion } from 'framer-motion';
import { CalendarIcon, PencilIcon, CurrencyDollarIcon, ChartBarIcon } from '@heroicons/react/24/outline';

const PilotCampaign = () => {
  const campaignDetails = [
    {
      icon: CalendarIcon,
      title: "4 Week Duration",
      description: "Focused pilot to measure impact and ROI"
    },
    {
      icon: PencilIcon,
      title: "5 Posts Per Week",
      description: "High-quality content across all platforms"
    },
    {
      icon: CurrencyDollarIcon,
      title: "±1.5M IDR/Week",
      description: "Professional editor + posting budget"
    },
    {
      icon: ChartBarIcon,
      title: "Full Analytics",
      description: "Google Data Studio tracking & reporting"
    }
  ];

  const contentTopics = [
    "Leasehold vs Ownership Explained",
    "Avoiding Legal Pitfalls in Bali",
    "What Dutch Buyers Should Know",
    "German Investment Guidelines",
    "Property Due Diligence Checklist"
  ];

  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-20">
      <div className="max-w-7xl mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6"
          >
            Pilot Campaign
          </motion.span>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl md:text-5xl font-bold text-default mb-6 leading-tight"
          >
            <span className="gradient-text">4-Week</span> Pilot Campaign Plan
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl text-muted max-w-3xl mx-auto"
          >
            A focused campaign to establish our partnership and measure real market impact.
          </motion.p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Left Side - Campaign Details */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold text-default mb-8">Campaign Structure</h3>
            
            <div className="grid grid-cols-2 gap-6 mb-12">
              {campaignDetails.map((detail, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                  viewport={{ once: true }}
                  className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4">
                    <detail.icon className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-lg font-semibold text-default mb-2">{detail.title}</h4>
                  <p className="text-sm text-muted">{detail.description}</p>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-6 border border-primary/20"
            >
              <h4 className="text-lg font-semibold text-default mb-4">Content Topics</h4>
              <ul className="space-y-3">
                {contentTopics.map((topic, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                    className="flex items-center text-muted"
                  >
                    <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                    {topic}
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </motion.div>

          {/* Right Side - Social Media Mockups */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold text-default mb-8">Social Media Preview</h3>
            
            {/* Instagram Post Mockup */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-6">
              <div className="bg-gradient-to-r from-primary to-accent h-48 flex items-center justify-center">
                <div className="text-center text-white">
                  <h4 className="text-xl font-bold mb-2">Leasehold Explained</h4>
                  <p className="text-sm opacity-90">What every foreign buyer needs to know</p>
                </div>
              </div>
              <div className="p-4">
                <div className="flex items-center mb-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full mr-3"></div>
                  <span className="font-semibold text-sm">paradise_indonesia</span>
                </div>
                <p className="text-sm text-muted">
                  Understanding leasehold vs freehold in Bali property investment. 
                  Our expert guide breaks down the legal framework... 
                  <span className="text-primary">#BaliProperty #LegalAdvice</span>
                </p>
              </div>
            </div>

            {/* Engagement Metrics */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-white rounded-xl p-6 shadow-lg border border-gray-100"
            >
              <h4 className="text-lg font-semibold text-default mb-4">Expected Metrics</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-primary">2K+</div>
                  <div className="text-sm text-muted">Reach per post</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-primary">150+</div>
                  <div className="text-sm text-muted">Engagements</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-primary">25+</div>
                  <div className="text-sm text-muted">Inquiries</div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default PilotCampaign;
