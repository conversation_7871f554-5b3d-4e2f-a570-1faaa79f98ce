"use client";

import { motion } from 'framer-motion';

const Synergy = () => {
  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background py-20">
      <div className="max-w-6xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl md:text-5xl font-bold text-default mb-12 leading-tight"
          >
            Together We Offer <span className="gradient-text">Certainty</span>
            <br />
            In An Uncertain Market
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            {/* Logo Merge Animation */}
            <div className="flex items-center justify-center space-x-8 mb-12">
              <motion.div
                className="w-32 h-32 bg-gradient-to-br from-primary to-accent rounded-2xl flex items-center justify-center shadow-xl"
                whileHover={{ scale: 1.1, rotate: 5 }}
              >
                <span className="text-white font-bold text-2xl">PP</span>
              </motion.div>
              
              <motion.div
                className="text-4xl text-primary"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                +
              </motion.div>
              
              <motion.div
                className="w-32 h-32 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl"
                whileHover={{ scale: 1.1, rotate: -5 }}
              >
                <span className="text-white font-bold text-2xl">PI</span>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="text-xl text-muted max-w-3xl mx-auto"
            >
              When Property Plaza's innovative technology meets Paradise Indonesia's trusted expertise, 
              we create an unmatched foundation for confident property decisions in Bali.
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Synergy;
