"use client";

import { motion } from 'framer-motion';
import { ChartBarIcon, GlobeAltIcon, UserGroupIcon, ClockIcon } from '@heroicons/react/24/outline';

const AboutPropertyPlaza = () => {
  const stats = [
    {
      icon: ClockIcon,
      number: "30",
      label: "Days Since Launch",
      description: "Early traction with zero paid marketing"
    },
    {
      icon: GlobeAltIcon,
      number: "30+",
      label: "Active Listings",
      description: "Verified and growing across key Bali regions"
    },
    {
      icon: UserGroupIcon,
      number: "350+",
      label: "Platform Visitors",
      description: "Growing international interest"
    },
    {
      icon: ChartBarIcon,
      number: "3",
      label: "Languages Supported",
      description: "Bahasa Indonesia, Dutch & English — with more to come"
    }
  ];

  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-20">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.span
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6"
            >
              About Us
            </motion.span>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-4xl md:text-5xl font-bold text-default mb-6 leading-tight"
            >
              <span className="gradient-text">Property Plaza</span>
              <br />
              Early Success Story
            </motion.h2>

<motion.div
  initial={{ opacity: 0, y: 20 }}
  whileInView={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.6, delay: 0.4 }}
  className="space-y-5 text-muted text-lg md:text-xl leading-relaxed mb-8 max-w-3xl"
>
  <p>
    In just <strong>30 days live</strong>, Property Plaza has attracted international interest — 
    with zero paid ads, agencies or influencer campaigns.
  </p>

  <p>
    Driven by a mission to remove confusion and risk from Bali's housing market, 
    we introduced a <strong>dual-platform approach</strong>: one site for global seekers 
    (<code>.com</code>) and one for local property owners (<code>.id</code>).
  </p>

  <p>
    The early response makes it clear: there’s strong demand for <strong>transparent, 
    direct alternatives</strong> to traditional broker models — and users are finding us organically.
  </p>
</motion.div>


            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-white rounded-xl p-6 shadow-lg border border-gray-100"
            >
              <h3 className="text-lg font-semibold text-default mb-4">Platform Features</h3>
              <ul className="space-y-3">
                <li className="flex items-center text-muted">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  Multilingual property descriptions (NL/DE/EN)
                </li>
                <li className="flex items-center text-muted">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  Dual-platform structure for local and global access
                </li>
                <li className="flex items-center text-muted">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  Transparent ownership and pricing model
                </li>
                <li className="flex items-center text-muted">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  Direct owner–seeker contact, no broker fees </li>
              </ul>
            </motion.div>
          </motion.div>

          {/* Right Side - Stats */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 gap-6"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
                className="bg-white rounded-xl p-6 text-center shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                
                <motion.div
                  className="text-3xl font-bold text-primary mb-2"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                >
                  {stat.number}
                </motion.div>
                
                <h3 className="text-lg font-semibold text-default mb-2">{stat.label}</h3>
                <p className="text-sm text-muted">{stat.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default AboutPropertyPlaza;
