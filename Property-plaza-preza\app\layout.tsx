import './globals.css'
import { Inter } from 'next/font/google'

const inter = Inter({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800'],
  display: 'swap',
})

export const metadata = {
  title: 'Property Plaza × Paradise Indonesia | Strategic Partnership Proposal',
  description: 'Empowering Property Decisions in Bali through Transparency, Knowledge, Connection & Trust. A strategic collaboration proposal.',
  keywords: 'Property Plaza, Paradise Indonesia, Bali Real Estate, Property Investment, Legal Transparency',
  authors: [{ name: 'Property Plaza' }],
  openGraph: {
    title: 'Property Plaza × Paradise Indonesia Partnership',
    description: 'Strategic collaboration proposal for empowering property decisions in Bali',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} overflow-x-hidden`}>
        {children}
      </body>
    </html>
  )
}
