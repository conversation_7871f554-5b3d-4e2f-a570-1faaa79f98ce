/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/sections/Metrics.tsx":
/*!**********************************!*\
  !*** ./app/sections/Metrics.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,EyeIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,EyeIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,EyeIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Metrics = ()=>{\n    const metrics = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 13,\n                columnNumber: 13\n            }, undefined),\n            title: \"Reach Growth\",\n            value: \"5K+\",\n            description: \"Average reach per post\",\n            trend: \"+45%\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 20,\n                columnNumber: 13\n            }, undefined),\n            title: \"Engagement\",\n            value: \"400+\",\n            description: \"Total engagements\",\n            trend: \"+60%\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 27,\n                columnNumber: 13\n            }, undefined),\n            title: \"Qualified Leads\",\n            value: \"75+\",\n            description: \"Qualified inquiries\",\n            trend: \"+80%\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-gray-50 py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            className: \"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6\",\n                            children: \"Results & Timeline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            className: \"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Campaign Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" & Timeline\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            className: \"text-xl text-muted max-w-3xl mx-auto\",\n                            children: \"Measurable outcomes and structured timeline for the 12-week campaign.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-8 items-stretch\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        className: \"lg:col-span-2 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-6 w-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-default mb-6 text-center\",\n                                    children: \"12-Week Campaign Timeline\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.1\n                                            },\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm\",\n                                                    children: \"1-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-xl font-semibold text-default mb-3\",\n                                                            children: \"Legal & Process Foundation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted mb-4 leading-relaxed\",\n                                                            children: \"Establish credibility through legal expertise and process transparency. Build trust with potential investors through detailed legal guidance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                                    children: \"Licensing & Permits\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 104,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                                    children: \"Transfer Requirements\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 105,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                                    children: \"Legal Risks\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                                    children: \"Trust Building\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 107,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-accent to-primary rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm\",\n                                                    children: \"5-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-xl font-semibold text-default mb-3\",\n                                                            children: \"Material & Build Quality\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted mb-4 leading-relaxed\",\n                                                            children: \"Deep-dive into construction quality, materials, and cost breakdowns. Showcase expertise in sustainable building practices.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm\",\n                                                                    children: \"Timber vs Concrete\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm\",\n                                                                    children: \"Roofing Systems\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm\",\n                                                                    children: \"Energy Efficiency\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm\",\n                                                                    children: \"Finishes\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.3\n                                            },\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm\",\n                                                    children: \"9-12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-xl font-semibold text-default mb-3\",\n                                                            children: \"ROI & Trust Network\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted mb-4 leading-relaxed\",\n                                                            children: \"Focus on investment returns and local partnership network. Demonstrate proven track record and investor success stories.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                                    children: \"Rental Yield Growth\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                                    children: \"Case Examples\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                                    children: \"Short vs Long-term Rentals\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                                    children: \"Investor Profiles\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Metrics;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Metrics);\nvar _c;\n$RefreshReg$(_c, \"Metrics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/sections/Metrics.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Ccomponents%5C%5CScrollIndicator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CAboutPropertyPlaza.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CFinalCTA.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CFourPillars.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CMarketProblem.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CMetrics.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CPilotCampaign.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CSynergy.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CWhyParadiseIndonesia.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Ccomponents%5C%5CScrollIndicator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CAboutPropertyPlaza.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CFinalCTA.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CFourPillars.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CMarketProblem.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CMetrics.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CPilotCampaign.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CSynergy.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CWhyParadiseIndonesia.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ScrollIndicator.tsx */ \"(app-pages-browser)/./app/components/ScrollIndicator.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sections/AboutPropertyPlaza.tsx */ \"(app-pages-browser)/./app/sections/AboutPropertyPlaza.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sections/FinalCTA.tsx */ \"(app-pages-browser)/./app/sections/FinalCTA.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sections/FourPillars.tsx */ \"(app-pages-browser)/./app/sections/FourPillars.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sections/Hero.tsx */ \"(app-pages-browser)/./app/sections/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sections/MarketProblem.tsx */ \"(app-pages-browser)/./app/sections/MarketProblem.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sections/Metrics.tsx */ \"(app-pages-browser)/./app/sections/Metrics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sections/PilotCampaign.tsx */ \"(app-pages-browser)/./app/sections/PilotCampaign.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sections/Synergy.tsx */ \"(app-pages-browser)/./app/sections/Synergy.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/sections/WhyParadiseIndonesia.tsx */ \"(app-pages-browser)/./app/sections/WhyParadiseIndonesia.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Ccomponents%5C%5CScrollIndicator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CAboutPropertyPlaza.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CFinalCTA.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CFourPillars.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CMarketProblem.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CMetrics.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CPilotCampaign.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CSynergy.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20presentation%5C%5CProperty-plaza-preza%5C%5Capp%5C%5Csections%5C%5CWhyParadiseIndonesia.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ })

});