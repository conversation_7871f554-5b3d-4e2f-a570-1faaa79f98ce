const e=new WeakMap;function t(t){return e.has(t)}const i=(()=>{function t(t){Object.setPrototypeOf(this,null),e.set(this,t)}return t.prototype=null,t})();function s(i){if(!t(i))throw new TypeError("not external");return e.get(i)}const n=function(){let e;try{e=new Function}catch(e){return!1}return"function"==typeof e}(),r=function(){if("undefined"!=typeof globalThis)return globalThis;let e=function(){return this}();if(!e&&n)try{e=new Function("return this")()}catch(e){}if(!e){if("undefined"==typeof __webpack_public_path__&&"undefined"!=typeof global)return global;if("undefined"!=typeof window)return window;if("undefined"!=typeof self)return self}return e}();class o{constructor(){this._exception=void 0,this._caught=!1}isEmpty(){return!this._caught}hasCaught(){return this._caught}exception(){return this._exception}setError(e){this._caught=!0,this._exception=e}reset(){this._caught=!1,this._exception=void 0}extractException(){const e=this._exception;return this.reset(),e}}const a=function(){var e;try{return Boolean(null===(e=Object.getOwnPropertyDescriptor(Function.prototype,"name"))||void 0===e?void 0:e.configurable)}catch(e){return!1}}(),l="object"==typeof Reflect,c="undefined"!=typeof FinalizationRegistry&&"undefined"!=typeof WeakRef,h=function(){try{const e=Symbol();new WeakRef(e),(new WeakMap).set(e,void 0)}catch(e){return!1}return!0}(),u="undefined"!=typeof BigInt;const p=function(){let e;return e="undefined"!=typeof __webpack_public_path__||"undefined"!=typeof __webpack_public_path__?"undefined"!=typeof __non_webpack_require__?__non_webpack_require__:void 0:"undefined"!=typeof require?require:void 0,e}(),f="function"==typeof MessageChannel?MessageChannel:function(){try{return p("worker_threads").MessageChannel}catch(e){}}(),d="function"==typeof setImmediate?setImmediate:function(e){if("function"!=typeof e)throw new TypeError('The "callback" argument must be of type function');if(f){let t=new f;t.port1.onmessage=function(){t.port1.onmessage=null,t=void 0,e()},t.port2.postMessage(null)}else setTimeout(e,0)},_="function"==typeof Buffer?Buffer:function(){try{return p("buffer").Buffer}catch(e){}}(),v="1.4.4",g=2147483647;class y{constructor(e,t){this.id=e,this.value=t}data(){return s(this.value)}isNumber(){return"number"==typeof this.value}isBigInt(){return"bigint"==typeof this.value}isString(){return"string"==typeof this.value}isFunction(){return"function"==typeof this.value}isExternal(){return t(this.value)}isObject(){return"object"==typeof this.value&&null!==this.value}isArray(){return Array.isArray(this.value)}isArrayBuffer(){return this.value instanceof ArrayBuffer}isTypedArray(){return ArrayBuffer.isView(this.value)&&!(this.value instanceof DataView)}isBuffer(e){return!!ArrayBuffer.isView(this.value)||(null!=e||(e=_),"function"==typeof e&&e.isBuffer(this.value))}isDataView(){return this.value instanceof DataView}isDate(){return this.value instanceof Date}isPromise(){return this.value instanceof Promise}isBoolean(){return"boolean"==typeof this.value}isUndefined(){return void 0===this.value}isSymbol(){return"symbol"==typeof this.value}isNull(){return null===this.value}dispose(){this.value=void 0}}class x extends y{constructor(e,t){super(e,t)}dispose(){}}class z{constructor(){this._values=[void 0,z.UNDEFINED,z.NULL,z.FALSE,z.TRUE,z.GLOBAL],this._next=z.MIN_ID}push(e){let t;const i=this._next,s=this._values;return i<s.length?(t=s[i],t.value=e):(t=new y(i,e),s[i]=t),this._next++,t}erase(e,t){this._next=e;const i=this._values;for(let s=e;s<t;++s)i[s].dispose()}get(e){return this._values[e]}swap(e,t){const i=this._values,s=i[e];i[e]=i[t],i[e].id=Number(e),i[t]=s,s.id=Number(t)}dispose(){this._values.length=z.MIN_ID,this._next=z.MIN_ID}}z.UNDEFINED=new x(1,void 0),z.NULL=new x(2,null),z.FALSE=new x(3,!1),z.TRUE=new x(4,!0),z.GLOBAL=new x(5,r),z.MIN_ID=6;class k{constructor(e,t,i,s,n=s){this.handleStore=e,this.id=t,this.parent=i,this.child=null,null!==i&&(i.child=this),this.start=s,this.end=n,this._escapeCalled=!1,this.callbackInfo={thiz:void 0,data:0,args:void 0,fn:void 0}}add(e){const t=this.handleStore.push(e);return this.end++,t}addExternal(e){return this.add(new i(e))}dispose(){this._escapeCalled&&(this._escapeCalled=!1),this.start!==this.end&&this.handleStore.erase(this.start,this.end)}escape(e){if(this._escapeCalled)return null;if(this._escapeCalled=!0,e<this.start||e>=this.end)return null;this.handleStore.swap(e,this.start);const t=this.handleStore.get(this.start);return this.start++,this.parent.end++,t}escapeCalled(){return this._escapeCalled}}class b{constructor(){this._rootScope=new k(null,0,null,1,z.MIN_ID),this.currentScope=this._rootScope,this._values=[void 0]}get(e){return this._values[e]}openScope(e){const t=this.currentScope;let i=t.child;if(null!==i)i.start=i.end=t.end;else{const s=t.id+1;i=new k(e.ctx.handleStore,s,t,t.end),this._values[s]=i}return this.currentScope=i,e.openHandleScopes++,i}closeScope(e){if(0===e.openHandleScopes)return;const t=this.currentScope;this.currentScope=t.parent,t.dispose(),e.openHandleScopes--}dispose(){this.currentScope=this._rootScope,this._values.length=1}}class w{constructor(){this._next=null,this._prev=null}dispose(){}finalize(){}link(e){this._prev=e,this._next=e._next,null!==this._next&&(this._next._prev=this),e._next=this}unlink(){null!==this._prev&&(this._prev._next=this._next),null!==this._next&&(this._next._prev=this._prev),this._prev=null,this._next=null}static finalizeAll(e){for(;null!==e._next;)e._next.finalize()}}class S{constructor(e,t=0,i=0,s=0){this.envObject=e,this._finalizeCallback=t,this._finalizeData=i,this._finalizeHint=s,this._makeDynCall_vppp=e.makeDynCall_vppp}callback(){return this._finalizeCallback}data(){return this._finalizeData}hint(){return this._finalizeHint}resetEnv(){this.envObject=void 0}resetFinalizer(){this._finalizeCallback=0,this._finalizeData=0,this._finalizeHint=0}callFinalizer(){const e=this._finalizeCallback,t=this._finalizeData,i=this._finalizeHint;if(this.resetFinalizer(),!e)return;const s=Number(e);this.envObject?this.envObject.callFinalizer(s,t,i):this._makeDynCall_vppp(s)(0,t,i)}dispose(){this.envObject=void 0,this._makeDynCall_vppp=void 0}}class E extends w{static create(e,t,i,s){const n=new E(e,t,i,s);return n.link(e.finalizing_reflist),n}constructor(e,t,i,s){super(),this._finalizer=new S(e,t,i,s)}data(){return this._finalizer.data()}dispose(){this._finalizer&&(this.unlink(),this._finalizer.envObject.dequeueFinalizer(this),this._finalizer.dispose(),this._finalizer=void 0,super.dispose())}finalize(){let e;this.unlink();let t=!1;try{this._finalizer.callFinalizer()}catch(i){t=!0,e=i}if(this.dispose(),t)throw e}}function m(e,t){if(!e.terminatedOrTerminating())throw t}class C{constructor(e,t,i,s,n){this.ctx=e,this.moduleApiVersion=t,this.makeDynCall_vppp=i,this.makeDynCall_vp=s,this.abort=n,this.openHandleScopes=0,this.instanceData=null,this.tryCatch=new o,this.refs=1,this.reflist=new w,this.finalizing_reflist=new w,this.pendingFinalizers=[],this.lastError={errorCode:0,engineErrorCode:0,engineReserved:0},this.inGcFinalizer=!1,this._bindingMap=new WeakMap,this.id=0}canCallIntoJs(){return!0}terminatedOrTerminating(){return!this.canCallIntoJs()}ref(){this.refs++}unref(){this.refs--,0===this.refs&&this.dispose()}ensureHandle(e){return this.ctx.ensureHandle(e)}ensureHandleId(e){return this.ensureHandle(e).id}clearLastError(){const e=this.lastError;return 0!==e.errorCode&&(e.errorCode=0),0!==e.engineErrorCode&&(e.engineErrorCode=0),0!==e.engineReserved&&(e.engineReserved=0),0}setLastError(e,t=0,i=0){const s=this.lastError;return s.errorCode!==e&&(s.errorCode=e),s.engineErrorCode!==t&&(s.engineErrorCode=t),s.engineReserved!==i&&(s.engineReserved=i),e}getReturnStatus(){return this.tryCatch.hasCaught()?this.setLastError(10):0}callIntoModule(e,t=m){const i=this.openHandleScopes;this.clearLastError();const s=e(this);if(i!==this.openHandleScopes&&this.abort("open_handle_scopes != open_handle_scopes_before"),this.tryCatch.hasCaught()){t(this,this.tryCatch.extractException())}return s}invokeFinalizerFromGC(e){if(this.moduleApiVersion!==g)this.enqueueFinalizer(e);else{const t=this.inGcFinalizer;this.inGcFinalizer=!0;try{e.finalize()}finally{this.inGcFinalizer=t}}}checkGCAccess(){this.moduleApiVersion===g&&this.inGcFinalizer&&this.abort("Finalizer is calling a function that may affect GC state.\nThe finalizers are run directly from GC and must not affect GC state.\nUse `node_api_post_finalizer` from inside of the finalizer to work around this issue.\nIt schedules the call as a new task in the event loop.")}enqueueFinalizer(e){-1===this.pendingFinalizers.indexOf(e)&&this.pendingFinalizers.push(e)}dequeueFinalizer(e){const t=this.pendingFinalizers.indexOf(e);-1!==t&&this.pendingFinalizers.splice(t,1)}deleteMe(){w.finalizeAll(this.finalizing_reflist),w.finalizeAll(this.reflist),this.tryCatch.extractException(),this.ctx.envStore.remove(this.id)}dispose(){0!==this.id&&(this.deleteMe(),this.finalizing_reflist.dispose(),this.reflist.dispose(),this.id=0)}initObjectBinding(e){const t={wrapped:0,tag:null};return this._bindingMap.set(e,t),t}getObjectBinding(e){return this._bindingMap.has(e)?this._bindingMap.get(e):this.initObjectBinding(e)}setInstanceData(e,t,i){this.instanceData&&this.instanceData.dispose(),this.instanceData=E.create(this,t,e,i)}getInstanceData(){return this.instanceData?this.instanceData.data():0}}class F extends C{constructor(e,t,i,s,n,r,o){super(e,i,s,n,r),this.filename=t,this.nodeBinding=o,this.destructing=!1,this.finalizationScheduled=!1}deleteMe(){this.destructing=!0,this.drainFinalizerQueue(),super.deleteMe()}canCallIntoJs(){return super.canCallIntoJs()&&this.ctx.canCallIntoJs()}triggerFatalException(e){if(this.nodeBinding)this.nodeBinding.napi.fatalException(e);else{if("object"!=typeof process||null===process||"function"!=typeof process._fatalException)throw e;process._fatalException(e)||(console.error(e),process.exit(1))}}callbackIntoModule(e,t){return this.callIntoModule(t,(t,i)=>{if(t.terminatedOrTerminating())return;const s="object"==typeof process&&null!==process,n=!!s&&Boolean(process.execArgv&&-1!==process.execArgv.indexOf("--force-node-api-uncaught-exceptions-policy"));if(t.moduleApiVersion<10&&!n&&!e){return void(s&&"function"==typeof process.emitWarning?process.emitWarning:function(e,t,i){if(e instanceof Error)console.warn(e.toString());else{const s=i?`[${i}] `:"";console.warn(`${s}${t||"Warning"}: ${e}`)}})("Uncaught N-API callback exception detected, please run node with option --force-node-api-uncaught-exceptions-policy=true to handle those exceptions properly.","DeprecationWarning","DEP0168")}t.triggerFatalException(i)})}callFinalizer(e,t,i){this.callFinalizerInternal(1,e,t,i)}callFinalizerInternal(e,t,i,s){const n=this.makeDynCall_vppp(t),r=this.id,o=this.ctx.openScope(this);try{this.callbackIntoModule(Boolean(e),()=>{n(r,i,s)})}finally{this.ctx.closeScope(this,o)}}enqueueFinalizer(e){super.enqueueFinalizer(e),this.finalizationScheduled||this.destructing||(this.finalizationScheduled=!0,this.ref(),d(()=>{this.finalizationScheduled=!1,this.unref(),this.drainFinalizerQueue()}))}drainFinalizerQueue(){for(;this.pendingFinalizers.length>0;){this.pendingFinalizers.shift().finalize()}}}function I(e,t,i,s,n,r,o){(i="number"!=typeof i?8:i)<8?i=8:i>10&&i!==g&&function(e,t){throw new Error(`${e} requires Node-API version ${t}, but this version of Node.js only supports version 10 add-ons.`)}(t,i);const a=new F(e,t,i,s,n,r,o);return e.envStore.add(a),e.addCleanupHook(a,()=>{a.unref()},0),a}class O extends Error{constructor(e){super(e);const t=new.target,i=t.prototype;if(!(this instanceof O)){const e=Object.setPrototypeOf;"function"==typeof e?e.call(Object,this,i):this.__proto__=i,"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,t)}}}Object.defineProperty(O.prototype,"name",{configurable:!0,writable:!0,value:"EmnapiError"});class D extends O{constructor(e,t){super(`${e}: The current runtime does not support "FinalizationRegistry" and "WeakRef".${t?` ${t}`:""}`)}}Object.defineProperty(D.prototype,"name",{configurable:!0,writable:!0,value:"NotSupportWeakRefError"});class R extends O{constructor(e,t){super(`${e}: The current runtime does not support "Buffer". Consider using buffer polyfill to make sure \`globalThis.Buffer\` is defined.${t?` ${t}`:""}`)}}Object.defineProperty(R.prototype,"name",{configurable:!0,writable:!0,value:"NotSupportBufferError"});class N{constructor(e){this._value=e}deref(){return this._value}dispose(){this._value=void 0}}class H{constructor(e){this._ref=new N(e)}setWeak(e,t){if(!c||void 0===this._ref||this._ref instanceof WeakRef)return;const i=this._ref.deref();try{H._registry.register(i,this,this);const s=new WeakRef(i);this._ref.dispose(),this._ref=s,this._param=e,this._callback=t}catch(e){if("symbol"!=typeof i)throw e}}clearWeak(){if(c&&void 0!==this._ref&&this._ref instanceof WeakRef){try{H._registry.unregister(this)}catch(e){}this._param=void 0,this._callback=void 0;const e=this._ref.deref();this._ref=void 0===e?e:new N(e)}}reset(){if(c)try{H._registry.unregister(this)}catch(e){}this._param=void 0,this._callback=void 0,this._ref instanceof N&&this._ref.dispose(),this._ref=void 0}isEmpty(){return void 0===this._ref}deref(){if(void 0!==this._ref)return this._ref.deref()}}var A;H._registry=c?new FinalizationRegistry(e=>{e._ref=void 0;const t=e._callback,i=e._param;e._callback=void 0,e._param=void 0,"function"==typeof t&&t(i)}):void 0,exports.ReferenceOwnership=void 0,(A=exports.ReferenceOwnership||(exports.ReferenceOwnership={}))[A.kRuntime=0]="kRuntime",A[A.kUserland=1]="kUserland";class j extends w{static weakCallback(e){e.persistent.reset(),e.invokeFinalizerFromGC()}static create(e,t,i,s,n,r,o){const a=new j(e,t,i,s);return e.ctx.refStore.add(a),a.link(e.reflist),a}constructor(e,t,i,s){super(),this.envObject=e,this._refcount=i,this._ownership=s;const n=e.ctx.handleStore.get(t);var r;this.canBeWeak=(r=n).isObject()||r.isFunction()||r.isSymbol(),this.persistent=new H(n.value),this.id=0,0===i&&this._setWeak()}ref(){return this.persistent.isEmpty()?0:(1===++this._refcount&&this.canBeWeak&&this.persistent.clearWeak(),this._refcount)}unref(){return this.persistent.isEmpty()||0===this._refcount?0:(0===--this._refcount&&this._setWeak(),this._refcount)}get(e=this.envObject){if(this.persistent.isEmpty())return 0;const t=this.persistent.deref();return e.ensureHandle(t).id}resetFinalizer(){}data(){return 0}refcount(){return this._refcount}ownership(){return this._ownership}callUserFinalizer(){}invokeFinalizerFromGC(){this.finalize()}_setWeak(){this.canBeWeak?this.persistent.setWeak(this,j.weakCallback):this.persistent.reset()}finalize(){this.persistent.reset();const e=this._ownership===exports.ReferenceOwnership.kRuntime;this.unlink(),this.callUserFinalizer(),e&&this.dispose()}dispose(){0!==this.id&&(this.unlink(),this.persistent.reset(),this.envObject.ctx.refStore.remove(this.id),super.dispose(),this.envObject=void 0,this.id=0)}}class B extends j{static create(e,t,i,s,n){const r=new B(e,t,i,s,n);return e.ctx.refStore.add(r),r.link(e.reflist),r}constructor(e,t,i,s,n){super(e,t,i,s),this._data=n}data(){return this._data}}class T extends j{static create(e,t,i,s,n,r,o){const a=new T(e,t,i,s,n,r,o);return e.ctx.refStore.add(a),a.link(e.finalizing_reflist),a}constructor(e,t,i,s,n,r,o){super(e,t,i,s),this._finalizer=new S(e,n,r,o)}resetFinalizer(){this._finalizer.resetFinalizer()}data(){return this._finalizer.data()}callUserFinalizer(){this._finalizer.callFinalizer()}invokeFinalizerFromGC(){this._finalizer.envObject.invokeFinalizerFromGC(this)}dispose(){this._finalizer&&(this._finalizer.envObject.dequeueFinalizer(this),this._finalizer.dispose(),super.dispose(),this._finalizer=void 0)}}class W{static create(e,t){const i=new W(e,t);return e.deferredStore.add(i),i}constructor(e,t){this.id=0,this.ctx=e,this.value=t}resolve(e){this.value.resolve(e),this.dispose()}reject(e){this.value.reject(e),this.dispose()}dispose(){this.ctx.deferredStore.remove(this.id),this.id=0,this.value=null,this.ctx=null}}class M{constructor(){this._values=[void 0],this._values.length=4,this._size=1,this._freeList=[]}add(e){let t;if(this._freeList.length)t=this._freeList.shift();else{t=this._size,this._size++;const e=this._values.length;t>=e&&(this._values.length=e+(e>>1)+16)}e.id=t,this._values[t]=e}get(e){return this._values[e]}has(e){return void 0!==this._values[e]}remove(e){const t=this._values[e];t&&(t.id=0,this._values[e]=void 0,this._freeList.push(Number(e)))}dispose(){for(let e=1;e<this._size;++e){const t=this._values[e];null==t||t.dispose()}this._values=[void 0],this._size=1,this._freeList=[]}}class L{constructor(e,t,i,s){this.envObject=e,this.fn=t,this.arg=i,this.order=s}}class P{constructor(){this._cleanupHooks=[],this._cleanupHookCounter=0}empty(){return 0===this._cleanupHooks.length}add(e,t,i){if(this._cleanupHooks.filter(s=>s.envObject===e&&s.fn===t&&s.arg===i).length>0)throw new Error("Can not add same fn and arg twice");this._cleanupHooks.push(new L(e,t,i,this._cleanupHookCounter++))}remove(e,t,i){for(let s=0;s<this._cleanupHooks.length;++s){const n=this._cleanupHooks[s];if(n.envObject===e&&n.fn===t&&n.arg===i)return void this._cleanupHooks.splice(s,1)}}drain(){const e=this._cleanupHooks.slice();e.sort((e,t)=>t.order-e.order);for(let t=0;t<e.length;++t){const i=e[t];"number"==typeof i.fn?i.envObject.makeDynCall_vp(i.fn)(i.arg):i.fn(i.arg),this._cleanupHooks.splice(this._cleanupHooks.indexOf(i),1)}}dispose(){this._cleanupHooks.length=0,this._cleanupHookCounter=0}}class U{constructor(){this.refHandle=(new f).port1,this.count=0}increase(){0===this.count&&this.refHandle.ref&&this.refHandle.ref(),this.count++}decrease(){0!==this.count&&(1===this.count&&this.refHandle.unref&&this.refHandle.unref(),this.count--)}}class V{constructor(){this._isStopping=!1,this._canCallIntoJs=!0,this._suppressDestroy=!1,this.envStore=new M,this.scopeStore=new b,this.refStore=new M,this.deferredStore=new M,this.handleStore=new z,this.feature={supportReflect:l,supportFinalizer:c,supportWeakSymbol:h,supportBigInt:u,supportNewFunction:n,canSetFunctionName:a,setImmediate:d,Buffer:_,MessageChannel:f},this.cleanupQueue=new P,"object"==typeof process&&null!==process&&"function"==typeof process.once&&(this.refCounter=new U,process.once("beforeExit",()=>{this._suppressDestroy||this.destroy()}))}suppressDestroy(){this._suppressDestroy=!0}getRuntimeVersions(){return{version:v,NODE_API_SUPPORTED_VERSION_MAX:10,NAPI_VERSION_EXPERIMENTAL:g,NODE_API_DEFAULT_MODULE_API_VERSION:8}}createNotSupportWeakRefError(e,t){return new D(e,t)}createNotSupportBufferError(e,t){return new R(e,t)}createReference(e,t,i,s){return j.create(e,t,i,s)}createReferenceWithData(e,t,i,s,n){return B.create(e,t,i,s,n)}createReferenceWithFinalizer(e,t,i,s,n=0,r=0,o=0){return T.create(e,t,i,s,n,r,o)}createDeferred(e){return W.create(this,e)}createEnv(e,t,i,s,n,r){return I(this,e,t,i,s,n,r)}createTrackedFinalizer(e,t,i,s){return E.create(e,t,i,s)}getCurrentScope(){return this.scopeStore.currentScope}addToCurrentScope(e){return this.scopeStore.currentScope.add(e)}openScope(e){return this.scopeStore.openScope(e)}closeScope(e,t){this.scopeStore.closeScope(e)}ensureHandle(e){switch(e){case void 0:return z.UNDEFINED;case null:return z.NULL;case!0:return z.TRUE;case!1:return z.FALSE;case r:return z.GLOBAL}return this.addToCurrentScope(e)}addCleanupHook(e,t,i){this.cleanupQueue.add(e,t,i)}removeCleanupHook(e,t,i){this.cleanupQueue.remove(e,t,i)}runCleanup(){for(;!this.cleanupQueue.empty();)this.cleanupQueue.drain()}increaseWaitingRequestCounter(){var e;null===(e=this.refCounter)||void 0===e||e.increase()}decreaseWaitingRequestCounter(){var e;null===(e=this.refCounter)||void 0===e||e.decrease()}setCanCallIntoJs(e){this._canCallIntoJs=e}setStopping(e){this._isStopping=e}canCallIntoJs(){return this._canCallIntoJs&&!this._isStopping}destroy(){this.setStopping(!0),this.setCanCallIntoJs(!1),this.runCleanup()}}let G;function q(){return new V}exports.ConstHandle=x,exports.Context=V,exports.Deferred=W,exports.EmnapiError=O,exports.Env=C,exports.External=i,exports.Finalizer=S,exports.Handle=y,exports.HandleScope=k,exports.HandleStore=z,exports.NAPI_VERSION_EXPERIMENTAL=g,exports.NODE_API_DEFAULT_MODULE_API_VERSION=8,exports.NODE_API_SUPPORTED_VERSION_MAX=10,exports.NODE_API_SUPPORTED_VERSION_MIN=1,exports.NodeEnv=F,exports.NotSupportBufferError=R,exports.NotSupportWeakRefError=D,exports.Persistent=H,exports.RefTracker=w,exports.Reference=j,exports.ReferenceWithData=B,exports.ReferenceWithFinalizer=T,exports.ScopeStore=b,exports.Store=M,exports.TrackedFinalizer=E,exports.TryCatch=o,exports.createContext=q,exports.getDefaultContext=function(){return G||(G=q()),G},exports.getExternalValue=s,exports.isExternal=t,exports.isReferenceType=function(e){return"object"==typeof e&&null!==e||"function"==typeof e},exports.version=v;
