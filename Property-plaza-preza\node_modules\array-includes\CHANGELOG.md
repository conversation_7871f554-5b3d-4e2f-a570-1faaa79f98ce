# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v3.1.9](https://github.com/es-shims/array-includes/compare/v3.1.8...v3.1.9) - 2025-06-01

### Commits

- [Deps] update `call-bind`, `es-abstract`, `es-object-atoms`, `get-intrinsic`, `is-string` [`3b934ae`](https://github.com/es-shims/array-includes/commit/3b934ae87a602798775c04dbeb4cb9e9f1805610)
- [Refactor] use `call-bound` and `math-intrinsics` directly [`160ea60`](https://github.com/es-shims/array-includes/commit/160ea60f7d9b205e516915558b503d078945dea3)
- [<PERSON>] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `hastrict-mode`, `tape` [`4e4c67d`](https://github.com/es-shims/array-includes/commit/4e4c67dd976b7ee56e12b7eabb3ad4ab4881f4e5)
- [Tests] replace `aud` with `npm audit` [`9c5ec1c`](https://github.com/es-shims/array-includes/commit/9c5ec1c160e0f7adeebb78350675465003b2d888)
- [Dev Deps] add missing peer dep [`863d207`](https://github.com/es-shims/array-includes/commit/863d207753789757edded151ab3d62ae5033c021)

## [v3.1.8](https://github.com/es-shims/array-includes/compare/v3.1.7...v3.1.8) - 2024-03-20

### Commits

- [Deps] update `call-bind`, `define-properties`, `es-abstract`, `get-intrinsic` [`b105f3a`](https://github.com/es-shims/array-includes/commit/b105f3a0ad1801aabf47bafd788482d8866ef0c9)
- [actions] remove redundant finisher [`fa1111d`](https://github.com/es-shims/array-includes/commit/fa1111d6d41381e4bd484e8f6d5896172874d6e7)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`499e7e9`](https://github.com/es-shims/array-includes/commit/499e7e9e952faab35740dfbe0b355183100e40b7)
- [Refactor] use `es-object-atoms` where possible [`e4bd17f`](https://github.com/es-shims/array-includes/commit/e4bd17f980b557fdf2b39eb552bafedd8a8f6bfb)
- [Tests] use `call-bind` instead of `function-bind` [`3dbe456`](https://github.com/es-shims/array-includes/commit/3dbe456f14c94a5d119b35ac41712c7faea93afd)

## [v3.1.7](https://github.com/es-shims/array-includes/compare/v3.1.6...v3.1.7) - 2023-09-03

### Commits

- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`e217b1e`](https://github.com/es-shims/array-includes/commit/e217b1ebfe90a7425654f0fa72c59f59930d4dd8)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`93465c3`](https://github.com/es-shims/array-includes/commit/93465c3da3bd6ea0670a0bd12dc9adc9085e8130)

## [v3.1.6](https://github.com/es-shims/array-includes/compare/v3.1.5...v3.1.6) - 2022-11-07

### Commits

- [meta] add `auto-changelog` [`c5fbe72`](https://github.com/es-shims/array-includes/commit/c5fbe728395deff641d756cc9d398a949076c180)
- [meta] use `npmignore` to autogenerate an npmignore file [`dbd6dc8`](https://github.com/es-shims/array-includes/commit/dbd6dc8f8906cca6b0b493e308686c4fd05bea15)
- [Deps] update `es-abstract`, `get-intrinsic` [`b819e3b`](https://github.com/es-shims/array-includes/commit/b819e3b3dd1adce0b3359529b0276a416efce351)
- [actions] update rebase action to use reusable workflow [`6e241d5`](https://github.com/es-shims/array-includes/commit/6e241d5177513cfb0261d0fbe0c8c98daf5c5eab)
- [Dev Deps] update `aud`, `tape` [`9b2a931`](https://github.com/es-shims/array-includes/commit/9b2a931aee6fc8195a349c6fe7894445a911223f)
- [readme] note that FF 102+ no longer needs this package [`0a0c758`](https://github.com/es-shims/array-includes/commit/0a0c758ed52808428314ffe53ea8278297170c84)

<!-- auto-changelog-above -->

3.1.5 / 2022-05-03
=================
  * [Fix] install polyfill on FF 99+
  * [Deps] update `define-properties`, `es-abstract`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `functions-have-names`, `safe-publish-latest`, `tape`
  * [actions] reuse common workflows
  * [actions] update codecov uploader

3.1.4 / 2021-10-04
=================
  * [Robustness] avoid a runtime `Math.max` call
  * [readme] add github actions/codecov badges
  * [readme] fix repo URLs; remove travis badge
  * [Deps] update `es-abstract`, `is-string`
  * [meta] use `prepublishOnly` script for npm 7+
  * [actions] update workflows
  * [actions] use `node/install` instead of `node/run`; use `codecov` action
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `tape`

3.1.3 / 2021-02-20
=================
  * [Deps] update `call-bind`, `es-abstract`, `get-intrinsic`
  * [meta] do not publish github action workflow files
  * [meta] gitignore coverage output
  * [actions] update workflows
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `functions-have-names`, `has-strict-mode`, `tape`
  * [Tests] increase coverage

3.1.2 / 2020-11-24
=================
  * [Robustness] remove dependency on `.apply`
  * [Deps] update `es-abstract`; use `call-bind` and `get-intrinsic` where applicable
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `functions-have-names`, `tape`; add `aud`, `safe-publish-latest`
  * [actions] add "Allow Edits" workflow
  * [actions] switch Automatic Rebase workflow to `pull_request_target` event
  * [Tests] migrate tests to Github Actions
  * [Tests] run `nyc` on all tests
  * [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner

3.1.1 / 2019-12-21
=================
  * [Fix] IE < 9 does not have index access on strings
  * [Deps] update `es-abstract`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`
  * [meta] remove unused Makefile and associated utilities
  * [Tests] add string tests

3.1.0 / 2019-12-11
=================
  * [New] add `auto` entry point
  * [Refactor] use split-up `es-abstract` (68% bundle size decrease)
  * [readme] fix repo URLs, remove testling, fix readme parsing
  * [Deps] update `es-abstract`, `define-properties`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `evalmd`, `covert`, `functions-have-names`, `replace`, `semver`, `tape`, `@es-shims/api`, `function-bind`
  * [meta] add `funding` field, FUNDING.yml
  * [meta] Only apps should have lockfiles
  * [Tests] add more `fromIndex` tests
  * [Tests] use shared travis-ci configs
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops
  * [Tests] remove `jscs`
  * [Tests] use `functions-have-names`
  * [Tests] use `npm audit` instead of `nsp`
  * [Tests] remove `jscs`
  * [actions] add automatic rebasing / merge commit blocking

3.0.3 / 2017-04-18
=================
  * [Fix] ensure that `shim.js` actually shims when the polyfill differs from native
  * [Tests] up to `node` `v7.9`, `v6.10`, `v4.8`; comment out OS X builds; improve test matrix
  * [Dev Deps] update `nsp`, `eslint`, `@ljharb/eslint-config`, `tape`, `jscs`, `semver`, `function-bind`, `@es-shims/api`
  * [Deps] update `es-abstract`
  * [Docs] update readme: add “getting started” and “usage” (#19)

3.0.2 / 2015-06-06
=================
  * Use the polyfill, not the implementation, as the default export
  * [Deps] update `es-abstract`
  * [Dev Deps] update `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver`
  * [Tests] up to `node` `v5.5`
  * [Tests] keep tests passing in `node` `v0.8`
  * [Tests] Only run `evalmd` as part of the full test suite, since it's more like a linter
  * [Tests] fix npm upgrades for older nodes

3.0.1 / 2015-05-23
=================
  * [Fix] in "shim", assign the polyfill, not the implementation

3.0.0 / 2015-05-23
=================
  * [Breaking] Implement the [es-shim API](es-shims/api)
  * [Deps] update `define-properties`, `es-abstract`
  * [Dev Deps] update `eslint`, `semver`, `nsp`, `semver`, `jscs`
  * [Docs] Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG
  * [Tests] use my personal shared `eslint` config
  * [Tests] up to `io.js` `v3.0`

2.0.0 / 2015-05-23
=================
  * Fix to not skip holes, per https://github.com/tc39/Array.prototype.includes/issues/15

1.1.1 / 2015-05-23
=================
  * Test up to `io.js` `v2.0`
  * Update `es-abstract`, `tape`, `eslint`, `semver`, `jscs`, `semver`

1.1.0 / 2015-03-19
=================
  * Update `es-abstract`, `editorconfig-tools`, `nsp`, `eslint`, `semver`

1.0.6 / 2015-02-17
=================
  * All grade A-supported `node`/`iojs` versions now ship with an `npm` that understands `^`.
  * Run `travis-ci` tests on `iojs` and `node` v0.12; allow 0.8 failures.
  * Update `tape`, `jscs`, `es-abstract`, remove `is`.

1.0.5 / 2015-01-30
=================
  * Update `tape`, `jscs`, `nsp`, `eslint`, `es-abstract`

1.0.4 / 2015-01-10
=================
  * Use `es-abstract` for ECMAScript spec internal abstract operations

1.0.3 / 2015-01-06
=================
  * Speed optimization: use Array#indexOf when available
  * Fix ES3, IE 6-8, Opera 10.6, Opera 11.1 support
  * Run testling on both sets of tests

1.0.2 / 2015-01-05
=================
  * Ensure tests are includes in the module on `npm`

1.0.1 / 2015-01-04
=================
  * Remove mistaken auto-shim.

1.0.0 / 2015-01-04
=================
  * v1.0.0
