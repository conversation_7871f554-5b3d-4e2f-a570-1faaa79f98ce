"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/sections/Synergy.tsx":
/*!**********************************!*\
  !*** ./app/sections/Synergy.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Synergy = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 30\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        className: \"text-3xl md:text-4xl font-bold text-default mb-8 leading-tight\",\n                        children: [\n                            \"Together We Offer \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"gradient-text\",\n                                children: \"Certainty\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, undefined),\n                            \"In An Uncertain Market\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        className: \"flex items-center justify-center w-48 h-32 mr-16\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            rotate: 2\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: \"/images/Transparent -02.png\",\n                                            alt: \"Property Plaza Logo\",\n                                            width: 400,\n                                            height: 400,\n                                            className: \"object-contain drop-shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        className: \"text-4xl text-primary flex items-center justify-center mr-12\",\n                                        animate: {\n                                            scale: [\n                                                1,\n                                                1.2,\n                                                1\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity\n                                        },\n                                        children: \"+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        className: \"flex items-center justify-center w-48 h-32\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            rotate: -2\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: \"/images/paradise-indonesia.png\",\n                                            alt: \"Paradise Indonesia Logo\",\n                                            width: 160,\n                                            height: 160,\n                                            className: \"object-contain drop-shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.6\n                                },\n                                className: \"space-y-4 mb-8 max-w-2xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg text-muted\",\n                                        children: \"\\uD83D\\uDD17 Structure meets speed.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg text-muted\",\n                                        children: \"\\uD83D\\uDCF1 Tech meets trust.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg text-muted\",\n                                        children: \"\\uD83E\\uDDED Legal certainty meets creative access.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 1.0\n                                },\n                                className: \"text-lg text-muted max-w-3xl mx-auto border-t border-primary/20 pt-6\",\n                                children: [\n                                    \"When trusted legal foundations meet digital innovation, Bali's property market gets what it's always needed: access without risk.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary font-medium\",\n                                        children: \"That's what happens when Paradise Indonesia partners with Property Plaza.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Synergy;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Synergy);\nvar _c;\n$RefreshReg$(_c, \"Synergy\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/sections/Synergy.tsx\n"));

/***/ })

});