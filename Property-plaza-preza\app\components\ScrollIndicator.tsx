"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const ScrollIndicator = () => {
  const [currentSection, setCurrentSection] = useState(1);
  const totalSections = 9;

  useEffect(() => {
    const handleScroll = () => {
      const sections = document.querySelectorAll('.snap-section');
      const scrollPosition = window.scrollY + window.innerHeight / 2;

      sections.forEach((section, index) => {
        const element = section as HTMLElement;
        const top = element.offsetTop;
        const bottom = top + element.offsetHeight;

        if (scrollPosition >= top && scrollPosition < bottom) {
          setCurrentSection(index + 1);
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="fixed right-8 top-1/2 transform -translate-y-1/2 z-50 hidden lg:block">
      <div className="flex flex-col space-y-3">
        {Array.from({ length: totalSections }, (_, i) => (
          <motion.div
            key={i}
            className={`w-3 h-3 rounded-full border-2 border-primary cursor-pointer transition-all duration-300 ${
              currentSection === i + 1 ? 'bg-primary scale-125' : 'bg-transparent hover:bg-primary/20'
            }`}
            whileHover={{ scale: 1.2 }}
            onClick={() => {
              const section = document.querySelectorAll('.snap-section')[i];
              section?.scrollIntoView({ behavior: 'smooth' });
            }}
          />
        ))}
      </div>
      
      <div className="mt-4 text-center">
        <span className="text-sm text-muted font-medium">
          {currentSection} / {totalSections}
        </span>
      </div>
    </div>
  );
};

export default ScrollIndicator;
