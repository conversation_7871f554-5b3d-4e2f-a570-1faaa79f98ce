"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[787],{184:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},631:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v17.25m0 0c-1.472 0-2.882.265-4.185.75M12 20.25c1.472 0 2.882.265 4.185.75M18.75 4.97A48.416 48.416 0 0 0 12 4.5c-2.291 0-4.545.16-6.75.47m13.5 0c1.01.143 2.01.317 3 .52m-3-.52 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.988 5.988 0 0 1-2.031.352 5.988 5.988 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L18.75 4.971Zm-16.5.52c.99-.203 1.99-.377 3-.52m0 0 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.989 5.989 0 0 1-2.031.352 5.989 5.989 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L5.25 4.971Z"}))})},901:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"RouterContext",{enumerable:!0,get:function(){return n}});let n=i(8229)._(i(2115)).default.createContext(null)},1174:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"}))})},1193:(t,e)=>{function i(t){var e;let{config:i,src:n,width:r,quality:s}=t,o=s||(null==(e=i.qualities)?void 0:e.reduce((t,e)=>Math.abs(e-75)<Math.abs(t-75)?e:t))||75;return i.path+"?url="+encodeURIComponent(n)+"&w="+r+"&q="+o+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return n}}),i.__next_img_default=!0;let n=i},1223:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"}))})},1316:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},1469:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return l},getImageProps:function(){return a}});let n=i(8229),r=i(8883),s=i(3063),o=n._(i(1193));function a(t){let{props:e}=(0,r.getImgProps)(t,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[t,i]of Object.entries(e))void 0===i&&delete e[t];return{props:e}}let l=s.Image},2227:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},2247:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m0-10.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.75c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.25-8.25-3.286Zm0 13.036h.008v.008H12v-.008Z"}))})},2464:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=i(8229)._(i(2115)).default.createContext({})},2513:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))})},2771:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},2975:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},3063:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"Image",{enumerable:!0,get:function(){return x}});let n=i(8229),r=i(6966),s=i(5155),o=r._(i(2115)),a=n._(i(7650)),l=n._(i(5564)),u=i(8883),h=i(5840),d=i(6752);i(3230);let c=i(901),p=n._(i(1193)),m=i(6654),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function v(t,e,i,n,r,s,o){let a=null==t?void 0:t.src;t&&t["data-loaded-src"]!==a&&(t["data-loaded-src"]=a,("decode"in t?t.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(t.parentElement&&t.isConnected){if("empty"!==e&&r(!0),null==i?void 0:i.current){let e=new Event("load");Object.defineProperty(e,"target",{writable:!1,value:t});let n=!1,r=!1;i.current({...e,nativeEvent:e,currentTarget:t,target:t,isDefaultPrevented:()=>n,isPropagationStopped:()=>r,persist:()=>{},preventDefault:()=>{n=!0,e.preventDefault()},stopPropagation:()=>{r=!0,e.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(t)}}))}function g(t){return o.use?{fetchPriority:t}:{fetchpriority:t}}let y=(0,o.forwardRef)((t,e)=>{let{src:i,srcSet:n,sizes:r,height:a,width:l,decoding:u,className:h,style:d,fetchPriority:c,placeholder:p,loading:f,unoptimized:y,fill:w,onLoadRef:x,onLoadingCompleteRef:b,setBlurComplete:P,setShowAltText:A,sizesInput:S,onLoad:E,onError:T,...M}=t,k=(0,o.useCallback)(t=>{t&&(T&&(t.src=t.src),t.complete&&v(t,p,x,b,P,y,S))},[i,p,x,b,P,T,y,S]),C=(0,m.useMergedRef)(e,k);return(0,s.jsx)("img",{...M,...g(c),loading:f,width:l,height:a,decoding:u,"data-nimg":w?"fill":"1",className:h,style:d,sizes:r,srcSet:n,src:i,ref:C,onLoad:t=>{v(t.currentTarget,p,x,b,P,y,S)},onError:t=>{A(!0),"empty"!==p&&P(!0),T&&T(t)}})});function w(t){let{isAppRouter:e,imgAttributes:i}=t,n={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...g(i.fetchPriority)};return e&&a.default.preload?(a.default.preload(i.src,n),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...n},"__nimg-"+i.src+i.srcSet+i.sizes)})}let x=(0,o.forwardRef)((t,e)=>{let i=(0,o.useContext)(c.RouterContext),n=(0,o.useContext)(d.ImageConfigContext),r=(0,o.useMemo)(()=>{var t;let e=f||n||h.imageConfigDefault,i=[...e.deviceSizes,...e.imageSizes].sort((t,e)=>t-e),r=e.deviceSizes.sort((t,e)=>t-e),s=null==(t=e.qualities)?void 0:t.sort((t,e)=>t-e);return{...e,allSizes:i,deviceSizes:r,qualities:s}},[n]),{onLoad:a,onLoadingComplete:l}=t,m=(0,o.useRef)(a);(0,o.useEffect)(()=>{m.current=a},[a]);let v=(0,o.useRef)(l);(0,o.useEffect)(()=>{v.current=l},[l]);let[g,x]=(0,o.useState)(!1),[b,P]=(0,o.useState)(!1),{props:A,meta:S}=(0,u.getImgProps)(t,{defaultLoader:p.default,imgConf:r,blurComplete:g,showAltText:b});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y,{...A,unoptimized:S.unoptimized,placeholder:S.placeholder,fill:S.fill,onLoadRef:m,onLoadingCompleteRef:v,setBlurComplete:x,setShowAltText:P,sizesInput:t.sizes,ref:e}),S.priority?(0,s.jsx)(w,{isAppRouter:!i,imgAttributes:A}):null]})});("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},4219:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},4274:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},4633:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))})},4648:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))})},5029:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return o}});let n=i(2115),r=n.useLayoutEffect,s=n.useEffect;function o(t){let{headManager:e,reduceComponentsToState:i}=t;function o(){if(e&&e.mountedInstances){let r=n.Children.toArray(Array.from(e.mountedInstances).filter(Boolean));e.updateHead(i(r,t))}}return r(()=>{var i;return null==e||null==(i=e.mountedInstances)||i.add(t.children),()=>{var i;null==e||null==(i=e.mountedInstances)||i.delete(t.children)}}),r(()=>(e&&(e._pendingUpdate=o),()=>{e&&(e._pendingUpdate=o)})),s(()=>(e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null),()=>{e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null)})),null}},5100:(t,e)=>{function i(t){let{widthInt:e,heightInt:i,blurWidth:n,blurHeight:r,blurDataURL:s,objectFit:o}=t,a=n?40*n:e,l=r?40*r:i,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},5493:(t,e,i)=>{let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{P:()=>sp});let s=t=>Array.isArray(t);function o(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function a(t){return"string"==typeof t||Array.isArray(t)}function l(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,n){if("function"==typeof e){let[r,s]=l(n);e=e(void 0!==i?i:t.custom,r,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,s]=l(n);e=e(void 0!==i?i:t.custom,r,s)}return e}function h(t,e,i){let n=t.getProps();return u(n,e,void 0!==i?i:n.custom,t)}let d=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],c=["initial",...d];function p(t){let e;return()=>(void 0===e&&(e=t()),e)}let m=p(()=>void 0!==window.ScrollTimeline);class f{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>m()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class v extends f{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function g(t,e){return t?t[e]||t.default||t:void 0}function y(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function w(t){return"function"==typeof t}function x(t,e){t.timeline=e,t.onfinish=null}let b=t=>Array.isArray(t)&&"number"==typeof t[0],P={linearEasing:void 0},A=function(t,e){let i=p(t);return()=>{var t;return null!=(t=P[e])?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),S=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n},E=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t(S(0,r-1,e))+", ";return`linear(${n.substring(0,n.length-2)})`},T=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,M={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:T([0,.65,.55,1]),circOut:T([.55,0,1,.45]),backIn:T([.31,.01,.66,-.59]),backOut:T([.33,1.53,.69,.99])},k={x:!1,y:!1};function C(t,e){let i=function(t,e,i){if(t instanceof Element)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function j(t){return e=>{"touch"===e.pointerType||k.x||k.y||t(e)}}let V=(t,e)=>!!e&&(t===e||V(t,e.parentElement)),R=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,L=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),D=new WeakSet;function O(t){return e=>{"Enter"===e.key&&t(e)}}function F(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let B=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=O(()=>{if(D.has(i))return;F(i,"down");let t=O(()=>{F(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>F(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function I(t){return R(t)&&!(k.x||k.y)}let _=t=>1e3*t,U=t=>t/1e3,W=t=>t,N=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],z=new Set(N),$=new Set(["width","height","top","left","right","bottom",...N]),H=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),Z=t=>s(t)?t[t.length-1]||0:t,Y={skipAnimations:!1,useManualTiming:!1},X=["read","resolveKeyframes","update","preRender","render","postRender"];function q(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=X.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,n=!1,r=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){s.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,r=!1,o=!1)=>{let a=o&&n?e:i;return r&&s.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),s.delete(t)},process:t=>{if(o=t,n){r=!0;return}n=!0,[e,i]=[i,e],e.forEach(a),e.clear(),n=!1,r&&(r=!1,l.process(t))}};return l}(s),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:d,postRender:c}=o,p=()=>{let s=Y.useManualTiming?r.timestamp:performance.now();i=!1,r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),h.process(r),d.process(r),c.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(p))},m=()=>{i=!0,n=!0,r.isProcessing||t(p)};return{schedule:X.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||m(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<X.length;e++)o[X[e]].cancel(t)},state:r,steps:o}}let{schedule:K,cancel:G,state:J,steps:Q}=q("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:W,!0);function tt(){n=void 0}let te={now:()=>(void 0===n&&te.set(J.isProcessing||Y.useManualTiming?J.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(tt)}};function ti(t,e){-1===t.indexOf(e)&&t.push(e)}function tn(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class tr{constructor(){this.subscriptions=[]}add(t){return ti(this.subscriptions,t),()=>tn(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ts=t=>!isNaN(parseFloat(t)),to={current:void 0};class ta{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=te.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=te.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=ts(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new tr);let i=this.events[t].add(e);return"change"===t?()=>{i(),K.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return to.current&&to.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=te.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tl(t,e){return new ta(t,e)}let tu=t=>!!(t&&t.getVelocity);function th(t,e){let i=t.getValue("willChange");if(tu(i)&&i.add)return i.add(e)}let td=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tc="data-"+td("framerAppearId"),tp={current:!1},tm=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tf(t,e,i,n){if(t===e&&i===n)return W;let r=e=>(function(t,e,i,n,r){let s,o,a=0;do(s=tm(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tm(r(t),e,n)}let tv=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tg=t=>e=>1-t(1-e),ty=tf(.33,1.53,.69,.99),tw=tg(ty),tx=tv(tw),tb=t=>(t*=2)<1?.5*tw(t):.5*(2-Math.pow(2,-10*(t-1))),tP=t=>1-Math.sin(Math.acos(t)),tA=tg(tP),tS=tv(tP),tE=t=>/^0[^.\s]+$/u.test(t),tT=(t,e,i)=>i>e?e:i<t?t:i,tM={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tk={...tM,transform:t=>tT(0,1,t)},tC={...tM,default:1},tj=t=>Math.round(1e5*t)/1e5,tV=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tR=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tL=(t,e)=>i=>!!("string"==typeof i&&tR.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tD=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(tV);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tO=t=>tT(0,255,t),tF={...tM,transform:t=>Math.round(tO(t))},tB={test:tL("rgb","red"),parse:tD("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tF.transform(t)+", "+tF.transform(e)+", "+tF.transform(i)+", "+tj(tk.transform(n))+")"},tI={test:tL("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tB.transform},t_=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tU=t_("deg"),tW=t_("%"),tN=t_("px"),tz=t_("vh"),t$=t_("vw"),tH={...tW,parse:t=>tW.parse(t)/100,transform:t=>tW.transform(100*t)},tZ={test:tL("hsl","hue"),parse:tD("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tW.transform(tj(e))+", "+tW.transform(tj(i))+", "+tj(tk.transform(n))+")"},tY={test:t=>tB.test(t)||tI.test(t)||tZ.test(t),parse:t=>tB.test(t)?tB.parse(t):tZ.test(t)?tZ.parse(t):tI.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tB.transform(t):tZ.transform(t)},tX=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tq="number",tK="color",tG=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tJ(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tG,t=>(tY.test(t)?(n.color.push(s),r.push(tK),i.push(tY.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tq),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tQ(t){return tJ(t).values}function t0(t){let{split:e,types:i}=tJ(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tq?r+=tj(t[s]):e===tK?r+=tY.transform(t[s]):r+=t[s]}return r}}let t1=t=>"number"==typeof t?0:t,t2={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(tV))?void 0:e.length)||0)+((null==(i=t.match(tX))?void 0:i.length)||0)>0},parse:tQ,createTransformer:t0,getAnimatableNone:function(t){let e=tQ(t);return t0(t)(e.map(t1))}},t5=new Set(["brightness","contrast","saturate","opacity"]);function t3(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tV)||[];if(!n)return t;let r=i.replace(n,""),s=+!!t5.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let t4=/\b([a-z-]*)\(.*?\)/gu,t9={...t2,getAnimatableNone:t=>{let e=t.match(t4);return e?e.map(t3).join(" "):t}},t6={...tM,transform:Math.round},t7={borderWidth:tN,borderTopWidth:tN,borderRightWidth:tN,borderBottomWidth:tN,borderLeftWidth:tN,borderRadius:tN,radius:tN,borderTopLeftRadius:tN,borderTopRightRadius:tN,borderBottomRightRadius:tN,borderBottomLeftRadius:tN,width:tN,maxWidth:tN,height:tN,maxHeight:tN,top:tN,right:tN,bottom:tN,left:tN,padding:tN,paddingTop:tN,paddingRight:tN,paddingBottom:tN,paddingLeft:tN,margin:tN,marginTop:tN,marginRight:tN,marginBottom:tN,marginLeft:tN,backgroundPositionX:tN,backgroundPositionY:tN,rotate:tU,rotateX:tU,rotateY:tU,rotateZ:tU,scale:tC,scaleX:tC,scaleY:tC,scaleZ:tC,skew:tU,skewX:tU,skewY:tU,distance:tN,translateX:tN,translateY:tN,translateZ:tN,x:tN,y:tN,z:tN,perspective:tN,transformPerspective:tN,opacity:tk,originX:tH,originY:tH,originZ:tN,zIndex:t6,size:tN,fillOpacity:tk,strokeOpacity:tk,numOctaves:t6},t8={...t7,color:tY,backgroundColor:tY,outlineColor:tY,fill:tY,stroke:tY,borderColor:tY,borderTopColor:tY,borderRightColor:tY,borderBottomColor:tY,borderLeftColor:tY,filter:t9,WebkitFilter:t9},et=t=>t8[t];function ee(t,e){let i=et(t);return i!==t9&&(i=t2),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let ei=new Set(["auto","none","0"]),en=t=>t===tM||t===tN,er=(t,e)=>parseFloat(t.split(", ")[e]),es=(t,e)=>(i,{transform:n})=>{if("none"===n||!n)return 0;let r=n.match(/^matrix3d\((.+)\)$/u);if(r)return er(r[1],e);{let e=n.match(/^matrix\((.+)\)$/u);return e?er(e[1],t):0}},eo=new Set(["x","y","z"]),ea=N.filter(t=>!eo.has(t)),el={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:es(4,13),y:es(5,14)};el.translateX=el.x,el.translateY=el.y;let eu=new Set,eh=!1,ed=!1;function ec(){if(ed){let t=Array.from(eu).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ea.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var n;null==(n=t.getValue(e))||n.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ed=!1,eh=!1,eu.forEach(t=>t.complete()),eu.clear()}function ep(){eu.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ed=!0)})}class em{constructor(t,e,i,n,r,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eu.add(this),eh||(eh=!0,K.read(ep),K.resolveKeyframes(ec))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;for(let r=0;r<t.length;r++)if(null===t[r])if(0===r){let r=null==n?void 0:n.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}else t[r]=t[r-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let ef=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ev=t=>e=>"string"==typeof e&&e.startsWith(t),eg=ev("--"),ey=ev("var(--"),ew=t=>!!ey(t)&&ex.test(t.split("/*")[0].trim()),ex=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,eb=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eP=t=>e=>e.test(t),eA=[tM,tN,tW,tU,t$,tz,{test:t=>"auto"===t,parse:t=>t}],eS=t=>eA.find(eP(t));class eE extends em{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&ew(n=n.trim())){let r=function t(e,i,n=1){W(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=eb.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${null!=i?i:n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return ef(t)?parseFloat(t):t}return ew(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!$.has(i)||2!==t.length)return;let[n,r]=t,s=eS(n),o=eS(r);if(s!==o)if(en(s)&&en(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tE(n))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!ei.has(e)&&tJ(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=ee(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=el[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(i);r&&r.jump(this.measuredOrigin,!1);let s=n.length-1,o=n[s];n[s]=el[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eT=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t2.test(t)||"0"===t)&&!t.startsWith("url(")),eM=t=>null!==t;function ek(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(eM),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return s&&void 0!==n?n:r[s]}class eC{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=te.now(),this.options={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ep(),ec()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=te.now(),this.hasAttemptedResolve=!0;let{name:i,type:n,velocity:r,delay:s,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eT(r,e),a=eT(s,e);return W(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||w(i))&&n)}(t,i,n,r))if(tp.current||!s){a&&a(ek(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}else this.options.duration=0;let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let ej=(t,e,i)=>t+(e-t)*i;function eV(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eR(t,e){return i=>i>0?e:t}let eL=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},eD=[tI,tB,tZ],eO=t=>eD.find(e=>e.test(t));function eF(t){let e=eO(t);if(W(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tZ&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=eV(a,n,t+1/3),s=eV(a,n,t),o=eV(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let eB=(t,e)=>{let i=eF(t),n=eF(e);if(!i||!n)return eR(t,e);let r={...i};return t=>(r.red=eL(i.red,n.red,t),r.green=eL(i.green,n.green,t),r.blue=eL(i.blue,n.blue,t),r.alpha=ej(i.alpha,n.alpha,t),tB.transform(r))},eI=(t,e)=>i=>e(t(i)),e_=(...t)=>t.reduce(eI),eU=new Set(["none","hidden"]);function eW(t,e){return i=>ej(t,e,i)}function eN(t){return"number"==typeof t?eW:"string"==typeof t?ew(t)?eR:tY.test(t)?eB:eH:Array.isArray(t)?ez:"object"==typeof t?tY.test(t)?eB:e$:eR}function ez(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>eN(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function e$(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=eN(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let eH=(t,e)=>{let i=t2.createTransformer(e),n=tJ(t),r=tJ(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?eU.has(t)&&!r.values.length||eU.has(e)&&!n.values.length?function(t,e){return eU.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):e_(ez(function(t,e){var i;let n=[],r={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let o=e.types[s],a=t.indexes[o][r[o]],l=null!=(i=t.values[a])?i:0;n[s]=l,r[o]++}return n}(n,r),r.values),i):(W(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eR(t,e))};function eZ(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?ej(t,e,i):eN(t)(t,e)}function eY(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let eX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eq(t,e){return t*Math.sqrt(1-e*e)}let eK=["duration","bounce"],eG=["stiffness","damping","mass"];function eJ(t,e){return e.some(e=>void 0!==t[e])}function eQ(t=eX.visualDuration,e=eX.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:eX.velocity,stiffness:eX.stiffness,damping:eX.damping,mass:eX.mass,isResolvedFromDuration:!1,...t};if(!eJ(t,eG)&&eJ(t,eK))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*tT(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:eX.mass,stiffness:n,damping:r}}else{let i=function({duration:t=eX.duration,bounce:e=eX.bounce,velocity:i=eX.velocity,mass:n=eX.mass}){let r,s;W(t<=_(eX.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tT(eX.minDamping,eX.maxDamping,o),t=tT(eX.minDuration,eX.maxDuration,U(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/eq(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=eq(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=_(t),isNaN(a))return{stiffness:eX.stiffness,damping:eX.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:eX.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-U(n.velocity||0)}),f=p||0,v=h/(2*Math.sqrt(u*d)),g=a-o,w=U(Math.sqrt(u/d)),x=5>Math.abs(g);if(r||(r=x?eX.restSpeed.granular:eX.restSpeed.default),s||(s=x?eX.restDelta.granular:eX.restDelta.default),v<1){let t=eq(w,v);i=e=>a-Math.exp(-v*w*e)*((f+v*w*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===v)i=t=>a-Math.exp(-w*t)*(g+(f+w*g)*t);else{let t=w*Math.sqrt(v*v-1);i=e=>{let i=Math.exp(-v*w*e),n=Math.min(t*e,300);return a-i*((f+v*w*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}let b={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0;v<1&&(n=0===t?_(f):eY(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(y(b),2e4),e=E(e=>b.next(t*e).value,t,30);return t+"ms "+e}};return b}function e0({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,y=p+g,w=void 0===o?y:o(y);w!==y&&(g=w-p);let x=t=>-g*Math.exp(-t/n),b=t=>w+x(t),P=t=>{let e=x(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?w:i},A=t=>{f(m.value)&&(d=t,c=eQ({keyframes:[m.value,v(m.value)],velocity:eY(b,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,P(t),A(t)),void 0!==d&&t>=d)?c.next(t-d):(e||P(t),m)}}}let e1=tf(.42,0,1,1),e2=tf(0,0,.58,1),e5=tf(.42,0,.58,1),e3=t=>Array.isArray(t)&&"number"!=typeof t[0],e4={linear:W,easeIn:e1,easeInOut:e5,easeOut:e2,circIn:tP,circInOut:tS,circOut:tA,backIn:tw,backInOut:tx,backOut:ty,anticipate:tb},e9=t=>{if(b(t)){W(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tf(e,i,n,r)}return"string"==typeof t?(W(void 0!==e4[t],`Invalid easing type '${t}'`),e4[t]):t};function e6({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=e3(n)?n.map(e9):e9(n),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(W(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||eZ,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=e_(Array.isArray(e)?e[i]||W:e,s)),n.push(s)}return n}(e,n,r),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=S(t[n],t[n+1],i);return a[n](r)};return i?e=>u(tT(t[0],t[s-1],e)):u}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=S(0,e,n);t.push(ej(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||e5).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let e7=t=>{let e=({timestamp:e})=>t(e);return{start:()=>K.update(e,!0),stop:()=>G(e),now:()=>J.isProcessing?J.timestamp:te.now()}},e8={decay:e0,inertia:e0,tween:e6,keyframes:e6,spring:eQ},it=t=>t/100;class ie extends eC{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:n,keyframes:r}=this.options,s=(null==n?void 0:n.KeyframeResolver)||em;this.resolver=new s(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i,{type:n="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:o,velocity:a=0}=this.options,l=w(n)?n:e8[n]||e6;l!==e6&&"number"!=typeof t[0]&&(e=e_(it,eZ(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=y(u));let{calculatedDuration:h}=u,d=h+s;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(r+1)-s}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:n,generator:r,mirroredGenerator:s,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return r.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,w=r;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(w=s)),y=tT(0,1,i)*h}let x=g?{done:!1,value:a[0]}:w.next(y);o&&(x.value=o(x.value));let{done:b}=x;g||null===l||(b=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return P&&void 0!==n&&(x.value=ek(a,this.options,n)),f&&f(x.value),P&&this.finish(),x}get duration(){let{resolved:t}=this;return t?U(t.calculatedDuration):0}get time(){return U(this.currentTime)}set time(t){t=_(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=U(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e7,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(t=this.currentTime)?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let ii=new Set(["opacity","clipPath","filter","transform"]),ir=p(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),is={anticipate:tb,backInOut:tx,circInOut:tS};class io extends eC{constructor(t){super(t);let{name:e,motionValue:i,element:n,keyframes:r}=this.options;this.resolver=new eE(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:n=300,times:r,ease:s,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof s&&A()&&s in is&&(s=is[s]),w((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&A()||!e||"string"==typeof e&&(e in M||A())||b(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new ie({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:t[0]},r=[],s=0;for(;!n.done&&s<2e4;)r.push((n=i.sample(s)).value),s+=10;return{times:void 0,keyframes:r,duration:s-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),n=h.duration,r=h.times,s=h.ease,o="keyframes"}let h=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&A()?E(e,i):b(e)?T(e):Array.isArray(e)?e.map(e=>t(e,i)||M.easeOut):M[e]}(a,r);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:n,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:n,times:r,ease:s});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(x(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(ek(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:n,times:r,type:o,ease:s,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return U(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return U(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=_(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return W;let{animation:i}=e;x(i,t)}else this.pendingTimeline=t;return W}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:n,type:r,ease:s,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new ie({...u,keyframes:i,duration:n,type:r,ease:s,times:o,isGenerator:!0}),d=_(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ir()&&i&&ii.has(i)&&!a&&!l&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}}let ia={type:"spring",stiffness:500,damping:25,restSpeed:10},il=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),iu={type:"keyframes",duration:.8},ih={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},id=(t,{keyframes:e})=>e.length>2?iu:z.has(t)?t.startsWith("scale")?il(e[1]):ia:ih,ic=(t,e,i,n={},r,s)=>o=>{let a=g(n,t)||{},l=a.delay||n.delay||0,{elapsed:u=0}=n;u-=_(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...id(t,h)}),h.duration&&(h.duration=_(h.duration)),h.repeatDelay&&(h.repeatDelay=_(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(d=!0)),(tp.current||Y.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),d&&!s&&void 0!==e.get()){let t=ek(h.keyframes,a);if(void 0!==t)return K.update(()=>{h.onUpdate(t),h.onComplete()}),new v([])}return!s&&io.supports(h)?new io(h):new ie(h)};function ip(t,e,{delay:i=0,transitionOverride:n,type:r}={}){var s;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;n&&(o=n);let u=[],d=r&&t.animationState&&t.animationState.getState()[r];for(let e in l){let n=t.getValue(e,null!=(s=t.latestValues[e])?s:null),r=l[e];if(void 0===r||d&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(d,e))continue;let a={delay:i,...g(o||{},e)},h=!1;if(window.MotionHandoffAnimation){let i=t.props[tc];if(i){let t=window.MotionHandoffAnimation(i,e,K);null!==t&&(a.startTime=t,h=!0)}}th(t,e),n.start(ic(e,n,r,t.shouldReduceMotion&&$.has(e)?{type:!1}:a,t,h));let c=n.animation;c&&u.push(c)}return a&&Promise.all(u).then(()=>{K.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=h(t,e)||{};for(let e in r={...r,...i}){let i=Z(r[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,tl(i))}}(t,a)})}),u}function im(t,e,i={}){var n;let r=h(t,e,"exit"===i.type?null==(n=t.presenceContext)?void 0:n.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(s=i.transitionOverride);let o=r?()=>Promise.all(ip(t,r,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(iv).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(im(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,r+n,o,a,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function iv(t,e){return t.sortNodePosition(e)}let ig=c.length,iy=[...d].reverse(),iw=d.length;function ix(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ib(){return{animate:ix(!0),whileInView:ix(),whileHover:ix(),whileTap:ix(),whileDrag:ix(),whileFocus:ix(),exit:ix()}}class iP{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iA extends iP{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>im(t,e,i)));else if("string"==typeof e)n=im(t,e,i);else{let r="function"==typeof e?h(t,e,i.custom):e;n=Promise.all(ip(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=ib(),n=!0,l=e=>(i,n)=>{var r;let s=h(t,n,"exit"===e?null==(r=t.presenceContext)?void 0:r.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function u(u){let{props:h}=t,d=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ig;t++){let n=c[t],r=e.props[n];(a(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},p=[],m=new Set,f={},v=1/0;for(let e=0;e<iw;e++){var g,y;let c=iy[e],w=i[c],x=void 0!==h[c]?h[c]:d[c],b=a(x),P=c===u?w.isActive:null;!1===P&&(v=e);let A=x===d[c]&&x!==h[c]&&b;if(A&&n&&t.manuallyAnimateOnMount&&(A=!1),w.protectedKeys={...f},!w.isActive&&null===P||!x&&!w.prevProp||r(x)||"boolean"==typeof x)continue;let S=(g=w.prevProp,"string"==typeof(y=x)?y!==g:!!Array.isArray(y)&&!o(y,g)),E=S||c===u&&w.isActive&&!A&&b||e>v&&b,T=!1,M=Array.isArray(x)?x:[x],k=M.reduce(l(c),{});!1===P&&(k={});let{prevResolvedValues:C={}}=w,j={...C,...k},V=e=>{E=!0,m.has(e)&&(T=!0,m.delete(e)),w.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in j){let e=k[t],i=C[t];if(f.hasOwnProperty(t))continue;let n=!1;(s(e)&&s(i)?o(e,i):e===i)?void 0!==e&&m.has(t)?V(t):w.protectedKeys[t]=!0:null!=e?V(t):m.add(t)}w.prevProp=x,w.prevResolvedValues=k,w.isActive&&(f={...f,...k}),n&&t.blockInitialAnimation&&(E=!1);let R=!(A&&S)||T;E&&R&&p.push(...M.map(t=>({animation:t,options:{type:c}})))}if(m.size){let e={};m.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=null!=n?n:null}),p.push({animation:e})}let w=!!p.length;return n&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(w=!1),n=!1,w?e(p):Promise.resolve()}return{animateChanges:u,setActive:function(e,n){var r;if(i[e].isActive===n)return Promise.resolve();null==(r=t.variantChildren)||r.forEach(t=>{var i;return null==(i=t.animationState)?void 0:i.setActive(e,n)}),i[e].isActive=n;let s=u(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=ib(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}let iS=0;class iE extends iP{constructor(){super(...arguments),this.id=iS++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function iT(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}function iM(t){return{point:{x:t.pageX,y:t.pageY}}}let ik=t=>e=>R(e)&&t(e,iM(e));function iC(t,e,i,n){return iT(t,e,ik(i),n)}let ij=(t,e)=>Math.abs(t-e);class iV{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iD(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(ij(t.x,e.x)**2+ij(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=J;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iR(e,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iD("pointercancel"===t.type?this.lastMoveEventInfo:iR(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!R(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=iR(iM(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=J;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iD(s,this.history)),this.removeListeners=e_(iC(this.contextWindow,"pointermove",this.handlePointerMove),iC(this.contextWindow,"pointerup",this.handlePointerUp),iC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),G(this.updatePoint)}}function iR(t,e){return e?{point:e(t.point)}:t}function iL(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iD({point:t},e){return{point:t,delta:iL(t,iO(e)),offset:iL(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iO(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>_(.1)));)i--;if(!n)return{x:0,y:0};let s=U(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iO(t){return t[t.length-1]}function iF(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function iB(t){return t.max-t.min}function iI(t,e,i,n=.5){t.origin=n,t.originPoint=ej(e.min,e.max,t.origin),t.scale=iB(i)/iB(e),t.translate=ej(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function i_(t,e,i,n){iI(t.x,e.x,i.x,n?n.originX:void 0),iI(t.y,e.y,i.y,n?n.originY:void 0)}function iU(t,e,i){t.min=i.min+e.min,t.max=t.min+iB(e)}function iW(t,e,i){t.min=e.min-i.min,t.max=t.min+iB(e)}function iN(t,e,i){iW(t.x,e.x,i.x),iW(t.y,e.y,i.y)}function iz(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i$(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function iH(t,e,i){return{min:iZ(t,e),max:iZ(t,i)}}function iZ(t,e){return"number"==typeof t?t:t[e]||0}let iY=()=>({translate:0,scale:1,origin:0,originPoint:0}),iX=()=>({x:iY(),y:iY()}),iq=()=>({min:0,max:0}),iK=()=>({x:iq(),y:iq()});function iG(t){return[t("x"),t("y")]}function iJ({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iQ(t){return void 0===t||1===t}function i0({scale:t,scaleX:e,scaleY:i}){return!iQ(t)||!iQ(e)||!iQ(i)}function i1(t){return i0(t)||i2(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function i2(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i5(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function i3(t,e=0,i=1,n,r){t.min=i5(t.min,e,i,n,r),t.max=i5(t.max,e,i,n,r)}function i4(t,{x:e,y:i}){i3(t.x,e.translate,e.scale,e.originPoint),i3(t.y,i.translate,i.scale,i.originPoint)}function i9(t,e){t.min=t.min+e,t.max=t.max+e}function i6(t,e,i,n,r=.5){let s=ej(t.min,t.max,r);i3(t,e,i,s,n)}function i7(t,e){i6(t.x,e.x,e.scaleX,e.scale,e.originX),i6(t.y,e.y,e.scaleY,e.scale,e.originY)}function i8(t,e){return iJ(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let nt=({current:t})=>t?t.ownerDocument.defaultView:null,ne=new WeakMap;class ni{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iK(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iV(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iM(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(k[t])return null;else return k[t]=!0,()=>{k[t]=!1};return k.x||k.y?null:(k.x=k.y=!0,()=>{k.x=k.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iG(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tW.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iB(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&K.postRender(()=>r(t,e)),th(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iG(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:nt(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&K.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!nn(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?ej(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?ej(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,r=this.constraints;e&&iF(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iz(t.x,i,r),y:iz(t.y,e,n)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iH(t,"left","right"),y:iH(t,"top","bottom")}}(i),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&iG(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iF(e))return!1;let n=e.current;W(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=i8(t,i),{scroll:r}=e;return r&&(i9(n.x,r.offset.x),i9(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:i$(t.x,s.x),y:i$(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iJ(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iG(o=>{if(!nn(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return th(this.visualElement,t),i.start(ic(t,i,0,e,this.visualElement,!1))}stopAnimation(){iG(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iG(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iG(e=>{let{drag:i}=this.getProps();if(!nn(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-ej(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iF(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iG(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iB(t),r=iB(e);return r>n?i=S(e.min,e.max-n,t.min):n>r&&(i=S(t.min,t.max-r,e.min)),tT(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iG(e=>{if(!nn(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(ej(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;ne.set(this.visualElement,this);let t=iC(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iF(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),K.read(e);let r=iT(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iG(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function nn(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class nr extends iP{constructor(t){super(t),this.removeGroupControls=W,this.removeListeners=W,this.controls=new ni(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||W}unmount(){this.removeGroupControls(),this.removeListeners()}}let ns=t=>(e,i)=>{t&&K.postRender(()=>t(e,i))};class no extends iP{constructor(){super(...arguments),this.removePointerDownListener=W}onPointerDown(t){this.session=new iV(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nt(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:ns(t),onStart:ns(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&K.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iC(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var na,nl,nu=i(5155),nh=i(2115);let nd=(0,nh.createContext)(null),nc=(0,nh.createContext)({}),np=(0,nh.createContext)({}),nm={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nf(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let nv={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tN.test(t))return t;else t=parseFloat(t);let i=nf(t,e.target.x),n=nf(t,e.target.y);return`${i}% ${n}%`}},ng={},{schedule:ny,cancel:nw}=q(queueMicrotask,!1);class nx extends nh.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;Object.assign(ng,nP),r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nm.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,s=i.projection;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||K.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ny.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nb(t){let[e,i]=function(t=!0){let e=(0,nh.useContext)(nd);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:n,register:r}=e,s=(0,nh.useId)();(0,nh.useEffect)(()=>{t&&r(s)},[t]);let o=(0,nh.useCallback)(()=>t&&n&&n(s),[s,n,t]);return!i&&n?[!1,o]:[!0]}(),n=(0,nh.useContext)(nc);return(0,nu.jsx)(nx,{...t,layoutGroup:n,switchLayoutGroup:(0,nh.useContext)(np),isPresent:e,safeToRemove:i})}let nP={borderRadius:{...nv,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nv,borderTopRightRadius:nv,borderBottomLeftRadius:nv,borderBottomRightRadius:nv,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=t2.parse(t);if(n.length>5)return t;let r=t2.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=ej(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}},nA=(t,e)=>t.depth-e.depth;class nS{constructor(){this.children=[],this.isDirty=!1}add(t){ti(this.children,t),this.isDirty=!0}remove(t){tn(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nA),this.isDirty=!1,this.children.forEach(t)}}function nE(t){let e=tu(t)?t.get():t;return H(e)?e.toValue():e}let nT=["TopLeft","TopRight","BottomLeft","BottomRight"],nM=nT.length,nk=t=>"string"==typeof t?parseFloat(t):t,nC=t=>"number"==typeof t||tN.test(t);function nj(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nV=nL(0,.5,tA),nR=nL(.5,.95,W);function nL(t,e,i){return n=>n<t?0:n>e?1:i(S(t,e,n))}function nD(t,e){t.min=e.min,t.max=e.max}function nO(t,e){nD(t.x,e.x),nD(t.y,e.y)}function nF(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nB(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nI(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(tW.test(e)&&(e=parseFloat(e),e=ej(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=ej(s.min,s.max,n);t===s&&(a-=e),t.min=nB(t.min,e,i,a,r),t.max=nB(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let n_=["x","scaleX","originX"],nU=["y","scaleY","originY"];function nW(t,e,i,n){nI(t.x,e,n_,i?i.x:void 0,n?n.x:void 0),nI(t.y,e,nU,i?i.y:void 0,n?n.y:void 0)}function nN(t){return 0===t.translate&&1===t.scale}function nz(t){return nN(t.x)&&nN(t.y)}function n$(t,e){return t.min===e.min&&t.max===e.max}function nH(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nZ(t,e){return nH(t.x,e.x)&&nH(t.y,e.y)}function nY(t){return iB(t.x)/iB(t.y)}function nX(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nq{constructor(){this.members=[]}add(t){ti(this.members,t),t.scheduleRender()}remove(t){if(tn(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nK={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},nG="undefined"!=typeof window&&void 0!==window.MotionDebug,nJ=["","X","Y","Z"],nQ={visibility:"hidden"},n0=0;function n1(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function n2({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=null==e?void 0:e()){this.id=n0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nG&&(nK.totalNodes=nK.resolvedTargetDeltas=nK.recalculatedProjection=0),this.nodes.forEach(n4),this.nodes.forEach(ri),this.nodes.forEach(rn),this.nodes.forEach(n9),nG&&window.MotionDebug.record(nK)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nS)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tr),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:n,layout:r,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||n)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=te.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(G(n),t(s-e))};return K.read(n,!0),()=>G(n)}(n,250),nm.hasAnimatedSinceResize&&(nm.hasAnimatedSinceResize=!1,this.nodes.forEach(re))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||ru,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!nZ(this.targetLayout,n)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...g(r,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||re(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,G(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rr),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[tc];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",K,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n7);return}this.isUpdating||this.nodes.forEach(n8),this.isUpdating=!1,this.nodes.forEach(rt),this.nodes.forEach(n5),this.nodes.forEach(n3),this.clearAllSnapshots();let t=te.now();J.delta=tT(0,1e3/60,t-J.timestamp),J.timestamp=t,J.isProcessing=!0,Q.update.process(J),Q.preRender.process(J),Q.render.process(J),J.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ny.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n6),this.sharedNodes.forEach(rs)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iK(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nz(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&(e||i1(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),rc((e=n).x),rc(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return iK();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(rm))){let{scroll:t}=this.root;t&&(i9(i.x,t.offset.x),i9(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=iK();if(nO(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let n=this.path[e],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nO(i,t),i9(i.x,r.offset.x),i9(i.y,r.offset.y))}return i}applyTransform(t,e=!1){let i=iK();nO(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i7(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),i1(n.latestValues)&&i7(i,n.latestValues)}return i1(this.latestValues)&&i7(i,this.latestValues),i}removeTransform(t){let e=iK();nO(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!i1(i.latestValues))continue;i0(i.latestValues)&&i.updateSnapshot();let n=iK();nO(n,i.measurePageBox()),nW(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return i1(this.latestValues)&&nW(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==J.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,n,r;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=J.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iK(),this.relativeTargetOrigin=iK(),iN(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iK(),this.targetWithTransforms=iK()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,n=this.relativeTarget,r=this.relativeParent.target,iU(i.x,n.x,r.x),iU(i.y,n.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nO(this.target,this.layout.layoutBox),i4(this.target,this.targetDelta)):nO(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iK(),this.relativeTargetOrigin=iK(),iN(this.relativeTargetOrigin,this.target,t.target),nO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nG&&nK.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||i0(this.parent.latestValues)||i2(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(n=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===J.timestamp&&(n=!1),n)return;let{layout:r,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||s))return;nO(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let r,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i7(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,i4(t,s)),n&&i1(r.latestValues)&&i7(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iK());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nF(this.prevProjectionDelta.x,this.projectionDelta.x),nF(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),i_(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&nX(this.projectionDelta.x,this.prevProjectionDelta.x)&&nX(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nG&&nK.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iX(),this.projectionDelta=iX(),this.projectionDeltaWithTransform=iX()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iX();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iK(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(rl));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(ro(o.x,t.x,n),ro(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,v;iN(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,v=n,ra(p.x,m.x,f.x,v),ra(p.y,m.y,f.y,v),i&&(u=this.relativeTarget,c=i,n$(u.x,c.x)&&n$(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iK()),nO(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=ej(0,void 0!==i.opacity?i.opacity:1,nV(n)),t.opacityExit=ej(void 0!==e.opacity?e.opacity:1,0,nR(n))):s&&(t.opacity=ej(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,n));for(let r=0;r<nM;r++){let s=`border${nT[r]}Radius`,o=nj(e,s),a=nj(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nC(o)===nC(a)?(t[s]=Math.max(ej(nk(o),nk(a),n),0),(tW.test(a)||tW.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=ej(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(G(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{nm.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let n=tu(0)?0:tl(t);return n.start(ic("",n,1e3,i)),n.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&rp(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iK();let e=iB(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iB(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nO(e,i),i7(e,r),i_(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nq),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&n1("z",t,n,this.animationValues);for(let e=0;e<nJ.length;e++)n1(`rotate${nJ[e]}`,t,n,this.animationValues),n1(`skew${nJ[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nQ;let n={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=nE(null==t?void 0:t.pointerEvents)||"",n.transform=r?r(this.latestValues,""):"none",n;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nE(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!i1(this.latestValues)&&(e.transform=r?r({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),n.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),r&&(n.transform=r(o,n.transform));let{x:a,y:l}=this.projectionDelta;for(let t in n.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?n.opacity=s===this?null!=(i=null!=(e=o.opacity)?e:this.latestValues.opacity)?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:n.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,ng){if(void 0===o[t])continue;let{correct:e,applyTo:i}=ng[t],r="none"===n.transform?o[t]:e(o[t],s);if(i){let t=i.length;for(let e=0;e<t;e++)n[i[e]]=r}else n[t]=r}return this.options.layoutId&&(n.pointerEvents=s===this?nE(null==t?void 0:t.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(n7),this.root.sharedNodes.clear()}}}function n5(t){t.updateLayout()}function n3(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:r}=t.options,s=i.source!==t.layout.source;"size"===r?iG(t=>{let n=s?i.measuredBox[t]:i.layoutBox[t],r=iB(n);n.min=e[t].min,n.max=n.min+r}):rp(r,i.layoutBox,e)&&iG(n=>{let r=s?i.measuredBox[n]:i.layoutBox[n],o=iB(e[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iX();i_(o,e,i.layoutBox);let a=iX();s?i_(a,t.applyTransform(n,!0),i.measuredBox):i_(a,e,i.layoutBox);let l=!nz(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iK();iN(o,i.layoutBox,r.layoutBox);let a=iK();iN(a,e,s.layoutBox),nZ(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function n4(t){nG&&nK.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function n9(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function n6(t){t.clearSnapshot()}function n7(t){t.clearMeasurements()}function n8(t){t.isLayoutDirty=!1}function rt(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function re(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ri(t){t.resolveTargetDelta()}function rn(t){t.calcProjection()}function rr(t){t.resetSkewAndRotation()}function rs(t){t.removeLeadSnapshot()}function ro(t,e,i){t.translate=ej(e.translate,0,i),t.scale=ej(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function ra(t,e,i,n){t.min=ej(e.min,i.min,n),t.max=ej(e.max,i.max,n)}function rl(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let ru={duration:.45,ease:[.4,0,.1,1]},rh=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rd=rh("applewebkit/")&&!rh("chrome/")?Math.round:W;function rc(t){t.min=rd(t.min),t.max=rd(t.max)}function rp(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nY(e)-nY(i)))}function rm(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let rf=n2({attachResizeListener:(t,e)=>iT(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rv={current:void 0},rg=n2({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rv.current){let t=new rf({});t.mount(window),t.setOptions({layoutScroll:!0}),rv.current=t}return rv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ry(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&K.postRender(()=>r(e,iM(e)))}class rw extends iP{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=C(t,i),o=j(t=>{let{target:i}=t,n=e(t);if("function"!=typeof n||!i)return;let s=j(t=>{n(t),i.removeEventListener("pointerleave",s)});i.addEventListener("pointerleave",s,r)});return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,t=>(ry(this.node,t,"Start"),t=>ry(this.node,t,"End"))))}unmount(){}}class rx extends iP{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=e_(iT(this.node.current,"focus",()=>this.onFocus()),iT(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function rb(t,e,i){let{props:n}=t;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&K.postRender(()=>r(e,iM(e)))}class rP extends iP{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=C(t,i),o=t=>{let n=t.currentTarget;if(!I(t)||D.has(n))return;D.add(n);let s=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),I(t)&&D.has(n)&&(D.delete(n),"function"==typeof s&&s(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||V(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{L.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),t.addEventListener("focus",t=>B(t,r),r)}),s}(t,t=>(rb(this.node,t,"Start"),(t,{success:e})=>rb(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rA=new WeakMap,rS=new WeakMap,rE=t=>{let e=rA.get(t.target);e&&e(t)},rT=t=>{t.forEach(rE)},rM={some:0,all:1};class rk extends iP{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rM[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rS.has(i)||rS.set(i,{});let n=rS.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rT,{root:t,...e})),n[r]}(e);return rA.set(t,i),n.observe(t),()=>{rA.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rC=(0,nh.createContext)({strict:!1}),rj=(0,nh.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),rV=(0,nh.createContext)({});function rR(t){return r(t.animate)||c.some(e=>a(t[e]))}function rL(t){return!!(rR(t)||t.variants)}function rD(t){return Array.isArray(t)?t.join(" "):t}let rO="undefined"!=typeof window,rF={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rB={};for(let t in rF)rB[t]={isEnabled:e=>rF[t].some(t=>!!e[t])};let rI=Symbol.for("motionComponentSymbol"),r_=rO?nh.useLayoutEffect:nh.useEffect,rU=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rW(t){if("string"!=typeof t||t.includes("-"));else if(rU.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let rN=t=>(e,i)=>{let n=(0,nh.useContext)(rV),s=(0,nh.useContext)(nd),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},n,s,o){let a={latestValues:function(t,e,i,n){let s={},o=n(t,{});for(let t in o)s[t]=nE(o[t]);let{initial:a,animate:l}=t,h=rR(t),d=rL(t);e&&d&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?l:a;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=u(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(n,s,o,t),renderState:e()};return i&&(a.onMount=t=>i({props:n,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,n,s);return i?o():function(t){let e=(0,nh.useRef)(null);return null===e.current&&(e.current=t()),e.current}(o)},rz=(t,e)=>e&&"number"==typeof t?e.transform(t):t,r$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rH=N.length;function rZ(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(z.has(t)){o=!0;continue}if(eg(t)){r[t]=i;continue}{let e=rz(i,t7[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rH;s++){let o=N[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rz(a,t7[o]);if(!l){r=!1;let e=r$[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rY={offset:"stroke-dashoffset",array:"stroke-dasharray"},rX={offset:"strokeDashoffset",array:"strokeDasharray"};function rq(t,e,i){return"string"==typeof t?t:tN.transform(e+i*t)}function rK(t,{attrX:e,attrY:i,attrScale:n,originX:r,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(rZ(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==r||void 0!==s||p.transform)&&(p.transformOrigin=function(t,e,i){let n=rq(e,t.x,t.width),r=rq(i,t.y,t.height);return`${n} ${r}`}(m,void 0!==r?r:.5,void 0!==s?s:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==o&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rY:rX;t[s.offset]=tN.transform(-n);let o=tN.transform(e),a=tN.transform(i);t[s.array]=`${o} ${a}`}(c,o,a,l,!1)}let rG=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),rJ=()=>({...rG(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase();function r0(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}let r1=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function r2(t,e,i,n){for(let i in r0(t,e,void 0,n),e.attrs)t.setAttribute(r1.has(i)?i:td(i),e.attrs[i])}function r5(t,{layout:e,layoutId:i}){return z.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ng[t]||"opacity"===t)}function r3(t,e,i){var n;let{style:r}=t,s={};for(let o in r)(tu(r[o])||e.style&&tu(e.style[o])||r5(o,t)||(null==(n=null==i?void 0:i.getValue(o))?void 0:n.liveStyle)!==void 0)&&(s[o]=r[o]);return s}function r4(t,e,i){let n=r3(t,e,i);for(let i in t)(tu(t[i])||tu(e[i]))&&(n[-1!==N.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r9=["x","y","width","height","cx","cy","r"],r6={useVisualState:rN({scrapeMotionValuesFromProps:r4,createRenderState:rJ,onUpdate:({props:t,prevProps:e,current:i,renderState:n,latestValues:r})=>{if(!i)return;let s=!!t.drag;if(!s){for(let t in r)if(z.has(t)){s=!0;break}}if(!s)return;let o=!e;if(e)for(let i=0;i<r9.length;i++){let n=r9[i];t[n]!==e[n]&&(o=!0)}o&&K.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,n),K.render(()=>{rK(n,r,rQ(i.tagName),t.transformTemplate),r2(i,n)})})}})},r7={useVisualState:rN({scrapeMotionValuesFromProps:r3,createRenderState:rG})};function r8(t,e,i){for(let n in e)tu(e[n])||r5(n,i)||(t[n]=e[n])}let st=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function se(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||st.has(t)}let si=t=>!se(t);try{!function(t){t&&(si=e=>e.startsWith("on")?!se(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let sn={current:null},sr={current:!1},ss=[...eA,tY,t2],so=t=>ss.find(eP(t)),sa=new WeakMap,sl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class su{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=em,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=te.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,K.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=s;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rR(e),this.isVariantNode=rL(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&tu(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sa.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sr.current||function(){if(sr.current=!0,rO)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sn.current=t.matches;t.addListener(e),e()}else sn.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sn.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in sa.delete(this.current),this.projection&&this.projection.unmount(),G(this.notifyUpdate),G(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=z.has(t),r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&K.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rB){let e=rB[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iK()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sl.length;e++){let i=sl[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(tu(r))t.addValue(n,r);else if(tu(s))t.addValue(n,tl(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,tl(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=tl(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let n=void 0===this.latestValues[t]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,t))?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(ef(n)||tE(n))?n=parseFloat(n):!so(n)&&t2.test(e)&&(n=ee(t,e)),this.setBaseTarget(t,tu(n)?n.get():n)),tu(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=u(this.props,n,null==(e=this.presenceContext)?void 0:e.custom);r&&(i=r[t])}if(n&&void 0!==i)return i;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||tu(r)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new tr),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sh extends su{constructor(){super(...arguments),this.KeyframeResolver=eE}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tu(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class sd extends sh{constructor(){super(...arguments),this.type="html",this.renderInstance=r0}readValueFromInstance(t,e){if(z.has(e)){let t=et(e);return t&&t.default||0}{let i=window.getComputedStyle(t),n=(eg(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i8(t,e)}build(t,e,i){rZ(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r3(t,e,i)}}class sc extends sh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iK}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(z.has(e)){let t=et(e);return t&&t.default||0}return e=r1.has(e)?e:td(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r4(t,e,i)}build(t,e,i){rK(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,n){r2(t,e,i,n)}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}}let sp=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((na={animation:{Feature:iA},exit:{Feature:iE},inView:{Feature:rk},tap:{Feature:rP},focus:{Feature:rx},hover:{Feature:rw},pan:{Feature:no},drag:{Feature:nr,ProjectionNode:rg,MeasureLayout:nb},layout:{ProjectionNode:rg,MeasureLayout:nb}},nl=(t,e)=>rW(t)?new sc(e):new sd(e,{allowProjection:t!==nh.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:o,Component:l}=t;function u(t,e){var i,n,u;let h,d={...(0,nh.useContext)(rj),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nh.useContext)(nc).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=d,p=function(t){let{initial:e,animate:i}=function(t,e){if(rR(t)){let{initial:e,animate:i}=t;return{initial:!1===e||a(e)?e:void 0,animate:a(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nh.useContext)(rV));return(0,nh.useMemo)(()=>({initial:e,animate:i}),[rD(e),rD(i)])}(t),m=o(t,c);if(!c&&rO){n=0,u=0,(0,nh.useContext)(rC).strict;let t=function(t){let{drag:e,layout:i}=rB;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(d);h=t.MeasureLayout,p.visualElement=function(t,e,i,n,r){var s,o;let{visualElement:a}=(0,nh.useContext)(rV),l=(0,nh.useContext)(rC),u=(0,nh.useContext)(nd),h=(0,nh.useContext)(rj).reducedMotion,d=(0,nh.useRef)(null);n=n||l.renderer,!d.current&&n&&(d.current=n(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,nh.useContext)(np);c&&!c.projection&&r&&("html"===c.type||"svg"===c.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iF(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:u})}(d.current,i,r,p);let m=(0,nh.useRef)(!1);(0,nh.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[tc],v=(0,nh.useRef)(!!f&&!(null==(s=window.MotionHandoffIsComplete)?void 0:s.call(window,f))&&(null==(o=window.MotionHasOptimisedAnimation)?void 0:o.call(window,f)));return r_(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),ny.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),(0,nh.useEffect)(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null==(t=window.MotionHandoffMarkAsComplete)||t.call(window,f)}),v.current=!1))}),c}(l,m,d,r,t.ProjectionNode)}return(0,nu.jsxs)(rV.Provider,{value:p,children:[h&&p.visualElement?(0,nu.jsx)(h,{visualElement:p.visualElement,...d}):null,s(l,t,(i=p.visualElement,(0,nh.useCallback)(t=>{t&&m.onMount&&m.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iF(e)&&(e.current=t))},[i])),m,c,p.visualElement)]})}n&&function(t){for(let e in t)rB[e]={...rB[e],...t[e]}}(n),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!=(i=null!=(e=l.displayName)?e:l.name)?i:"",")"));let h=(0,nh.forwardRef)(u);return h[rI]=l,h}({...rW(t)?r6:r7,preloadedFeatures:na,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(rW(e)?function(t,e,i,n){let r=(0,nh.useMemo)(()=>{let i=rJ();return rK(i,e,rQ(n),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};r8(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return r8(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nh.useMemo)(()=>{let i=rG();return rZ(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(si(r)||!0===i&&se(r)||!e&&!se(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nh.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,nh.useMemo)(()=>tu(u)?u.get():u,[u]);return(0,nh.createElement)(e,{...l,children:h})}}(e),createVisualElement:nl,Component:t})}))},5500:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},5529:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))})},5564:(t,e,i)=>{var n=i(9509);Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return v},defaultHead:function(){return c}});let r=i(8229),s=i(6966),o=i(5155),a=s._(i(2115)),l=r._(i(5029)),u=i(2464),h=i(2830),d=i(7544);function c(t){void 0===t&&(t=!1);let e=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return t||e.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),e}function p(t,e){return"string"==typeof e||"number"==typeof e?t:e.type===a.default.Fragment?t.concat(a.default.Children.toArray(e.props.children).reduce((t,e)=>"string"==typeof e||"number"==typeof e?t:t.concat(e),[])):t.concat(e)}i(3230);let m=["name","httpEquiv","charSet","itemProp"];function f(t,e){let{inAmpMode:i}=e;return t.reduce(p,[]).reverse().concat(c(i).reverse()).filter(function(){let t=new Set,e=new Set,i=new Set,n={};return r=>{let s=!0,o=!1;if(r.key&&"number"!=typeof r.key&&r.key.indexOf("$")>0){o=!0;let e=r.key.slice(r.key.indexOf("$")+1);t.has(e)?s=!1:t.add(e)}switch(r.type){case"title":case"base":e.has(r.type)?s=!1:e.add(r.type);break;case"meta":for(let t=0,e=m.length;t<e;t++){let e=m[t];if(r.props.hasOwnProperty(e))if("charSet"===e)i.has(e)?s=!1:i.add(e);else{let t=r.props[e],i=n[e]||new Set;("name"!==e||!o)&&i.has(t)?s=!1:(i.add(t),n[e]=i)}}}return s}}()).reverse().map((t,e)=>{let r=t.key||e;if(n.env.__NEXT_OPTIMIZE_FONTS&&!i&&"link"===t.type&&t.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(e=>t.props.href.startsWith(e))){let e={...t.props||{}};return e["data-href"]=e.href,e.href=void 0,e["data-optimized-fonts"]=!0,a.default.cloneElement(t,e)}return a.default.cloneElement(t,{key:r})})}let v=function(t){let{children:e}=t,i=(0,a.useContext)(u.AmpStateContext),n=(0,a.useContext)(h.HeadManagerContext);return(0,o.jsx)(l.default,{reduceComponentsToState:f,headManager:n,inAmpMode:(0,d.isInAmpMode)(i),children:e})};("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},5628:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},5840:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return n}});let i=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6654:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(2115);function r(t,e){let i=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let t=i.current;t&&(i.current=null,t());let e=r.current;e&&(r.current=null,e())}else t&&(i.current=s(t,n)),e&&(r.current=s(e,n))},[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6752:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ImageConfigContext",{enumerable:!0,get:function(){return s}});let n=i(8229)._(i(2115)),r=i(5840),s=n.default.createContext(r.imageConfigDefault)},6766:(t,e,i)=>{i.d(e,{default:()=>r.a});var n=i(1469),r=i.n(n)},6865:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},7305:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))})},7544:(t,e)=>{function i(t){let{ampFirst:e=!1,hybrid:i=!1,hasQuery:n=!1}=void 0===t?{}:t;return e||i&&n}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isInAmpMode",{enumerable:!0,get:function(){return i}})},7572:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))})},8246:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},8593:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})},8673:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"}))})},8883:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImgProps",{enumerable:!0,get:function(){return l}}),i(3230);let n=i(5100),r=i(5840),s=["-moz-initial","fill","none","scale-down",void 0];function o(t){return void 0!==t.default}function a(t){return void 0===t?t:"number"==typeof t?Number.isFinite(t)?t:NaN:"string"==typeof t&&/^[0-9]+$/.test(t)?parseInt(t,10):NaN}function l(t,e){var i,l;let u,h,d,{src:c,sizes:p,unoptimized:m=!1,priority:f=!1,loading:v,className:g,quality:y,width:w,height:x,fill:b=!1,style:P,overrideSrc:A,onLoad:S,onLoadingComplete:E,placeholder:T="empty",blurDataURL:M,fetchPriority:k,decoding:C="async",layout:j,objectFit:V,objectPosition:R,lazyBoundary:L,lazyRoot:D,...O}=t,{imgConf:F,showAltText:B,blurComplete:I,defaultLoader:_}=e,U=F||r.imageConfigDefault;if("allSizes"in U)u=U;else{let t=[...U.deviceSizes,...U.imageSizes].sort((t,e)=>t-e),e=U.deviceSizes.sort((t,e)=>t-e),n=null==(i=U.qualities)?void 0:i.sort((t,e)=>t-e);u={...U,allSizes:t,deviceSizes:e,qualities:n}}if(void 0===_)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let W=O.loader||_;delete O.loader,delete O.srcSet;let N="__next_img_default"in W;if(N){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let t=W;W=e=>{let{config:i,...n}=e;return t(n)}}if(j){"fill"===j&&(b=!0);let t={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[j];t&&(P={...P,...t});let e={responsive:"100vw",fill:"100vw"}[j];e&&!p&&(p=e)}let z="",$=a(w),H=a(x);if((l=c)&&"object"==typeof l&&(o(l)||void 0!==l.src)){let t=o(c)?c.default:c;if(!t.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(t)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!t.height||!t.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(t)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(h=t.blurWidth,d=t.blurHeight,M=M||t.blurDataURL,z=t.src,!b)if($||H){if($&&!H){let e=$/t.width;H=Math.round(t.height*e)}else if(!$&&H){let e=H/t.height;$=Math.round(t.width*e)}}else $=t.width,H=t.height}let Z=!f&&("lazy"===v||void 0===v);(!(c="string"==typeof c?c:z)||c.startsWith("data:")||c.startsWith("blob:"))&&(m=!0,Z=!1),u.unoptimized&&(m=!0),N&&!u.dangerouslyAllowSVG&&c.split("?",1)[0].endsWith(".svg")&&(m=!0);let Y=a(y),X=Object.assign(b?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:V,objectPosition:R}:{},B?{}:{color:"transparent"},P),q=I||"empty"===T?null:"blur"===T?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:$,heightInt:H,blurWidth:h,blurHeight:d,blurDataURL:M||"",objectFit:X.objectFit})+'")':'url("'+T+'")',K=s.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,G=q?{backgroundSize:K,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:q}:{},J=function(t){let{config:e,src:i,unoptimized:n,width:r,quality:s,sizes:o,loader:a}=t;if(n)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(t,e,i){let{deviceSizes:n,allSizes:r}=t;if(i){let t=/(^|\s)(1?\d?\d)vw/g,e=[];for(let n;n=t.exec(i);)e.push(parseInt(n[2]));if(e.length){let t=.01*Math.min(...e);return{widths:r.filter(e=>e>=n[0]*t),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof e?{widths:n,kind:"w"}:{widths:[...new Set([e,2*e].map(t=>r.find(e=>e>=t)||r[r.length-1]))],kind:"x"}}(e,r,o),h=l.length-1;return{sizes:o||"w"!==u?o:"100vw",srcSet:l.map((t,n)=>a({config:e,src:i,quality:s,width:t})+" "+("w"===u?t:n+1)+u).join(", "),src:a({config:e,src:i,quality:s,width:l[h]})}}({config:u,src:c,unoptimized:m,width:$,quality:Y,sizes:p,loader:W});return{props:{...O,loading:Z?"lazy":v,fetchPriority:k,width:$,height:H,decoding:C,className:g,style:{...X,...G},sizes:J.sizes,srcSet:J.srcSet,src:A||J.src},meta:{unoptimized:m,priority:f,placeholder:T,fill:b}}}},8960:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},9994:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))})}}]);