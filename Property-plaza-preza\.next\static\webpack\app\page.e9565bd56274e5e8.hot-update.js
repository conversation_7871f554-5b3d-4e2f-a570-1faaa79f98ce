"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/sections/Metrics.tsx":
/*!**********************************!*\
  !*** ./app/sections/Metrics.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,CalendarIcon,ChartBarIcon,ChatBubbleLeftRightIcon,EyeIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,CalendarIcon,ChartBarIcon,ChatBubbleLeftRightIcon,EyeIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,CalendarIcon,ChartBarIcon,ChatBubbleLeftRightIcon,EyeIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,CalendarIcon,ChartBarIcon,ChatBubbleLeftRightIcon,EyeIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,CalendarIcon,ChartBarIcon,ChatBubbleLeftRightIcon,EyeIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,CalendarIcon,ChartBarIcon,ChatBubbleLeftRightIcon,EyeIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Metrics = ()=>{\n    const metrics = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 16,\n                columnNumber: 13\n            }, undefined),\n            title: \"Reach Growth\",\n            value: \"5K+\",\n            description: \"Average reach per post\",\n            trend: \"+45%\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 23,\n                columnNumber: 13\n            }, undefined),\n            title: \"Engagement\",\n            value: \"400+\",\n            description: \"Total engagements\",\n            trend: \"+60%\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 30,\n                columnNumber: 13\n            }, undefined),\n            title: \"Qualified Leads\",\n            value: \"75+\",\n            description: \"Qualified inquiries\",\n            trend: \"+80%\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 37,\n                columnNumber: 13\n            }, undefined),\n            title: \"Conversions\",\n            value: \"15+\",\n            description: \"Consultation bookings\",\n            trend: \"+120%\"\n        }\n    ];\n    const campaignStructure = [\n        {\n            title: \"12 Week Duration\",\n            description: \"Extended post to measure deep market impact and ROI\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 49,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            title: \"7 Posts Per Week\",\n            description: \"High-quality content across TikTok, Instagram & LinkedIn\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 54,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            title: \"±2M IDR/Week\",\n            description: \"Professional editor + enhanced posting budget\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 59,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            title: \"Advanced Analytics\",\n            description: \"Google Data Studio + TikTok Analytics + IG Insights\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_CalendarIcon_ChartBarIcon_ChatBubbleLeftRightIcon_EyeIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                lineNumber: 64,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-gray-50 py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            className: \"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6\",\n                            children: \"Results & Timeline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            className: \"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Campaign Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" & Timeline\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            className: \"text-xl text-muted max-w-3xl mx-auto\",\n                            children: \"Measurable outcomes and structured timeline for the 12-week campaign with comprehensive analytics tracking.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\",\n                    children: campaignStructure.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl p-6 shadow-lg border border-gray-100 text-center hover:shadow-xl transition-shadow duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-primary mb-4 flex justify-center\",\n                                    children: item.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-default mb-3\",\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted leading-relaxed\",\n                                    children: item.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-default mb-8 text-center\",\n                            children: \"Expected Campaign Results\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: metrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1 * index\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-primary mb-4 flex justify-center\",\n                                            children: metric.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-default mb-2\",\n                                            children: metric.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted mb-2\",\n                                            children: metric.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-semibold text-green-600\",\n                                            children: metric.trend\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-default mb-8 text-center\",\n                            children: \"12-Week Campaign Timeline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm\",\n                                            children: \"1-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-xl font-semibold text-default mb-3\",\n                                                    children: \"Legal & Process Foundation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-4 leading-relaxed\",\n                                                    children: \"Establish credibility through comprehensive legal expertise and transparent processes. Build investor confidence through detailed guidance on property acquisition, permits, and regulatory compliance in Bali's complex legal landscape.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                            children: \"Leasehold vs. Freehold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                            children: \"Transfer Requirements\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                            children: \"Legal Fees\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                            children: \"Tax Implications\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-accent to-primary rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm\",\n                                            children: \"5-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-xl font-semibold text-default mb-3\",\n                                                    children: \"Material & Build Quality + Cost Breakdown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-4 leading-relaxed\",\n                                                    children: \"Comprehensive analysis of construction materials, building techniques, and detailed cost breakdowns. Demonstrate expertise in sustainable practices while providing transparent pricing for informed investment decisions.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm\",\n                                                            children: \"Timber vs. Concrete Pros/Cons\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm\",\n                                                            children: \"Build Cost per m\\xb2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm\",\n                                                            children: \"Hidden Costs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm\",\n                                                            children: \"Infrastructure & Permits\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.3\n                                    },\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm\",\n                                            children: \"9-12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-xl font-semibold text-default mb-3\",\n                                                    children: \"Tenant & ROI + Investor Profiles + Local Trust Network\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-4 leading-relaxed\",\n                                                    children: \"Comprehensive focus on investment returns, tenant management, and our trusted local network. Showcase proven track record with detailed investor profiles and success stories while highlighting our vetted partner ecosystem.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                            children: \"Rental Yield vs. Capital Growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                            children: \"Private Expats & Families\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                            children: \"Vetted Architects & Lawyers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm\",\n                                                            children: \"Builder Verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            className: \"mt-12 pt-8 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-default mb-6 text-center\",\n                                    children: \"Multi-Platform Content Distribution\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center space-x-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-br from-purple-400 via-pink-500 to-orange-500 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 bg-white rounded-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-default\",\n                                                    children: \"Instagram\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-black rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 bg-white rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-default\",\n                                                    children: \"TikTok\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-center text-sm text-muted mt-4\",\n                                    children: \"Consistent brand messaging across all platforms with platform-specific content optimization\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Metrics;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Metrics);\nvar _c;\n$RefreshReg$(_c, \"Metrics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/sections/Metrics.tsx\n"));

/***/ })

});