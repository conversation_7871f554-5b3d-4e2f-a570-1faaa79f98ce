(()=>{var e={};e.id=974,e.ids=[974],e.modules={597:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>m});var r=i(7413),n=i(677),s=i(2492),a=i(3398),o=i(1199),l=i(4056),d=i(6289),c=i(2117),u=i(2674),h=i(5331),p=i(6151);function m(){return(0,r.jsxs)("main",{className:"relative",children:[(0,r.jsx)(p.default,{}),(0,r.jsx)(n.default,{}),(0,r.jsx)(s.default,{}),(0,r.jsx)(a.default,{}),(0,r.jsx)(o.default,{}),(0,r.jsx)(l.default,{}),(0,r.jsx)(d.default,{}),(0,r.jsx)(c.default,{}),(0,r.jsx)(u.default,{}),(0,r.jsx)(h.default,{})]})}},677:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Hero.tsx","default")},679:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});var r=i(3210);let n=r.forwardRef(function({title:e,titleId:t,...i},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"}))})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1199:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\AboutPropertyPlaza.tsx","default")},1790:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),n=i(4156),s=i(3210);let a=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),o=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))});var l=i(3635),d=i(5994);let c=()=>{let e=[{icon:a,number:"30",label:"Days Since Launch",description:"Fresh platform with modern approach"},{icon:o,number:"30+",label:"Active Listings",description:"Curated properties across Bali"},{icon:l.A,number:"350+",label:"Platform Visitors",description:"Growing international interest"},{icon:d.A,number:"3",label:"Languages Supported",description:"Dutch, German & English"}];return(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-20",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6",children:(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsx)(n.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6",children:"About Us"}),(0,r.jsxs)(n.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight",children:[(0,r.jsx)("span",{className:"gradient-text",children:"Property Plaza"}),(0,r.jsx)("br",{}),"Early Success Story"]}),(0,r.jsx)(n.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-xl text-muted mb-8 leading-relaxed",children:"In just 30 days, we've built a platform that's already attracting serious international buyers and quality listings. Our multilingual, AI-assisted approach is proving the market demand for transparency."}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-default mb-4",children:"Platform Features"}),(0,r.jsxs)("ul",{className:"space-y-3",children:[(0,r.jsxs)("li",{className:"flex items-center text-muted",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Multilingual property descriptions (NL/DE/EN)"]}),(0,r.jsxs)("li",{className:"flex items-center text-muted",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"AI-assisted legal document analysis"]}),(0,r.jsxs)("li",{className:"flex items-center text-muted",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Direct buyer-seller connections"]}),(0,r.jsxs)("li",{className:"flex items-center text-muted",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Transparent pricing structure"]})]})]})]}),(0,r.jsx)(n.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"grid grid-cols-2 gap-6",children:e.map((e,t)=>(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3+.1*t},viewport:{once:!0},whileHover:{scale:1.05},className:"bg-white rounded-xl p-6 text-center shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,r.jsx)(n.P.div,{className:"text-3xl font-bold text-primary mb-2",initial:{scale:0},whileInView:{scale:1},transition:{duration:.5,delay:.5+.1*t},children:e.number}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-default mb-2",children:e.label}),(0,r.jsx)("p",{className:"text-sm text-muted",children:e.description})]},t))})]})})})}},2117:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\PilotCampaign.tsx","default")},2205:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,7924,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},2492:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\MarketProblem.tsx","default")},2623:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),n=i(4156),s=i(6524),a=i(3210);let o=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))});var l=i(679);let d=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))}),c=()=>{let e=[{icon:s.A,title:"Transparency",subtitle:"No surprises, clear legal & price structure",description:"Every property listing includes complete legal documentation, clear ownership structure, and transparent pricing with no hidden fees.",color:"from-blue-500 to-blue-600",bgColor:"bg-blue-50",textColor:"text-blue-600"},{icon:o,title:"Knowledge",subtitle:"Guidance in your language",description:"Expert guidance provided in Dutch, German, and English, ensuring you understand every aspect of your property investment.",color:"from-green-500 to-green-600",bgColor:"bg-green-50",textColor:"text-green-600"},{icon:l.A,title:"Connection",subtitle:"We link buyers, sellers & experts directly",description:"Direct connections between serious buyers, verified sellers, and trusted legal experts, eliminating unnecessary intermediaries.",color:"from-purple-500 to-purple-600",bgColor:"bg-purple-50",textColor:"text-purple-600"},{icon:d,title:"Empowerment",subtitle:"When people understand, they act confidently",description:"Armed with complete information and expert guidance, you can make property decisions with complete confidence.",color:"from-primary to-accent",bgColor:"bg-primary/5",textColor:"text-primary"}];return(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-background py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-6",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsx)(n.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6",children:"Our Foundation"}),(0,r.jsxs)(n.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-4xl md:text-5xl font-bold text-default mb-6",children:["The ",(0,r.jsx)("span",{className:"gradient-text",children:"Four Pillars"})," of Trust"]}),(0,r.jsx)(n.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-xl text-muted max-w-3xl mx-auto",children:"Our mission is built on four fundamental principles that transform how property decisions are made in Bali."})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:e.map((e,t)=>(0,r.jsx)(n.P.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},whileHover:{y:-10,scale:1.02},className:"group cursor-pointer",children:(0,r.jsxs)("div",{className:`${e.bgColor} rounded-2xl p-8 h-full border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-xl`,children:[(0,r.jsx)(n.P.div,{className:`w-16 h-16 bg-gradient-to-br ${e.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`,children:(0,r.jsx)(e.icon,{className:"w-8 h-8 text-white"})}),(0,r.jsx)("h3",{className:`text-2xl font-bold ${e.textColor} mb-3`,children:e.title}),(0,r.jsx)("p",{className:"text-default font-semibold mb-4 text-sm",children:e.subtitle}),(0,r.jsx)("p",{className:"text-muted leading-relaxed text-sm",children:e.description}),(0,r.jsx)(n.P.div,{className:`mt-6 w-full h-1 bg-gradient-to-r ${e.color} rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300`})]})},t))}),(0,r.jsx)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},className:"text-center mt-16",children:(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 text-muted",children:[(0,r.jsx)("div",{className:"flex space-x-1",children:e.map((e,t)=>(0,r.jsx)(n.P.div,{className:"w-2 h-2 bg-primary rounded-full",animate:{scale:[1,1.2,1]},transition:{duration:2,delay:.2*t,repeat:1/0}},t))}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Building trust through action"})]})})]})})}},2674:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Metrics.tsx","default")},2704:()=>{},2969:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});var r=i(3210);let n=r.forwardRef(function({title:e,titleId:t,...i},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3398:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\FourPillars.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\FourPillars.tsx","default")},3418:(e,t,i)=>{Promise.resolve().then(i.bind(i,5617)),Promise.resolve().then(i.bind(i,1790)),Promise.resolve().then(i.bind(i,5990)),Promise.resolve().then(i.bind(i,2623)),Promise.resolve().then(i.bind(i,7179)),Promise.resolve().then(i.bind(i,5097)),Promise.resolve().then(i.bind(i,8931)),Promise.resolve().then(i.bind(i,4120)),Promise.resolve().then(i.bind(i,6108)),Promise.resolve().then(i.bind(i,4433))},3635:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});var r=i(3210);let n=r.forwardRef(function({title:e,titleId:t,...i},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},3773:()=>{},3873:e=>{"use strict";e.exports=require("path")},4056:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\WhyParadiseIndonesia.tsx","default")},4120:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),n=i(4156),s=i(2969),a=i(3210);let o=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}),l=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var d=i(5994);let c=()=>{let e=[{icon:s.A,title:"4 Week Duration",description:"Focused pilot to measure impact and ROI"},{icon:o,title:"5 Posts Per Week",description:"High-quality content across all platforms"},{icon:l,title:"\xb11.5M IDR/Week",description:"Professional editor + posting budget"},{icon:d.A,title:"Full Analytics",description:"Google Data Studio tracking & reporting"}];return(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-6",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsx)(n.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6",children:"Pilot Campaign"}),(0,r.jsxs)(n.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight",children:[(0,r.jsx)("span",{className:"gradient-text",children:"4-Week"})," Pilot Campaign Plan"]}),(0,r.jsx)(n.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-xl text-muted max-w-3xl mx-auto",children:"A focused campaign to establish our partnership and measure real market impact."})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-start",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-default mb-8",children:"Campaign Structure"}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-6 mb-12",children:e.map((e,t)=>(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-default mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-muted",children:e.description})]},t))}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},className:"bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-6 border border-primary/20",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-default mb-4",children:"Content Topics"}),(0,r.jsx)("ul",{className:"space-y-3",children:["Leasehold vs Ownership Explained","Avoiding Legal Pitfalls in Bali","What Dutch Buyers Should Know","German Investment Guidelines","Property Due Diligence Checklist"].map((e,t)=>(0,r.jsxs)(n.P.li,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.4,delay:.6+.1*t},className:"flex items-center text-muted",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),e]},t))})]})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-default mb-8",children:"Social Media Preview"}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-6",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-primary to-accent h-48 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-white",children:[(0,r.jsx)("h4",{className:"text-xl font-bold mb-2",children:"Leasehold Explained"}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:"What every foreign buyer needs to know"})]})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full mr-3"}),(0,r.jsx)("span",{className:"font-semibold text-sm",children:"paradise_indonesia"})]}),(0,r.jsxs)("p",{className:"text-sm text-muted",children:["Understanding leasehold vs freehold in Bali property investment. Our expert guide breaks down the legal framework...",(0,r.jsx)("span",{className:"text-primary",children:"#BaliProperty #LegalAdvice"})]})]})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.7},className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-default mb-4",children:"Expected Metrics"}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-primary",children:"2K+"}),(0,r.jsx)("div",{className:"text-sm text-muted",children:"Reach per post"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-primary",children:"150+"}),(0,r.jsx)("div",{className:"text-sm text-muted",children:"Engagements"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-primary",children:"25+"}),(0,r.jsx)("div",{className:"text-sm text-muted",children:"Inquiries"})]})]})]})]})]})]})})}},4156:(e,t,i)=>{"use strict";let r;function n(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}i.d(t,{P:()=>sp});let s=e=>Array.isArray(e);function a(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let r=0;r<i;r++)if(t[r]!==e[r])return!1;return!0}function o(e){return"string"==typeof e||Array.isArray(e)}function l(e){let t=[{},{}];return null==e||e.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function d(e,t,i,r){if("function"==typeof t){let[n,s]=l(r);t=t(void 0!==i?i:e.custom,n,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[n,s]=l(r);t=t(void 0!==i?i:e.custom,n,s)}return t}function c(e,t,i){let r=e.getProps();return d(r,t,void 0!==i?i:r.custom,e)}let u=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],h=["initial",...u];function p(e){let t;return()=>(void 0===t&&(t=e()),t)}let m=p(()=>void 0!==window.ScrollTimeline);class f{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let i=0;i<this.animations.length;i++)this.animations[i][e]=t}attachTimeline(e,t){let i=this.animations.map(i=>m()&&i.attachTimeline?i.attachTimeline(e):"function"==typeof t?t(i):void 0);return()=>{i.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class y extends f{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function g(e,t){return e?e[t]||e.default||e:void 0}function x(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function v(e){return"function"==typeof e}function b(e,t){e.timeline=t,e.onfinish=null}let w=e=>Array.isArray(e)&&"number"==typeof e[0],P={linearEasing:void 0},j=function(e,t){let i=p(e);return()=>{var e;return null!=(e=P[t])?e:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),A=(e,t,i)=>{let r=t-e;return 0===r?1:(i-e)/r},T=(e,t,i=10)=>{let r="",n=Math.max(Math.round(t/i),2);for(let t=0;t<n;t++)r+=e(A(0,n-1,t))+", ";return`linear(${r.substring(0,r.length-2)})`},N=([e,t,i,r])=>`cubic-bezier(${e}, ${t}, ${i}, ${r})`,E={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:N([0,.65,.55,1]),circOut:N([.55,0,1,.45]),backIn:N([.31,.01,.66,-.59]),backOut:N([.33,1.53,.69,.99])},k={x:!1,y:!1};function C(e,t){let i=function(e,t,i){if(e instanceof Element)return[e];if("string"==typeof e){let t=document.querySelectorAll(e);return t?Array.from(t):[]}return Array.from(e)}(e),r=new AbortController;return[i,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function S(e){return t=>{"touch"===t.pointerType||k.x||k.y||e(t)}}let V=(e,t)=>!!t&&(e===t||V(e,t.parentElement)),M=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,R=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),L=new WeakSet;function D(e){return t=>{"Enter"===t.key&&e(t)}}function I(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let B=(e,t)=>{let i=e.currentTarget;if(!i)return;let r=D(()=>{if(L.has(i))return;I(i,"down");let e=D(()=>{I(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>I(i,"cancel"),t)});i.addEventListener("keydown",r,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),t)};function F(e){return M(e)&&!(k.x||k.y)}let z=e=>1e3*e,O=e=>e/1e3,W=e=>e,U=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],$=new Set(U),H=new Set(["width","height","top","left","right","bottom",...U]),_=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),q=e=>s(e)?e[e.length-1]||0:e,K={skipAnimations:!1,useManualTiming:!1},G=["read","resolveKeyframes","update","preRender","render","postRender"];function Y(e,t){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=G.reduce((e,t)=>(e[t]=function(e){let t=new Set,i=new Set,r=!1,n=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1};function o(t){s.has(t)&&(l.schedule(t),e()),t(a)}let l={schedule:(e,n=!1,a=!1)=>{let o=a&&r?t:i;return n&&s.add(e),o.has(e)||o.add(e),e},cancel:e=>{i.delete(e),s.delete(e)},process:e=>{if(a=e,r){n=!0;return}r=!0,[t,i]=[i,t],t.forEach(o),t.clear(),r=!1,n&&(n=!1,l.process(e))}};return l}(s),e),{}),{read:o,resolveKeyframes:l,update:d,preRender:c,render:u,postRender:h}=a,p=()=>{let s=K.useManualTiming?n.timestamp:performance.now();i=!1,n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1),n.timestamp=s,n.isProcessing=!0,o.process(n),l.process(n),d.process(n),c.process(n),u.process(n),h.process(n),n.isProcessing=!1,i&&t&&(r=!1,e(p))},m=()=>{i=!0,r=!0,n.isProcessing||e(p)};return{schedule:G.reduce((e,t)=>{let r=a[t];return e[t]=(e,t=!1,n=!1)=>(i||m(),r.schedule(e,t,n)),e},{}),cancel:e=>{for(let t=0;t<G.length;t++)a[G[t]].cancel(e)},state:n,steps:a}}let{schedule:Z,cancel:X,state:J,steps:Q}=Y("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:W,!0);function ee(){r=void 0}let et={now:()=>(void 0===r&&et.set(J.isProcessing||K.useManualTiming?J.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(ee)}};function ei(e,t){-1===e.indexOf(t)&&e.push(t)}function er(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class en{constructor(){this.subscriptions=[]}add(e){return ei(this.subscriptions,e),()=>er(this.subscriptions,e)}notify(e,t,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let es=e=>!isNaN(parseFloat(e)),ea={current:void 0};class eo{constructor(e,t={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=et.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=et.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=es(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new en);let i=this.events[e].add(t);return"change"===e?()=>{i(),Z.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ea.current&&ea.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=et.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function el(e,t){return new eo(e,t)}let ed=e=>!!(e&&e.getVelocity);function ec(e,t){let i=e.getValue("willChange");if(ed(i)&&i.add)return i.add(t)}let eu=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eh="data-"+eu("framerAppearId"),ep={current:!1},em=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function ef(e,t,i,r){if(e===t&&i===r)return W;let n=t=>(function(e,t,i,r,n){let s,a,o=0;do(s=em(a=t+(i-t)/2,r,n)-e)>0?i=a:t=a;while(Math.abs(s)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:em(n(e),t,r)}let ey=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,eg=e=>t=>1-e(1-t),ex=ef(.33,1.53,.69,.99),ev=eg(ex),eb=ey(ev),ew=e=>(e*=2)<1?.5*ev(e):.5*(2-Math.pow(2,-10*(e-1))),eP=e=>1-Math.sin(Math.acos(e)),ej=eg(eP),eA=ey(eP),eT=e=>/^0[^.\s]+$/u.test(e),eN=(e,t,i)=>i>t?t:i<e?e:i,eE={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},ek={...eE,transform:e=>eN(0,1,e)},eC={...eE,default:1},eS=e=>Math.round(1e5*e)/1e5,eV=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eM=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eR=(e,t)=>i=>!!("string"==typeof i&&eM.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),eL=(e,t,i)=>r=>{if("string"!=typeof r)return r;let[n,s,a,o]=r.match(eV);return{[e]:parseFloat(n),[t]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},eD=e=>eN(0,255,e),eI={...eE,transform:e=>Math.round(eD(e))},eB={test:eR("rgb","red"),parse:eL("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:r=1})=>"rgba("+eI.transform(e)+", "+eI.transform(t)+", "+eI.transform(i)+", "+eS(ek.transform(r))+")"},eF={test:eR("#"),parse:function(e){let t="",i="",r="",n="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),r=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),r=e.substring(3,4),n=e.substring(4,5),t+=t,i+=i,r+=r,n+=n),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:eB.transform},ez=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eO=ez("deg"),eW=ez("%"),eU=ez("px"),e$=ez("vh"),eH=ez("vw"),e_={...eW,parse:e=>eW.parse(e)/100,transform:e=>eW.transform(100*e)},eq={test:eR("hsl","hue"),parse:eL("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:r=1})=>"hsla("+Math.round(e)+", "+eW.transform(eS(t))+", "+eW.transform(eS(i))+", "+eS(ek.transform(r))+")"},eK={test:e=>eB.test(e)||eF.test(e)||eq.test(e),parse:e=>eB.test(e)?eB.parse(e):eq.test(e)?eq.parse(e):eF.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eB.transform(e):eq.transform(e)},eG=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eY="number",eZ="color",eX=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eJ(e){let t=e.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,a=t.replace(eX,e=>(eK.test(e)?(r.color.push(s),n.push(eZ),i.push(eK.parse(e))):e.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(e)):(r.number.push(s),n.push(eY),i.push(parseFloat(e))),++s,"${}")).split("${}");return{values:i,split:a,indexes:r,types:n}}function eQ(e){return eJ(e).values}function e0(e){let{split:t,types:i}=eJ(e),r=t.length;return e=>{let n="";for(let s=0;s<r;s++)if(n+=t[s],void 0!==e[s]){let t=i[s];t===eY?n+=eS(e[s]):t===eZ?n+=eK.transform(e[s]):n+=e[s]}return n}}let e1=e=>"number"==typeof e?0:e,e2={test:function(e){var t,i;return isNaN(e)&&"string"==typeof e&&((null==(t=e.match(eV))?void 0:t.length)||0)+((null==(i=e.match(eG))?void 0:i.length)||0)>0},parse:eQ,createTransformer:e0,getAnimatableNone:function(e){let t=eQ(e);return e0(e)(t.map(e1))}},e5=new Set(["brightness","contrast","saturate","opacity"]);function e3(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=i.match(eV)||[];if(!r)return e;let n=i.replace(r,""),s=+!!e5.has(t);return r!==i&&(s*=100),t+"("+s+n+")"}let e6=/\b([a-z-]*)\(.*?\)/gu,e4={...e2,getAnimatableNone:e=>{let t=e.match(e6);return t?t.map(e3).join(" "):e}},e9={...eE,transform:Math.round},e7={borderWidth:eU,borderTopWidth:eU,borderRightWidth:eU,borderBottomWidth:eU,borderLeftWidth:eU,borderRadius:eU,radius:eU,borderTopLeftRadius:eU,borderTopRightRadius:eU,borderBottomRightRadius:eU,borderBottomLeftRadius:eU,width:eU,maxWidth:eU,height:eU,maxHeight:eU,top:eU,right:eU,bottom:eU,left:eU,padding:eU,paddingTop:eU,paddingRight:eU,paddingBottom:eU,paddingLeft:eU,margin:eU,marginTop:eU,marginRight:eU,marginBottom:eU,marginLeft:eU,backgroundPositionX:eU,backgroundPositionY:eU,rotate:eO,rotateX:eO,rotateY:eO,rotateZ:eO,scale:eC,scaleX:eC,scaleY:eC,scaleZ:eC,skew:eO,skewX:eO,skewY:eO,distance:eU,translateX:eU,translateY:eU,translateZ:eU,x:eU,y:eU,z:eU,perspective:eU,transformPerspective:eU,opacity:ek,originX:e_,originY:e_,originZ:eU,zIndex:e9,size:eU,fillOpacity:ek,strokeOpacity:ek,numOctaves:e9},e8={...e7,color:eK,backgroundColor:eK,outlineColor:eK,fill:eK,stroke:eK,borderColor:eK,borderTopColor:eK,borderRightColor:eK,borderBottomColor:eK,borderLeftColor:eK,filter:e4,WebkitFilter:e4},te=e=>e8[e];function tt(e,t){let i=te(e);return i!==e4&&(i=e2),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let ti=new Set(["auto","none","0"]),tr=e=>e===eE||e===eU,tn=(e,t)=>parseFloat(e.split(", ")[t]),ts=(e,t)=>(i,{transform:r})=>{if("none"===r||!r)return 0;let n=r.match(/^matrix3d\((.+)\)$/u);if(n)return tn(n[1],t);{let t=r.match(/^matrix\((.+)\)$/u);return t?tn(t[1],e):0}},ta=new Set(["x","y","z"]),to=U.filter(e=>!ta.has(e)),tl={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:ts(4,13),y:ts(5,14)};tl.translateX=tl.x,tl.translateY=tl.y;let td=new Set,tc=!1,tu=!1;function th(){if(tu){let e=Array.from(td).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return to.forEach(i=>{let r=e.getValue(i);void 0!==r&&(t.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{var r;null==(r=e.getValue(t))||r.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tu=!1,tc=!1,td.forEach(e=>e.complete()),td.clear()}function tp(){td.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tu=!0)})}class tm{constructor(e,t,i,r,n,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(td.add(this),tc||(tc=!0,Z.read(tp),Z.resolveKeyframes(th))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:r}=this;for(let n=0;n<e.length;n++)if(null===e[n])if(0===n){let n=null==r?void 0:r.get(),s=e[e.length-1];if(void 0!==n)e[0]=n;else if(i&&t){let r=i.readValue(t,s);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=s),r&&void 0===n&&r.set(e[0])}else e[n]=e[n-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),td.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,td.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let tf=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),ty=e=>t=>"string"==typeof t&&t.startsWith(e),tg=ty("--"),tx=ty("var(--"),tv=e=>!!tx(e)&&tb.test(e.split("/*")[0].trim()),tb=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tw=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tP=e=>t=>t.test(e),tj=[eE,eU,eW,eO,eH,e$,{test:e=>"auto"===e,parse:e=>e}],tA=e=>tj.find(tP(e));class tT extends tm{constructor(e,t,i,r,n){super(e,t,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let r=e[i];if("string"==typeof r&&tv(r=r.trim())){let n=function e(t,i,r=1){W(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[n,s]=function(e){let t=tw.exec(e);if(!t)return[,];let[,i,r,n]=t;return[`--${null!=i?i:r}`,n]}(t);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let e=a.trim();return tf(e)?parseFloat(e):e}return tv(s)?e(s,i,r+1):s}(r,t.current);void 0!==n&&(e[i]=n),i===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!H.has(i)||2!==e.length)return;let[r,n]=e,s=tA(r),a=tA(n);if(s!==a)if(tr(s)&&tr(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var r;("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||eT(r))&&i.push(t)}i.length&&function(e,t,i){let r,n=0;for(;n<e.length&&!r;){let t=e[n];"string"==typeof t&&!ti.has(t)&&eJ(t).values.length&&(r=e[n]),n++}if(r&&i)for(let n of t)e[n]=tt(i,r)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tl[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(i,r).jump(r,!1)}measureEndState(){var e;let{element:t,name:i,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let n=t.getValue(i);n&&n.jump(this.measuredOrigin,!1);let s=r.length-1,a=r[s];r[s]=tl[i](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),(null==(e=this.removedTransforms)?void 0:e.length)&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tN=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e2.test(e)||"0"===e)&&!e.startsWith("url(")),tE=e=>null!==e;function tk(e,{repeat:t,repeatType:i="loop"},r){let n=e.filter(tE),s=t&&"loop"!==i&&t%2==1?0:n.length-1;return s&&void 0!==r?r:n[s]}class tC{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=et.now(),this.options={autoplay:e,delay:t,type:i,repeat:r,repeatDelay:n,repeatType:s,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(tp(),th()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=et.now(),this.hasAttemptedResolve=!0;let{name:i,type:r,velocity:n,delay:s,onComplete:a,onUpdate:o,isGenerator:l}=this.options;if(!l&&!function(e,t,i,r){let n=e[0];if(null===n)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],a=tN(n,t),o=tN(s,t);return W(a===o,`You are trying to animate ${t} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||v(i))&&r)}(e,i,r,n))if(ep.current||!s){o&&o(tk(e,this.options,t)),a&&a(),this.resolveFinishedPromise();return}else this.options.duration=0;let d=this.initPlayback(e,t);!1!==d&&(this._resolved={keyframes:e,finalKeyframe:t,...d},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}let tS=(e,t,i)=>e+(t-e)*i;function tV(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function tM(e,t){return i=>i>0?t:e}let tR=(e,t,i)=>{let r=e*e,n=i*(t*t-r)+r;return n<0?0:Math.sqrt(n)},tL=[eF,eB,eq],tD=e=>tL.find(t=>t.test(e));function tI(e){let t=tD(e);if(W(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===eq&&(i=function({hue:e,saturation:t,lightness:i,alpha:r}){e/=360,i/=100;let n=0,s=0,a=0;if(t/=100){let r=i<.5?i*(1+t):i+t-i*t,o=2*i-r;n=tV(o,r,e+1/3),s=tV(o,r,e),a=tV(o,r,e-1/3)}else n=s=a=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*a),alpha:r}}(i)),i}let tB=(e,t)=>{let i=tI(e),r=tI(t);if(!i||!r)return tM(e,t);let n={...i};return e=>(n.red=tR(i.red,r.red,e),n.green=tR(i.green,r.green,e),n.blue=tR(i.blue,r.blue,e),n.alpha=tS(i.alpha,r.alpha,e),eB.transform(n))},tF=(e,t)=>i=>t(e(i)),tz=(...e)=>e.reduce(tF),tO=new Set(["none","hidden"]);function tW(e,t){return i=>tS(e,t,i)}function tU(e){return"number"==typeof e?tW:"string"==typeof e?tv(e)?tM:eK.test(e)?tB:t_:Array.isArray(e)?t$:"object"==typeof e?eK.test(e)?tB:tH:tM}function t$(e,t){let i=[...e],r=i.length,n=e.map((e,i)=>tU(e)(e,t[i]));return e=>{for(let t=0;t<r;t++)i[t]=n[t](e);return i}}function tH(e,t){let i={...e,...t},r={};for(let n in i)void 0!==e[n]&&void 0!==t[n]&&(r[n]=tU(e[n])(e[n],t[n]));return e=>{for(let t in r)i[t]=r[t](e);return i}}let t_=(e,t)=>{let i=e2.createTransformer(t),r=eJ(e),n=eJ(t);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?tO.has(e)&&!n.values.length||tO.has(t)&&!r.values.length?function(e,t){return tO.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):tz(t$(function(e,t){var i;let r=[],n={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){let a=t.types[s],o=e.indexes[a][n[a]],l=null!=(i=e.values[o])?i:0;r[s]=l,n[a]++}return r}(r,n),n.values),i):(W(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tM(e,t))};function tq(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?tS(e,t,i):tU(e)(e,t)}function tK(e,t,i){var r,n;let s=Math.max(t-5,0);return r=i-e(s),(n=t-s)?1e3/n*r:0}let tG={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tY(e,t){return e*Math.sqrt(1-t*t)}let tZ=["duration","bounce"],tX=["stiffness","damping","mass"];function tJ(e,t){return t.some(t=>void 0!==e[t])}function tQ(e=tG.visualDuration,t=tG.bounce){let i,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:n,restDelta:s}=r,a=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:d,damping:c,mass:u,duration:h,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:tG.velocity,stiffness:tG.stiffness,damping:tG.damping,mass:tG.mass,isResolvedFromDuration:!1,...e};if(!tJ(e,tX)&&tJ(e,tZ))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),r=i*i,n=2*eN(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:tG.mass,stiffness:r,damping:n}}else{let i=function({duration:e=tG.duration,bounce:t=tG.bounce,velocity:i=tG.velocity,mass:r=tG.mass}){let n,s;W(e<=z(tG.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=eN(tG.minDamping,tG.maxDamping,a),e=eN(tG.minDuration,tG.maxDuration,O(e)),a<1?(n=t=>{let r=t*a,n=r*e;return .001-(r-i)/tY(t,a)*Math.exp(-n)},s=t=>{let r=t*a*e,s=Math.pow(a,2)*Math.pow(t,2)*e,o=Math.exp(-r),l=tY(Math.pow(t,2),a);return(r*i+i-s)*o*(-n(t)+.001>0?-1:1)/l}):(n=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),s=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let r=i;for(let i=1;i<12;i++)r-=e(r)/t(r);return r}(n,s,5/e);if(e=z(e),isNaN(o))return{stiffness:tG.stiffness,damping:tG.damping,duration:e};{let t=Math.pow(o,2)*r;return{stiffness:t,damping:2*a*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...i,mass:tG.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-O(r.velocity||0)}),f=p||0,y=c/(2*Math.sqrt(d*u)),g=o-a,v=O(Math.sqrt(d/u)),b=5>Math.abs(g);if(n||(n=b?tG.restSpeed.granular:tG.restSpeed.default),s||(s=b?tG.restDelta.granular:tG.restDelta.default),y<1){let e=tY(v,y);i=t=>o-Math.exp(-y*v*t)*((f+y*v*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===y)i=e=>o-Math.exp(-v*e)*(g+(f+v*g)*e);else{let e=v*Math.sqrt(y*y-1);i=t=>{let i=Math.exp(-y*v*t),r=Math.min(e*t,300);return o-i*((f+y*v*g)*Math.sinh(r)+e*g*Math.cosh(r))/e}}let w={calculatedDuration:m&&h||null,next:e=>{let t=i(e);if(m)l.done=e>=h;else{let r=0;y<1&&(r=0===e?z(f):tK(i,e,t));let a=Math.abs(o-t)<=s;l.done=Math.abs(r)<=n&&a}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(x(w),2e4),t=T(t=>w.next(e*t).value,e,30);return e+"ms "+t}};return w}function t0({keyframes:e,velocity:t=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:d=.5,restSpeed:c}){let u,h,p=e[0],m={done:!1,value:p},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,y=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,g=i*t,x=p+g,v=void 0===a?x:a(x);v!==x&&(g=v-p);let b=e=>-g*Math.exp(-e/r),w=e=>v+b(e),P=e=>{let t=b(e),i=w(e);m.done=Math.abs(t)<=d,m.value=m.done?v:i},j=e=>{f(m.value)&&(u=e,h=tQ({keyframes:[m.value,y(m.value)],velocity:tK(w,e,m.value),damping:n,stiffness:s,restDelta:d,restSpeed:c}))};return j(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==u||(t=!0,P(e),j(e)),void 0!==u&&e>=u)?h.next(e-u):(t||P(e),m)}}}let t1=ef(.42,0,1,1),t2=ef(0,0,.58,1),t5=ef(.42,0,.58,1),t3=e=>Array.isArray(e)&&"number"!=typeof e[0],t6={linear:W,easeIn:t1,easeInOut:t5,easeOut:t2,circIn:eP,circInOut:eA,circOut:ej,backIn:ev,backInOut:eb,backOut:ex,anticipate:ew},t4=e=>{if(w(e)){W(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,r,n]=e;return ef(t,i,r,n)}return"string"==typeof e?(W(void 0!==t6[e],`Invalid easing type '${e}'`),t6[e]):e};function t9({duration:e=300,keyframes:t,times:i,ease:r="easeInOut"}){var n;let s=t3(r)?r.map(t4):t4(r),a={done:!1,value:t[0]},o=function(e,t,{clamp:i=!0,ease:r,mixer:n}={}){let s=e.length;if(W(s===t.length,"Both input and output ranges must be the same length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let r=[],n=i||tq,s=e.length-1;for(let i=0;i<s;i++){let s=n(e[i],e[i+1]);t&&(s=tz(Array.isArray(t)?t[i]||W:t,s)),r.push(s)}return r}(t,r,n),l=o.length,d=i=>{if(a&&i<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(i<e[r+1]);r++);let n=A(e[r],e[r+1],i);return o[r](n)};return i?t=>d(eN(e[0],e[s-1],t)):d}((n=i&&i.length===t.length?i:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let r=1;r<=t;r++){let n=A(0,t,r);e.push(tS(i,1,n))}}(t,e.length-1),t}(t),n.map(t=>t*e)),t,{ease:Array.isArray(s)?s:t.map(()=>s||t5).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=o(t),a.done=t>=e,a)}}let t7=e=>{let t=({timestamp:t})=>e(t);return{start:()=>Z.update(t,!0),stop:()=>X(t),now:()=>J.isProcessing?J.timestamp:et.now()}},t8={decay:t0,inertia:t0,tween:t9,keyframes:t9,spring:tQ},ie=e=>e/100;class it extends tC{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:t,motionValue:i,element:r,keyframes:n}=this.options,s=(null==r?void 0:r.KeyframeResolver)||tm;this.resolver=new s(n,(e,t)=>this.onKeyframesResolved(e,t),t,i,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){let t,i,{type:r="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:a,velocity:o=0}=this.options,l=v(r)?r:t8[r]||t9;l!==t9&&"number"!=typeof e[0]&&(t=tz(ie,tq(e[0],e[1])),e=[0,100]);let d=l({...this.options,keyframes:e});"mirror"===a&&(i=l({...this.options,keyframes:[...e].reverse(),velocity:-o})),null===d.calculatedDuration&&(d.calculatedDuration=x(d));let{calculatedDuration:c}=d,u=c+s;return{generator:d,mirroredGenerator:i,mapPercentToKeyframes:t,calculatedDuration:c,resolvedDuration:u,totalDuration:u*(n+1)-s}}onPostResolved(){let{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:i}=this;if(!i){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:r,generator:n,mirroredGenerator:s,mapPercentToKeyframes:a,keyframes:o,calculatedDuration:l,totalDuration:d,resolvedDuration:c}=i;if(null===this.startTime)return n.next(0);let{delay:u,repeat:h,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-d/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let y=this.currentTime-u*(this.speed>=0?1:-1),g=this.speed>=0?y<0:y>d;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let x=this.currentTime,v=n;if(h){let e=Math.min(this.currentTime,d)/c,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,h+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/c)):"mirror"===p&&(v=s)),x=eN(0,1,i)*c}let b=g?{done:!1,value:o[0]}:v.next(x);a&&(b.value=a(b.value));let{done:w}=b;g||null===l||(w=this.speed>=0?this.currentTime>=d:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&void 0!==r&&(b.value=tk(o,this.options,r)),f&&f(b.value),P&&this.finish(),b}get duration(){let{resolved:e}=this;return e?O(e.calculatedDuration):0}get time(){return O(this.currentTime)}set time(e){e=z(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=O(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=t7,onPlay:t,startTime:i}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let r=this.driver.now();null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=r):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(e=this.currentTime)?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let ii=new Set(["opacity","clipPath","filter","transform"]),ir=p(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),is={anticipate:ew,backInOut:eb,circInOut:eA};class ia extends tC{constructor(e){super(e);let{name:t,motionValue:i,element:r,keyframes:n}=this.options;this.resolver=new tT(n,(e,t)=>this.onKeyframesResolved(e,t),t,i,r),this.resolver.scheduleResolve()}initPlayback(e,t){var i;let{duration:r=300,times:n,ease:s,type:a,motionValue:o,name:l,startTime:d}=this.options;if(!o.owner||!o.owner.current)return!1;if("string"==typeof s&&j()&&s in is&&(s=is[s]),v((i=this.options).type)||"spring"===i.type||!function e(t){return!!("function"==typeof t&&j()||!t||"string"==typeof t&&(t in E||j())||w(t)||Array.isArray(t)&&t.every(e))}(i.ease)){let{onComplete:t,onUpdate:i,motionValue:o,element:l,...d}=this.options,c=function(e,t){let i=new it({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),r={done:!1,value:e[0]},n=[],s=0;for(;!r.done&&s<2e4;)n.push((r=i.sample(s)).value),s+=10;return{times:void 0,keyframes:n,duration:s-10,ease:"linear"}}(e,d);1===(e=c.keyframes).length&&(e[1]=e[0]),r=c.duration,n=c.times,s=c.ease,a="keyframes"}let c=function(e,t,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:a="loop",ease:o="easeInOut",times:l}={}){let d={[t]:i};l&&(d.offset=l);let c=function e(t,i){if(t)return"function"==typeof t&&j()?T(t,i):w(t)?N(t):Array.isArray(t)?t.map(t=>e(t,i)||E.easeOut):E[t]}(o,n);return Array.isArray(c)&&(d.easing=c),e.animate(d,{delay:r,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"})}(o.owner.current,l,e,{...this.options,duration:r,times:n,ease:s});return c.startTime=null!=d?d:this.calcStartTime(),this.pendingTimeline?(b(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{let{onComplete:i}=this.options;o.set(tk(e,this.options,t)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:n,type:a,ease:s,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return O(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return O(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:i}=t;i.currentTime=z(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:i}=t;i.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}get startTime(){let{resolved:e}=this;if(!e)return null;let{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return W;let{animation:i}=t;b(i,e)}else this.pendingTimeline=e;return W}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:i,duration:r,type:n,ease:s,times:a}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:o,element:l,...d}=this.options,c=new it({...d,keyframes:i,duration:r,type:n,ease:s,times:a,isGenerator:!0}),u=z(this.time);e.setWithVelocity(c.sample(u-10).value,c.sample(u).value,10)}let{onStop:o}=this.options;o&&o(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:i,repeatDelay:r,repeatType:n,damping:s,type:a}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return ir()&&i&&ii.has(i)&&!o&&!l&&!r&&"mirror"!==n&&0!==s&&"inertia"!==a}}let io={type:"spring",stiffness:500,damping:25,restSpeed:10},il=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),id={type:"keyframes",duration:.8},ic={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},iu=(e,{keyframes:t})=>t.length>2?id:$.has(e)?e.startsWith("scale")?il(t[1]):io:ic,ih=(e,t,i,r={},n,s)=>a=>{let o=g(r,e)||{},l=o.delay||r.delay||0,{elapsed:d=0}=r;d-=z(l);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-d,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:s?void 0:n};!function({when:e,delay:t,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:d,...c}){return!!Object.keys(c).length}(o)&&(c={...c,...iu(e,c)}),c.duration&&(c.duration=z(c.duration)),c.repeatDelay&&(c.repeatDelay=z(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let u=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(u=!0)),(ep.current||K.skipAnimations)&&(u=!0,c.duration=0,c.delay=0),u&&!s&&void 0!==t.get()){let e=tk(c.keyframes,o);if(void 0!==e)return Z.update(()=>{c.onUpdate(e),c.onComplete()}),new y([])}return!s&&ia.supports(c)?new ia(c):new it(c)};function ip(e,t,{delay:i=0,transitionOverride:r,type:n}={}){var s;let{transition:a=e.getDefaultTransition(),transitionEnd:o,...l}=t;r&&(a=r);let d=[],u=n&&e.animationState&&e.animationState.getState()[n];for(let t in l){let r=e.getValue(t,null!=(s=e.latestValues[t])?s:null),n=l[t];if(void 0===n||u&&function({protectedKeys:e,needsAnimating:t},i){let r=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,r}(u,t))continue;let o={delay:i,...g(a||{},t)},c=!1;if(window.MotionHandoffAnimation){let i=e.props[eh];if(i){let e=window.MotionHandoffAnimation(i,t,Z);null!==e&&(o.startTime=e,c=!0)}}ec(e,t),r.start(ih(t,r,n,e.shouldReduceMotion&&H.has(t)?{type:!1}:o,e,c));let h=r.animation;h&&d.push(h)}return o&&Promise.all(d).then(()=>{Z.update(()=>{o&&function(e,t){let{transitionEnd:i={},transition:r={},...n}=c(e,t)||{};for(let t in n={...n,...i}){let i=q(n[t]);e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,el(i))}}(e,o)})}),d}function im(e,t,i={}){var r;let n=c(e,t,"exit"===i.type?null==(r=e.presenceContext)?void 0:r.custom:void 0),{transition:s=e.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let a=n?()=>Promise.all(ip(e,n,i)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=s;return function(e,t,i=0,r=0,n=1,s){let a=[],o=(e.variantChildren.size-1)*r,l=1===n?(e=0)=>e*r:(e=0)=>o-e*r;return Array.from(e.variantChildren).sort(iy).forEach((e,r)=>{e.notify("AnimationStart",t),a.push(im(e,t,{...s,delay:i+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,n+r,a,o,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([a(),o(i.delay)]);{let[e,t]="beforeChildren"===l?[a,o]:[o,a];return e().then(()=>t())}}function iy(e,t){return e.sortNodePosition(t)}let ig=h.length,ix=[...u].reverse(),iv=u.length;function ib(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iw(){return{animate:ib(!0),whileInView:ib(),whileHover:ib(),whileTap:ib(),whileDrag:ib(),whileFocus:ib(),exit:ib()}}class iP{constructor(e){this.isMounted=!1,this.node=e}update(){}}class ij extends iP{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>im(e,t,i)));else if("string"==typeof t)r=im(e,t,i);else{let n="function"==typeof t?c(e,t,i.custom):t;r=Promise.all(ip(e,n,i))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=iw(),r=!0,l=t=>(i,r)=>{var n;let s=c(e,r,"exit"===t?null==(n=e.presenceContext)?void 0:n.custom:void 0);if(s){let{transition:e,transitionEnd:t,...r}=s;i={...i,...r,...t}}return i};function d(d){let{props:c}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<ig;e++){let r=h[e],n=t.props[r];(o(n)||!1===n)&&(i[r]=n)}return i}(e.parent)||{},p=[],m=new Set,f={},y=1/0;for(let t=0;t<iv;t++){var g,x;let h=ix[t],v=i[h],b=void 0!==c[h]?c[h]:u[h],w=o(b),P=h===d?v.isActive:null;!1===P&&(y=t);let j=b===u[h]&&b!==c[h]&&w;if(j&&r&&e.manuallyAnimateOnMount&&(j=!1),v.protectedKeys={...f},!v.isActive&&null===P||!b&&!v.prevProp||n(b)||"boolean"==typeof b)continue;let A=(g=v.prevProp,"string"==typeof(x=b)?x!==g:!!Array.isArray(x)&&!a(x,g)),T=A||h===d&&v.isActive&&!j&&w||t>y&&w,N=!1,E=Array.isArray(b)?b:[b],k=E.reduce(l(h),{});!1===P&&(k={});let{prevResolvedValues:C={}}=v,S={...C,...k},V=t=>{T=!0,m.has(t)&&(N=!0,m.delete(t)),v.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in S){let t=k[e],i=C[e];if(f.hasOwnProperty(e))continue;let r=!1;(s(t)&&s(i)?a(t,i):t===i)?void 0!==t&&m.has(e)?V(e):v.protectedKeys[e]=!0:null!=t?V(e):m.add(e)}v.prevProp=b,v.prevResolvedValues=k,v.isActive&&(f={...f,...k}),r&&e.blockInitialAnimation&&(T=!1);let M=!(j&&A)||N;T&&M&&p.push(...E.map(e=>({animation:e,options:{type:h}})))}if(m.size){let t={};m.forEach(i=>{let r=e.getBaseTarget(i),n=e.getValue(i);n&&(n.liveStyle=!0),t[i]=null!=r?r:null}),p.push({animation:t})}let v=!!p.length;return r&&(!1===c.initial||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(v=!1),r=!1,v?t(p):Promise.resolve()}return{animateChanges:d,setActive:function(t,r){var n;if(i[t].isActive===r)return Promise.resolve();null==(n=e.variantChildren)||n.forEach(e=>{var i;return null==(i=e.animationState)?void 0:i.setActive(t,r)}),i[t].isActive=r;let s=d(t);for(let e in i)i[e].protectedKeys={};return s},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=iw(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();n(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null==(e=this.unmountControls)||e.call(this)}}let iA=0;class iT extends iP{constructor(){super(...arguments),this.id=iA++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}function iN(e,t,i,r={passive:!0}){return e.addEventListener(t,i,r),()=>e.removeEventListener(t,i)}function iE(e){return{point:{x:e.pageX,y:e.pageY}}}let ik=e=>t=>M(t)&&e(t,iE(t));function iC(e,t,i,r){return iN(e,t,ik(i),r)}let iS=(e,t)=>Math.abs(e-t);class iV{constructor(e,t,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=iL(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(iS(e.x,t.x)**2+iS(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!i)return;let{point:r}=e,{timestamp:n}=J;this.history.push({...r,timestamp:n});let{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=iM(t,this.transformPagePoint),Z.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iL("pointercancel"===e.type?this.lastMoveEventInfo:iM(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,s),r&&r(e,s)},!M(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=i,this.contextWindow=r||window;let s=iM(iE(e),this.transformPagePoint),{point:a}=s,{timestamp:o}=J;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,iL(s,this.history)),this.removeListeners=tz(iC(this.contextWindow,"pointermove",this.handlePointerMove),iC(this.contextWindow,"pointerup",this.handlePointerUp),iC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),X(this.updatePoint)}}function iM(e,t){return t?{point:t(e.point)}:e}function iR(e,t){return{x:e.x-t.x,y:e.y-t.y}}function iL({point:e},t){return{point:e,delta:iR(e,iD(t)),offset:iR(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,r=null,n=iD(e);for(;i>=0&&(r=e[i],!(n.timestamp-r.timestamp>z(.1)));)i--;if(!r)return{x:0,y:0};let s=O(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let a={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function iD(e){return e[e.length-1]}function iI(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function iB(e){return e.max-e.min}function iF(e,t,i,r=.5){e.origin=r,e.originPoint=tS(t.min,t.max,e.origin),e.scale=iB(i)/iB(t),e.translate=tS(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function iz(e,t,i,r){iF(e.x,t.x,i.x,r?r.originX:void 0),iF(e.y,t.y,i.y,r?r.originY:void 0)}function iO(e,t,i){e.min=i.min+t.min,e.max=e.min+iB(t)}function iW(e,t,i){e.min=t.min-i.min,e.max=e.min+iB(t)}function iU(e,t,i){iW(e.x,t.x,i.x),iW(e.y,t.y,i.y)}function i$(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function iH(e,t){let i=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,r]=[r,i]),{min:i,max:r}}function i_(e,t,i){return{min:iq(e,t),max:iq(e,i)}}function iq(e,t){return"number"==typeof e?e:e[t]||0}let iK=()=>({translate:0,scale:1,origin:0,originPoint:0}),iG=()=>({x:iK(),y:iK()}),iY=()=>({min:0,max:0}),iZ=()=>({x:iY(),y:iY()});function iX(e){return[e("x"),e("y")]}function iJ({top:e,left:t,right:i,bottom:r}){return{x:{min:t,max:i},y:{min:e,max:r}}}function iQ(e){return void 0===e||1===e}function i0({scale:e,scaleX:t,scaleY:i}){return!iQ(e)||!iQ(t)||!iQ(i)}function i1(e){return i0(e)||i2(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function i2(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function i5(e,t,i,r,n){return void 0!==n&&(e=r+n*(e-r)),r+i*(e-r)+t}function i3(e,t=0,i=1,r,n){e.min=i5(e.min,t,i,r,n),e.max=i5(e.max,t,i,r,n)}function i6(e,{x:t,y:i}){i3(e.x,t.translate,t.scale,t.originPoint),i3(e.y,i.translate,i.scale,i.originPoint)}function i4(e,t){e.min=e.min+t,e.max=e.max+t}function i9(e,t,i,r,n=.5){let s=tS(e.min,e.max,n);i3(e,t,i,s,r)}function i7(e,t){i9(e.x,t.x,t.scaleX,t.scale,t.originX),i9(e.y,t.y,t.scaleY,t.scale,t.originY)}function i8(e,t){return iJ(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let re=({current:e})=>e?e.ownerDocument.defaultView:null,rt=new WeakMap;class ri{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iZ(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iV(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(iE(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(k[e])return null;else return k[e]=!0,()=>{k[e]=!1};return k.x||k.y?null:(k.x=k.y=!0,()=>{k.x=k.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iX(e=>{let t=this.getAxisMotionValue(e).get()||0;if(eW.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[e];r&&(t=iB(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),n&&Z.postRender(()=>n(e,t)),ec(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>iX(e=>{var t;return"paused"===this.getAnimationState(e)&&(null==(t=this.getAxisMotionValue(e).animation)?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:re(this.visualElement)})}stop(e,t){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&Z.postRender(()=>n(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:r}=this.getProps();if(!i||!rr(e,r,this.currentDirection))return;let n=this.getAxisMotionValue(e),s=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:i},r){return void 0!==t&&e<t?e=r?tS(t,e,r.min):Math.max(e,t):void 0!==i&&e>i&&(e=r?tS(i,e,r.max):Math.min(e,i)),e}(s,this.constraints[e],this.elastic[e])),n.set(s)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(e=this.visualElement.projection)?void 0:e.layout,n=this.constraints;t&&iI(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,{top:t,left:i,bottom:r,right:n}){return{x:i$(e.x,i,n),y:i$(e.y,t,r)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:i_(e,"left","right"),y:i_(e,"top","bottom")}}(i),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&iX(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!iI(t))return!1;let r=t.current;W(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(e,t,i){let r=i8(e,i),{scroll:n}=t;return n&&(i4(r.x,n.offset.x),i4(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),a=(e=n.layout.layoutBox,{x:iH(e.x,s.x),y:iH(e.y,s.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=iJ(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iX(a=>{if(!rr(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let d={type:"inertia",velocity:i?e[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,d)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return ec(this.visualElement,e),i.start(ih(e,i,0,t,this.visualElement,!1))}stopAnimation(){iX(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){iX(e=>{var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.pause()})}getAnimationState(e){var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){iX(t=>{let{drag:i}=this.getProps();if(!rr(t,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(t);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[t];n.set(e[t]-tS(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!iI(t)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};iX(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();r[e]=function(e,t){let i=.5,r=iB(e),n=iB(t);return n>r?i=A(t.min,t.max-r,e.min):r>n&&(i=A(e.min,e.max-n,t.min)),eN(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iX(t=>{if(!rr(t,e,null))return;let i=this.getAxisMotionValue(t),{min:n,max:s}=this.constraints[t];i.set(tS(n,s,r[t]))})}addListeners(){if(!this.visualElement.current)return;rt.set(this.visualElement,this);let e=iC(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();iI(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),Z.read(t);let n=iN(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(iX(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{n(),e(),r(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:a}}}function rr(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class rn extends iP{constructor(e){super(e),this.removeGroupControls=W,this.removeListeners=W,this.controls=new ri(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||W}unmount(){this.removeGroupControls(),this.removeListeners()}}let rs=e=>(t,i)=>{e&&Z.postRender(()=>e(t,i))};class ra extends iP{constructor(){super(...arguments),this.removePointerDownListener=W}onPointerDown(e){this.session=new iV(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:re(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rs(e),onStart:rs(t),onMove:i,onEnd:(e,t)=>{delete this.session,r&&Z.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=iC(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ro,rl,rd=i(687),rc=i(3210);let ru=(0,rc.createContext)(null),rh=(0,rc.createContext)({}),rp=(0,rc.createContext)({}),rm={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ry={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eU.test(e))return e;else e=parseFloat(e);let i=rf(e,t.target.x),r=rf(e,t.target.y);return`${i}% ${r}%`}},rg={},{schedule:rx,cancel:rv}=Y(queueMicrotask,!1);class rb extends rc.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=e;Object.assign(rg,rP),n&&(t.group&&t.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rm.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:r,isPresent:n}=this.props,s=i.projection;return s&&(s.isPresent=n,r||e.layoutDependency!==t||void 0===t?s.willUpdate():this.safeToRemove(),e.isPresent!==n&&(n?s.promote():s.relegate()||Z.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),rx.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rw(e){let[t,i]=function(e=!0){let t=(0,rc.useContext)(ru);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:r,register:n}=t,s=(0,rc.useId)();(0,rc.useEffect)(()=>{e&&n(s)},[e]);let a=(0,rc.useCallback)(()=>e&&r&&r(s),[s,r,e]);return!i&&r?[!1,a]:[!0]}(),r=(0,rc.useContext)(rh);return(0,rd.jsx)(rb,{...e,layoutGroup:r,switchLayoutGroup:(0,rc.useContext)(rp),isPresent:t,safeToRemove:i})}let rP={borderRadius:{...ry,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ry,borderTopRightRadius:ry,borderBottomLeftRadius:ry,borderBottomRightRadius:ry,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let r=e2.parse(e);if(r.length>5)return e;let n=e2.createTransformer(e),s=+("number"!=typeof r[0]),a=i.x.scale*t.x,o=i.y.scale*t.y;r[0+s]/=a,r[1+s]/=o;let l=tS(a,o,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}},rj=(e,t)=>e.depth-t.depth;class rA{constructor(){this.children=[],this.isDirty=!1}add(e){ei(this.children,e),this.isDirty=!0}remove(e){er(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rj),this.isDirty=!1,this.children.forEach(e)}}function rT(e){let t=ed(e)?e.get():e;return _(t)?t.toValue():t}let rN=["TopLeft","TopRight","BottomLeft","BottomRight"],rE=rN.length,rk=e=>"string"==typeof e?parseFloat(e):e,rC=e=>"number"==typeof e||eU.test(e);function rS(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rV=rR(0,.5,ej),rM=rR(.5,.95,W);function rR(e,t,i){return r=>r<e?0:r>t?1:i(A(e,t,r))}function rL(e,t){e.min=t.min,e.max=t.max}function rD(e,t){rL(e.x,t.x),rL(e.y,t.y)}function rI(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rB(e,t,i,r,n){return e-=t,e=r+1/i*(e-r),void 0!==n&&(e=r+1/n*(e-r)),e}function rF(e,t,[i,r,n],s,a){!function(e,t=0,i=1,r=.5,n,s=e,a=e){if(eW.test(t)&&(t=parseFloat(t),t=tS(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=tS(s.min,s.max,r);e===s&&(o-=t),e.min=rB(e.min,t,i,o,n),e.max=rB(e.max,t,i,o,n)}(e,t[i],t[r],t[n],t.scale,s,a)}let rz=["x","scaleX","originX"],rO=["y","scaleY","originY"];function rW(e,t,i,r){rF(e.x,t,rz,i?i.x:void 0,r?r.x:void 0),rF(e.y,t,rO,i?i.y:void 0,r?r.y:void 0)}function rU(e){return 0===e.translate&&1===e.scale}function r$(e){return rU(e.x)&&rU(e.y)}function rH(e,t){return e.min===t.min&&e.max===t.max}function r_(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rq(e,t){return r_(e.x,t.x)&&r_(e.y,t.y)}function rK(e){return iB(e.x)/iB(e.y)}function rG(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rY{constructor(){this.members=[]}add(e){ei(this.members,e),e.scheduleRender()}remove(e){if(er(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rZ={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},rX="undefined"!=typeof window&&void 0!==window.MotionDebug,rJ=["","X","Y","Z"],rQ={visibility:"hidden"},r0=0;function r1(e,t,i,r){let{latestValues:n}=t;n[e]&&(i[e]=n[e],t.setStaticValue(e,0),r&&(r[e]=0))}function r2({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(e={},i=null==t?void 0:t()){this.id=r0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rX&&(rZ.totalNodes=rZ.resolvedTargetDeltas=rZ.recalculatedProjection=0),this.nodes.forEach(r6),this.nodes.forEach(ni),this.nodes.forEach(nr),this.nodes.forEach(r4),rX&&window.MotionDebug.record(rZ)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rA)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new en),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:r,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||r)&&(this.isLayoutDirty=!0),e){let i,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=et.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&(X(r),e(s-t))};return Z.read(r,!0),()=>X(r)}(r,250),rm.hasAnimatedSinceResize&&(rm.hasAnimatedSinceResize=!1,this.nodes.forEach(nt))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&s&&(r||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||nd,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!rq(this.targetLayout,r)||i,d=!t&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||d||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,d);let t={...g(n,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||nt(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,X(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nn),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let r=i.props[eh];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",Z,!(e||i))}let{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&e(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r7);return}this.isUpdating||this.nodes.forEach(r8),this.isUpdating=!1,this.nodes.forEach(ne),this.nodes.forEach(r5),this.nodes.forEach(r3),this.clearAllSnapshots();let e=et.now();J.delta=eN(0,1e3/60,e-J.timestamp),J.timestamp=e,J.isProcessing=!0,Q.update.process(J),Q.preRender.process(J),Q.render.process(J),J.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rx.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(r9),this.sharedNodes.forEach(ns)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Z.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Z.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iZ(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!n)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!r$(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;e&&(t||i1(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),r=this.removeElementScroll(i);return e&&(r=this.removeTransform(r)),nh((t=r).x),nh(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return iZ();let i=t.measureViewportBox();if(!((null==(e=this.scroll)?void 0:e.wasRoot)||this.path.some(nm))){let{scroll:e}=this.root;e&&(i4(i.x,e.offset.x),i4(i.y,e.offset.y))}return i}removeElementScroll(e){var t;let i=iZ();if(rD(i,e),null==(t=this.scroll)?void 0:t.wasRoot)return i;for(let t=0;t<this.path.length;t++){let r=this.path[t],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&rD(i,e),i4(i.x,n.offset.x),i4(i.y,n.offset.y))}return i}applyTransform(e,t=!1){let i=iZ();rD(i,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i7(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),i1(r.latestValues)&&i7(i,r.latestValues)}return i1(this.latestValues)&&i7(i,this.latestValues),i}removeTransform(e){let t=iZ();rD(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!i1(i.latestValues))continue;i0(i.latestValues)&&i.updateSnapshot();let r=iZ();rD(r,i.measurePageBox()),rW(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return i1(this.latestValues)&&rW(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==J.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,i,r,n;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==s;if(!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:o,layoutId:l}=this.options;if(this.layout&&(o||l)){if(this.resolvedRelativeTargetAt=J.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iZ(),this.relativeTargetOrigin=iZ(),iU(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rD(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iZ(),this.targetWithTransforms=iZ()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,r=this.relativeTarget,n=this.relativeParent.target,iO(i.x,r.x,n.x),iO(i.y,r.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rD(this.target,this.layout.layoutBox),i6(this.target,this.targetDelta)):rD(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iZ(),this.relativeTargetOrigin=iZ(),iU(this.relativeTargetOrigin,this.target,e.target),rD(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rX&&rZ.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||i0(this.parent.latestValues)||i2(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),i=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===J.timestamp&&(r=!1),r)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;rD(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;!function(e,t,i,r=!1){let n,s,a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){s=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i7(e,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,i6(e,s)),r&&i1(n.latestValues)&&i7(e,n.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iZ());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rI(this.prevProjectionDelta.x,this.projectionDelta.x),rI(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iz(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===a&&this.treeScale.y===o&&rG(this.projectionDelta.x,this.prevProjectionDelta.x)&&rG(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),rX&&rZ.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null==(t=this.options.visualElement)||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iG(),this.projectionDelta=iG(),this.projectionDeltaWithTransform=iG()}setAnimationOrigin(e,t=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},a=iG();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=iZ(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),d=this.getStack(),c=!d||d.members.length<=1,u=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(nl));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(na(a.x,e.x,r),na(a.y,e.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var d,h,p,m,f,y;iU(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,y=r,no(p.x,m.x,f.x,y),no(p.y,m.y,f.y,y),i&&(d=this.relativeTarget,h=i,rH(d.x,h.x)&&rH(d.y,h.y))&&(this.isProjectionDirty=!1),i||(i=iZ()),rD(i,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,i,r,n,s){n?(e.opacity=tS(0,void 0!==i.opacity?i.opacity:1,rV(r)),e.opacityExit=tS(void 0!==t.opacity?t.opacity:1,0,rM(r))):s&&(e.opacity=tS(void 0!==t.opacity?t.opacity:1,void 0!==i.opacity?i.opacity:1,r));for(let n=0;n<rE;n++){let s=`border${rN[n]}Radius`,a=rS(t,s),o=rS(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||rC(a)===rC(o)?(e[s]=Math.max(tS(rk(a),rk(o),r),0),(eW.test(o)||eW.test(a))&&(e[s]+="%")):e[s]=o)}(t.rotate||i.rotate)&&(e.rotate=tS(t.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(X(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Z.update(()=>{rm.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,i){let r=ed(0)?0:el(e);return r.start(ih("",r,1e3,i)),r.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:r,latestValues:n}=e;if(t&&i&&r){if(this!==e&&this.layout&&r&&np(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||iZ();let t=iB(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let r=iB(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+r}rD(t,i),i7(t,n),iz(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rY),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null==(e=this.getStack())?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null==(e=this.getStack())?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let r={};i.z&&r1("z",e,r,this.animationValues);for(let t=0;t<rJ.length;t++)r1(`rotate${rJ[t]}`,e,r,this.animationValues),r1(`skew${rJ[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){var t,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return rQ;let r={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=rT(null==e?void 0:e.pointerEvents)||"",r.transform=n?n(this.latestValues,""):"none",r;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rT(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!i1(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let a=s.animationValues||s.latestValues;this.applyTransformsToTarget(),r.transform=function(e,t,i){let r="",n=e.x.translate/t.x,s=e.y.translate/t.y,a=(null==i?void 0:i.z)||0;if((n||s||a)&&(r=`translate3d(${n}px, ${s}px, ${a}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:n,rotateY:s,skewX:a,skewY:o}=i;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),a&&(r+=`skewX(${a}deg) `),o&&(r+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(r+=`scale(${o}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,a),n&&(r.transform=n(a,r.transform));let{x:o,y:l}=this.projectionDelta;for(let e in r.transformOrigin=`${100*o.origin}% ${100*l.origin}% 0`,s.animationValues?r.opacity=s===this?null!=(i=null!=(t=a.opacity)?t:this.latestValues.opacity)?i:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:r.opacity=s===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,rg){if(void 0===a[e])continue;let{correct:t,applyTo:i}=rg[e],n="none"===r.transform?a[e]:t(a[e],s);if(i){let e=i.length;for(let t=0;t<e;t++)r[i[t]]=n}else r[e]=n}return this.options.layoutId&&(r.pointerEvents=s===this?rT(null==e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null==(t=e.currentAnimation)?void 0:t.stop()}),this.root.nodes.forEach(r7),this.root.sharedNodes.clear()}}}function r5(e){e.updateLayout()}function r3(e){var t;let i=(null==(t=e.resumeFrom)?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:n}=e.options,s=i.source!==e.layout.source;"size"===n?iX(e=>{let r=s?i.measuredBox[e]:i.layoutBox[e],n=iB(r);r.min=t[e].min,r.max=r.min+n}):np(n,i.layoutBox,t)&&iX(r=>{let n=s?i.measuredBox[r]:i.layoutBox[r],a=iB(t[r]);n.max=n.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});let a=iG();iz(a,t,i.layoutBox);let o=iG();s?iz(o,e.applyTransform(r,!0),i.measuredBox):iz(o,t,i.layoutBox);let l=!r$(a),d=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let a=iZ();iU(a,i.layoutBox,n.layoutBox);let o=iZ();iU(o,t,s.layoutBox),rq(a,o)||(d=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:i,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:d})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function r6(e){rX&&rZ.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function r4(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function r9(e){e.clearSnapshot()}function r7(e){e.clearMeasurements()}function r8(e){e.isLayoutDirty=!1}function ne(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function nt(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ni(e){e.resolveTargetDelta()}function nr(e){e.calcProjection()}function nn(e){e.resetSkewAndRotation()}function ns(e){e.removeLeadSnapshot()}function na(e,t,i){e.translate=tS(t.translate,0,i),e.scale=tS(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function no(e,t,i,r){e.min=tS(t.min,i.min,r),e.max=tS(t.max,i.max,r)}function nl(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let nd={duration:.45,ease:[.4,0,.1,1]},nc=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),nu=nc("applewebkit/")&&!nc("chrome/")?Math.round:W;function nh(e){e.min=nu(e.min),e.max=nu(e.max)}function np(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rK(t)-rK(i)))}function nm(e){var t;return e!==e.root&&(null==(t=e.scroll)?void 0:t.wasRoot)}let nf=r2({attachResizeListener:(e,t)=>iN(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ny={current:void 0},ng=r2({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ny.current){let e=new nf({});e.mount(window),e.setOptions({layoutScroll:!0}),ny.current=e}return ny.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function nx(e,t,i){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&Z.postRender(()=>n(t,iE(t)))}class nv extends iP{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[r,n,s]=C(e,i),a=S(e=>{let{target:i}=e,r=t(e);if("function"!=typeof r||!i)return;let s=S(e=>{r(e),i.removeEventListener("pointerleave",s)});i.addEventListener("pointerleave",s,n)});return r.forEach(e=>{e.addEventListener("pointerenter",a,n)}),s}(e,e=>(nx(this.node,e,"Start"),e=>nx(this.node,e,"End"))))}unmount(){}}class nb extends iP{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tz(iN(this.node.current,"focus",()=>this.onFocus()),iN(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function nw(e,t,i){let{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&Z.postRender(()=>n(t,iE(t)))}class nP extends iP{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[r,n,s]=C(e,i),a=e=>{let r=e.currentTarget;if(!F(e)||L.has(r))return;L.add(r);let s=t(e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),F(e)&&L.has(r)&&(L.delete(r),"function"==typeof s&&s(e,{success:t}))},o=e=>{a(e,i.useGlobalTarget||V(r,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return r.forEach(e=>{R.has(e.tagName)||-1!==e.tabIndex||null!==e.getAttribute("tabindex")||(e.tabIndex=0),(i.useGlobalTarget?window:e).addEventListener("pointerdown",a,n),e.addEventListener("focus",e=>B(e,n),n)}),s}(e,e=>(nw(this.node,e,"Start"),(e,{success:t})=>nw(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nj=new WeakMap,nA=new WeakMap,nT=e=>{let t=nj.get(e.target);t&&t(e)},nN=e=>{e.forEach(nT)},nE={some:0,all:1};class nk extends iP{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:r="some",once:n}=e,s={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nE[r]};return function(e,t,i){let r=function({root:e,...t}){let i=e||document;nA.has(i)||nA.set(i,{});let r=nA.get(i),n=JSON.stringify(t);return r[n]||(r[n]=new IntersectionObserver(nN,{root:e,...t})),r[n]}(t);return nj.set(e,i),r.observe(e),()=>{nj.delete(e),r.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=t?i:r;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let nC=(0,rc.createContext)({strict:!1}),nS=(0,rc.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),nV=(0,rc.createContext)({});function nM(e){return n(e.animate)||h.some(t=>o(e[t]))}function nR(e){return!!(nM(e)||e.variants)}function nL(e){return Array.isArray(e)?e.join(" "):e}let nD="undefined"!=typeof window,nI={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nB={};for(let e in nI)nB[e]={isEnabled:t=>nI[e].some(e=>!!t[e])};let nF=Symbol.for("motionComponentSymbol"),nz=nD?rc.useLayoutEffect:rc.useEffect,nO=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nW(e){if("string"!=typeof e||e.includes("-"));else if(nO.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let nU=e=>(t,i)=>{let r=(0,rc.useContext)(nV),s=(0,rc.useContext)(ru),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:i},r,s,a){let o={latestValues:function(e,t,i,r){let s={},a=r(e,{});for(let e in a)s[e]=rT(a[e]);let{initial:o,animate:l}=e,c=nM(e),u=nR(e);t&&u&&!c&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===l&&(l=t.animate));let h=!!i&&!1===i.initial,p=(h=h||!1===o)?l:o;if(p&&"boolean"!=typeof p&&!n(p)){let t=Array.isArray(p)?p:[p];for(let i=0;i<t.length;i++){let r=d(e,t[i]);if(r){let{transitionEnd:e,transition:t,...i}=r;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(s[e]=t)}for(let t in e)s[t]=e[t]}}}return s}(r,s,a,e),renderState:t()};return i&&(o.onMount=e=>i({props:r,current:e,...o}),o.onUpdate=e=>i(e)),o})(e,t,r,s);return i?a():function(e){let t=(0,rc.useRef)(null);return null===t.current&&(t.current=e()),t.current}(a)},n$=(e,t)=>t&&"number"==typeof e?t.transform(e):e,nH={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},n_=U.length;function nq(e,t,i){let{style:r,vars:n,transformOrigin:s}=e,a=!1,o=!1;for(let e in t){let i=t[e];if($.has(e)){a=!0;continue}if(tg(e)){n[e]=i;continue}{let t=n$(i,e7[e]);e.startsWith("origin")?(o=!0,s[e]=t):r[e]=t}}if(!t.transform&&(a||i?r.transform=function(e,t,i){let r="",n=!0;for(let s=0;s<n_;s++){let a=U[s],o=e[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let e=n$(o,e7[a]);if(!l){n=!1;let t=nH[a]||a;r+=`${t}(${e}) `}i&&(t[a]=e)}}return r=r.trim(),i?r=i(t,n?"":r):n&&(r="none"),r}(t,e.transform,i):r.transform&&(r.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=s;r.transformOrigin=`${e} ${t} ${i}`}}let nK={offset:"stroke-dashoffset",array:"stroke-dasharray"},nG={offset:"strokeDashoffset",array:"strokeDasharray"};function nY(e,t,i){return"string"==typeof e?e:eU.transform(t+i*e)}function nZ(e,{attrX:t,attrY:i,attrScale:r,originX:n,originY:s,pathLength:a,pathSpacing:o=1,pathOffset:l=0,...d},c,u){if(nq(e,d,u),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:p,dimensions:m}=e;h.transform&&(m&&(p.transform=h.transform),delete h.transform),m&&(void 0!==n||void 0!==s||p.transform)&&(p.transformOrigin=function(e,t,i){let r=nY(t,e.x,e.width),n=nY(i,e.y,e.height);return`${r} ${n}`}(m,void 0!==n?n:.5,void 0!==s?s:.5)),void 0!==t&&(h.x=t),void 0!==i&&(h.y=i),void 0!==r&&(h.scale=r),void 0!==a&&function(e,t,i=1,r=0,n=!0){e.pathLength=1;let s=n?nK:nG;e[s.offset]=eU.transform(-r);let a=eU.transform(t),o=eU.transform(i);e[s.array]=`${a} ${o}`}(h,a,o,l,!1)}let nX=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),nJ=()=>({...nX(),attrs:{}}),nQ=e=>"string"==typeof e&&"svg"===e.toLowerCase();function n0(e,{style:t,vars:i},r,n){for(let s in Object.assign(e.style,t,n&&n.getProjectionStyles(r)),i)e.style.setProperty(s,i[s])}let n1=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function n2(e,t,i,r){for(let i in n0(e,t,void 0,r),t.attrs)e.setAttribute(n1.has(i)?i:eu(i),t.attrs[i])}function n5(e,{layout:t,layoutId:i}){return $.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!rg[e]||"opacity"===e)}function n3(e,t,i){var r;let{style:n}=e,s={};for(let a in n)(ed(n[a])||t.style&&ed(t.style[a])||n5(a,e)||(null==(r=null==i?void 0:i.getValue(a))?void 0:r.liveStyle)!==void 0)&&(s[a]=n[a]);return s}function n6(e,t,i){let r=n3(e,t,i);for(let i in e)(ed(e[i])||ed(t[i]))&&(r[-1!==U.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return r}let n4=["x","y","width","height","cx","cy","r"],n9={useVisualState:nU({scrapeMotionValuesFromProps:n6,createRenderState:nJ,onUpdate:({props:e,prevProps:t,current:i,renderState:r,latestValues:n})=>{if(!i)return;let s=!!e.drag;if(!s){for(let e in n)if($.has(e)){s=!0;break}}if(!s)return;let a=!t;if(t)for(let i=0;i<n4.length;i++){let r=n4[i];e[r]!==t[r]&&(a=!0)}a&&Z.read(()=>{!function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}}(i,r),Z.render(()=>{nZ(r,n,nQ(i.tagName),e.transformTemplate),n2(i,r)})})}})},n7={useVisualState:nU({scrapeMotionValuesFromProps:n3,createRenderState:nX})};function n8(e,t,i){for(let r in t)ed(t[r])||n5(r,i)||(e[r]=t[r])}let se=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function st(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||se.has(e)}let si=e=>!st(e);try{!function(e){e&&(si=t=>t.startsWith("on")?!st(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}let sr={current:null},sn={current:!1},ss=[...tj,eK,e2],sa=e=>ss.find(tP(e)),so=new WeakMap,sl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sd{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tm,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=et.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,Z.render(this.render,!1,!0))};let{latestValues:o,renderState:l,onUpdate:d}=s;this.onUpdate=d,this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=nM(t),this.isVariantNode=nR(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...u}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in u){let t=u[e];void 0!==o[e]&&ed(t)&&t.set(o[e],!1)}}mount(e){this.current=e,so.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),sn.current||function(){if(sn.current=!0,nD)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>sr.current=e.matches;e.addListener(t),t()}else sr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in so.delete(this.current),this.projection&&this.projection.unmount(),X(this.notifyUpdate),X(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=$.has(e),n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&Z.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{n(),s(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in nB){let t=nB[e];if(!t)continue;let{isEnabled:i,Feature:r}=t;if(!this.features[e]&&r&&i(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iZ()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<sl.length;t++){let i=sl[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(e,t,i){for(let r in t){let n=t[r],s=i[r];if(ed(n))e.addValue(r,n);else if(ed(s))e.addValue(r,el(n,{owner:e}));else if(s!==n)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(n):t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(r);e.addValue(r,el(void 0!==t?t:n,{owner:e}))}}for(let r in i)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=el(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){var i;let r=void 0===this.latestValues[e]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,e))?i:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(tf(r)||eT(r))?r=parseFloat(r):!sa(r)&&e2.test(t)&&(r=tt(e,t)),this.setBaseTarget(e,ed(r)?r.get():r)),ed(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let i,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let n=d(this.props,r,null==(t=this.presenceContext)?void 0:t.custom);n&&(i=n[e])}if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||ed(n)?void 0!==this.initialValues[e]&&void 0===i?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new en),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class sc extends sd{constructor(){super(...arguments),this.KeyframeResolver=tT}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;ed(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class su extends sc{constructor(){super(...arguments),this.type="html",this.renderInstance=n0}readValueFromInstance(e,t){if($.has(t)){let e=te(t);return e&&e.default||0}{let i=window.getComputedStyle(e),r=(tg(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return i8(e,t)}build(e,t,i){nq(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return n3(e,t,i)}}class sh extends sc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iZ}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if($.has(t)){let e=te(t);return e&&e.default||0}return t=n1.has(t)?t:eu(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return n6(e,t,i)}build(e,t,i){nZ(e,t,this.isSVGTag,i.transformTemplate)}renderInstance(e,t,i,r){n2(e,t,i,r)}mount(e){this.isSVGTag=nQ(e.tagName),super.mount(e)}}let sp=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(i,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((ro={animation:{Feature:ij},exit:{Feature:iT},inView:{Feature:nk},tap:{Feature:nP},focus:{Feature:nb},hover:{Feature:nv},pan:{Feature:ra},drag:{Feature:rn,ProjectionNode:ng,MeasureLayout:rw},layout:{ProjectionNode:ng,MeasureLayout:rw}},rl=(e,t)=>nW(e)?new sh(t):new su(t,{allowProjection:e!==rc.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:r,Component:n}){var s,a;function l(e,s){var a,l,d;let c,u={...(0,rc.useContext)(nS),...e,layoutId:function({layoutId:e}){let t=(0,rc.useContext)(rh).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=u,p=function(e){let{initial:t,animate:i}=function(e,t){if(nM(e)){let{initial:t,animate:i}=e;return{initial:!1===t||o(t)?t:void 0,animate:o(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,rc.useContext)(nV));return(0,rc.useMemo)(()=>({initial:t,animate:i}),[nL(t),nL(i)])}(e),m=r(e,h);if(!h&&nD){l=0,d=0,(0,rc.useContext)(nC).strict;let e=function(e){let{drag:t,layout:i}=nB;if(!t&&!i)return{};let r={...t,...i};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==i?void 0:i.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);c=e.MeasureLayout,p.visualElement=function(e,t,i,r,n){var s,a;let{visualElement:o}=(0,rc.useContext)(nV),l=(0,rc.useContext)(nC),d=(0,rc.useContext)(ru),c=(0,rc.useContext)(nS).reducedMotion,u=(0,rc.useRef)(null);r=r||l.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:c}));let h=u.current,p=(0,rc.useContext)(rp);h&&!h.projection&&n&&("html"===h.type||"svg"===h.type)&&function(e,t,i,r){let{layoutId:n,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:d}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!a||o&&iI(o),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:d})}(u.current,i,n,p);let m=(0,rc.useRef)(!1);(0,rc.useInsertionEffect)(()=>{h&&m.current&&h.update(i,d)});let f=i[eh],y=(0,rc.useRef)(!!f&&!(null==(s=window.MotionHandoffIsComplete)?void 0:s.call(window,f))&&(null==(a=window.MotionHasOptimisedAnimation)?void 0:a.call(window,f)));return nz(()=>{h&&(m.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),rx.render(h.render),y.current&&h.animationState&&h.animationState.animateChanges())}),(0,rc.useEffect)(()=>{h&&(!y.current&&h.animationState&&h.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var e;null==(e=window.MotionHandoffMarkAsComplete)||e.call(window,f)}),y.current=!1))}),h}(n,m,u,t,e.ProjectionNode)}return(0,rd.jsxs)(nV.Provider,{value:p,children:[c&&p.visualElement?(0,rd.jsx)(c,{visualElement:p.visualElement,...u}):null,i(n,e,(a=p.visualElement,(0,rc.useCallback)(e=>{e&&m.onMount&&m.onMount(e),a&&(e?a.mount(e):a.unmount()),s&&("function"==typeof s?s(e):iI(s)&&(s.current=e))},[a])),m,h,p.visualElement)]})}e&&function(e){for(let t in e)nB[t]={...nB[t],...e[t]}}(e),l.displayName=`motion.${"string"==typeof n?n:`create(${null!=(a=null!=(s=n.displayName)?s:n.name)?a:""})`}`;let d=(0,rc.forwardRef)(l);return d[nF]=n,d}({...nW(e)?n9:n7,preloadedFeatures:ro,useRender:function(e=!1){return(t,i,r,{latestValues:n},s)=>{let a=(nW(t)?function(e,t,i,r){let n=(0,rc.useMemo)(()=>{let i=nJ();return nZ(i,t,nQ(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};n8(t,e.style,e),n.style={...t,...n.style}}return n}:function(e,t){let i={},r=function(e,t){let i=e.style||{},r={};return n8(r,i,e),Object.assign(r,function({transformTemplate:e},t){return(0,rc.useMemo)(()=>{let i=nX();return nq(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,t),o=function(e,t,i){let r={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(si(n)||!0===i&&st(n)||!t&&!st(n)||e.draggable&&n.startsWith("onDrag"))&&(r[n]=e[n]);return r}(i,"string"==typeof t,e),l=t!==rc.Fragment?{...o,...a,ref:r}:{},{children:d}=i,c=(0,rc.useMemo)(()=>ed(d)?d.get():d,[d]);return(0,rc.createElement)(t,{...l,children:c})}}(t),createVisualElement:rl,Component:e})}))},4433:(e,t,i)=>{"use strict";i.d(t,{default:()=>d});var r=i(687),n=i(4156),s=i(3210);let a=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}),o=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))});var l=i(679);let d=()=>(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-primary/5 py-20",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-6 text-center",children:(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsx)(n.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6",children:"Strategic Partnership"}),(0,r.jsxs)(n.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-4xl md:text-5xl font-bold text-default mb-8 leading-tight",children:["Why ",(0,r.jsx)("span",{className:"gradient-text",children:"Paradise Indonesia"}),"?"]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"bg-white rounded-2xl p-12 shadow-xl border border-gray-100 mb-12",children:[(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 mb-12",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(a,{className:"w-8 h-8 text-white"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-default mb-3",children:"Legal Integrity"}),(0,r.jsx)("p",{className:"text-muted",children:"Known for expertise and market presence in Bali real estate law"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(o,{className:"w-8 h-8 text-white"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-default mb-3",children:"Market Reputation"}),(0,r.jsx)("p",{className:"text-muted",children:"Established trust and credibility with international clients"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(l.A,{className:"w-8 h-8 text-white"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-default mb-3",children:"Perfect Synergy"}),(0,r.jsx)("p",{className:"text-muted",children:"They bring reputation, we bring reach and modern technology"})]})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.6},className:"bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-8 border border-primary/20",children:[(0,r.jsxs)("blockquote",{className:"text-2xl md:text-3xl font-bold text-default mb-4 leading-relaxed",children:['"You bring ',(0,r.jsx)("span",{className:"gradient-text",children:"reputation"}),".",(0,r.jsx)("br",{}),"We bring ",(0,r.jsx)("span",{className:"gradient-text",children:"reach"}),'."']}),(0,r.jsx)("p",{className:"text-lg text-muted",children:"Together, we create the perfect foundation for trust in Bali's property market."})]})]})]})})})},5097:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),n=i(4156),s=i(3210);let a=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))});var o=i(3635);let l=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m0-10.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.75c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.25-8.25-3.286Zm0 13.036h.008v.008H12v-.008Z"}))}),d=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}),c=()=>{let e=[{icon:a,title:"Legal Confusion",description:"Buyers are confused by leasehold vs ownership rules",color:"text-red-500"},{icon:o.A,title:"Limited Access",description:"Sellers lack access to serious foreign buyers",color:"text-orange-500"},{icon:l,title:"Trust Issues",description:"Trust in the real estate space is critically low",color:"text-yellow-500"}];return(0,r.jsxs)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,r.jsx)(n.P.div,{className:"absolute top-1/4 left-1/4 w-96 h-96 border border-muted rounded-lg transform rotate-12",animate:{rotate:[12,15,12]},transition:{duration:8,repeat:1/0}}),(0,r.jsx)(n.P.div,{className:"absolute bottom-1/4 right-1/4 w-64 h-64 border border-muted rounded-lg transform -rotate-6",animate:{rotate:[-6,-9,-6]},transition:{duration:6,repeat:1/0}})]}),(0,r.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-6",children:(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"mb-6",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-full text-sm font-semibold",children:[(0,r.jsx)(d,{className:"w-4 h-4 mr-2"}),"Market Challenge"]})}),(0,r.jsxs)(n.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight",children:["The ",(0,r.jsx)("span",{className:"gradient-text",children:"Problem"})," We're Solving"]}),(0,r.jsx)(n.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-xl text-muted mb-12 leading-relaxed",children:"Bali's property market is plagued by uncertainty, confusion, and mistrust. Both buyers and sellers struggle in an environment lacking transparency."}),(0,r.jsx)("div",{className:"space-y-6",children:e.map((e,t)=>(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.5+.1*t},className:"flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow",children:[(0,r.jsx)("div",{className:`flex-shrink-0 w-12 h-12 ${e.color} bg-gray-50 rounded-lg flex items-center justify-center`,children:(0,r.jsx)(e.icon,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-default mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-muted",children:e.description})]})]},t))})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"relative",children:[(0,r.jsxs)("div",{className:"relative",children:[[void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(n.P.div,{className:"absolute inset-0 bg-white border border-gray-200 rounded-lg shadow-lg",style:{transform:`translate(${8*t}px, ${8*t}px) rotate(${2*t}deg)`,zIndex:3-t},animate:{rotate:[2*t,2*t+2,2*t]},transition:{duration:4+t,repeat:1/0,ease:"easeInOut"}},t)),(0,r.jsxs)("div",{className:"relative z-10 bg-white border-2 border-red-200 rounded-lg p-8 h-96",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(a,{className:"w-8 h-8 text-red-500 mr-3"}),(0,r.jsx)("h3",{className:"text-xl font-bold text-default",children:"Legal Document"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-red-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/2"}),(0,r.jsx)("div",{className:"mt-8 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d,{className:"w-5 h-5 text-red-500 mr-2"}),(0,r.jsx)("span",{className:"text-red-700 font-semibold text-sm",children:"UNCLEAR TERMS"})]})})]})]})]}),[void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(n.P.div,{className:"absolute w-8 h-8 bg-red-100 text-red-500 rounded-full flex items-center justify-center font-bold text-lg",style:{top:`${20+25*t}%`,right:`${-10+5*t}%`},animate:{y:[0,-10,0],rotate:[0,5,0]},transition:{duration:2+.5*t,repeat:1/0,delay:.3*t},children:"?"},t))]})]})})]})}},5331:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\FinalCTA.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\FinalCTA.tsx","default")},5617:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});var r=i(687),n=i(3210),s=i(4156);let a=()=>{let[e,t]=(0,n.useState)(1);return(0,n.useEffect)(()=>{let e=()=>{let e=document.querySelectorAll(".snap-section"),i=window.scrollY+window.innerHeight/2;e.forEach((e,r)=>{let n=e.offsetTop,s=n+e.offsetHeight;i>=n&&i<s&&t(r+1)})};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,r.jsxs)("div",{className:"fixed right-8 top-1/2 transform -translate-y-1/2 z-50 hidden lg:block",children:[(0,r.jsx)("div",{className:"flex flex-col space-y-3",children:Array.from({length:9},(t,i)=>(0,r.jsx)(s.P.div,{className:`w-3 h-3 rounded-full border-2 border-primary cursor-pointer transition-all duration-300 ${e===i+1?"bg-primary scale-125":"bg-transparent hover:bg-primary/20"}`,whileHover:{scale:1.2},onClick:()=>{let e=document.querySelectorAll(".snap-section")[i];e?.scrollIntoView({behavior:"smooth"})}},i))}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsxs)("span",{className:"text-sm text-muted font-medium",children:[e," / ",9]})})]})}},5709:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},5990:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),n=i(4156),s=i(3210);let a=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var o=i(2969);let l=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}),d=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}),c=()=>(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-accent/10 py-20",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-6 text-center",children:(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsxs)(n.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"text-4xl md:text-6xl font-bold text-default mb-8 leading-tight",children:["Let's Launch the ",(0,r.jsx)("span",{className:"gradient-text",children:"Pilot"})]}),(0,r.jsx)(n.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-xl text-muted mb-12 max-w-3xl mx-auto",children:"Ready to transform Bali's property market together? Let's start with our 4-week pilot campaign and build something extraordinary."}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"flex flex-col sm:flex-row gap-6 justify-center mb-16",children:[(0,r.jsxs)(n.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-primary text-lg group relative overflow-hidden",children:[(0,r.jsxs)("span",{className:"relative z-10 flex items-center",children:[(0,r.jsx)(a,{className:"w-6 h-6 mr-2"}),"Approve Collaboration"]}),(0,r.jsx)(n.P.div,{className:"absolute inset-0 bg-gradient-to-r from-accent to-primary",initial:{x:"-100%"},whileHover:{x:0},transition:{duration:.3}})]}),(0,r.jsxs)(n.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-secondary text-lg group",children:[(0,r.jsx)(o.A,{className:"w-6 h-6 mr-2"}),"Book an Alignment Call"]})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"bg-white rounded-2xl p-8 shadow-xl border border-gray-100 max-w-4xl mx-auto",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-default mb-8",children:"Get In Touch"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,r.jsxs)(n.P.a,{href:"mailto:<EMAIL>",whileHover:{scale:1.05},className:"flex flex-col items-center p-6 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors group",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",children:(0,r.jsx)(l,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("h4",{className:"font-semibold text-default mb-2",children:"Email"}),(0,r.jsx)("p",{className:"text-sm text-muted",children:"<EMAIL>"})]}),(0,r.jsxs)(n.P.a,{href:"https://wa.me/1234567890",whileHover:{scale:1.05},className:"flex flex-col items-center p-6 rounded-xl bg-green-50 hover:bg-green-100 transition-colors group",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",children:(0,r.jsx)(d,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("h4",{className:"font-semibold text-default mb-2",children:"WhatsApp"}),(0,r.jsx)("p",{className:"text-sm text-muted",children:"+62 123 456 7890"})]}),(0,r.jsxs)(n.P.a,{href:"https://calendly.com/propertyplaza",whileHover:{scale:1.05},className:"flex flex-col items-center p-6 rounded-xl bg-primary/5 hover:bg-primary/10 transition-colors group",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("h4",{className:"font-semibold text-default mb-2",children:"Calendly"}),(0,r.jsx)("p",{className:"text-sm text-muted",children:"Schedule a meeting"})]})]})]}),(0,r.jsx)(n.P.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.8},className:"mt-12 text-center",children:(0,r.jsx)("p",{className:"text-muted text-sm",children:"\xa9 2024 Property Plaza \xd7 Paradise Indonesia Strategic Partnership"})})]})})})},5994:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});var r=i(3210);let n=r.forwardRef(function({title:e,titleId:t,...i},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},6108:(e,t,i)=>{"use strict";i.d(t,{default:()=>s});var r=i(687),n=i(4156);let s=()=>(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background py-20",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-6 text-center",children:(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsxs)(n.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-4xl md:text-5xl font-bold text-default mb-12 leading-tight",children:["Together We Offer ",(0,r.jsx)("span",{className:"gradient-text",children:"Certainty"}),(0,r.jsx)("br",{}),"In An Uncertain Market"]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.8,delay:.4},className:"relative",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-8 mb-12",children:[(0,r.jsx)(n.P.div,{className:"w-32 h-32 bg-gradient-to-br from-primary to-accent rounded-2xl flex items-center justify-center shadow-xl",whileHover:{scale:1.1,rotate:5},children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:"PP"})}),(0,r.jsx)(n.P.div,{className:"text-4xl text-primary",animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0},children:"+"}),(0,r.jsx)(n.P.div,{className:"w-32 h-32 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl",whileHover:{scale:1.1,rotate:-5},children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:"PI"})})]}),(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"text-xl text-muted max-w-3xl mx-auto",children:"When Property Plaza's innovative technology meets Paradise Indonesia's trusted expertise, we create an unmatched foundation for confident property decisions in Bali."})]})]})})})},6151:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\components\\\\ScrollIndicator.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\components\\ScrollIndicator.tsx","default")},6289:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Synergy.tsx","default")},6524:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});var r=i(3210);let n=r.forwardRef(function({title:e,titleId:t,...i},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},6562:(e,t,i)=>{Promise.resolve().then(i.bind(i,6151)),Promise.resolve().then(i.bind(i,1199)),Promise.resolve().then(i.bind(i,5331)),Promise.resolve().then(i.bind(i,3398)),Promise.resolve().then(i.bind(i,677)),Promise.resolve().then(i.bind(i,2492)),Promise.resolve().then(i.bind(i,2674)),Promise.resolve().then(i.bind(i,2117)),Promise.resolve().then(i.bind(i,6289)),Promise.resolve().then(i.bind(i,4056))},7179:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});var r=i(687),n=i(4156),s=i(3210);let a=s.forwardRef(function({title:e,titleId:t,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}),o=()=>{let e=()=>{let e=document.querySelectorAll(".snap-section")[1];e?.scrollIntoView({behavior:"smooth"})};return(0,r.jsxs)("section",{className:"snap-section relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-primary/5",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:`radial-gradient(circle at 25% 25%, #b78b4c 2px, transparent 2px),
                           radial-gradient(circle at 75% 75%, #8a6b3c 1px, transparent 1px)`,backgroundSize:"60px 60px"}})}),(0,r.jsx)(n.P.div,{className:"absolute top-20 left-20 w-32 h-32 rounded-full bg-primary/10 blur-xl",animate:{y:[0,-20,0],scale:[1,1.1,1]},transition:{duration:6,repeat:1/0,ease:"easeInOut"}}),(0,r.jsx)(n.P.div,{className:"absolute bottom-32 right-32 w-24 h-24 rounded-full bg-accent/10 blur-xl",animate:{y:[0,20,0],scale:[1,.9,1]},transition:{duration:8,repeat:1/0,ease:"easeInOut"}}),(0,r.jsxs)("div",{className:"relative z-10 text-center max-w-6xl mx-auto px-6",children:[(0,r.jsx)(n.P.div,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"mb-8",children:(0,r.jsxs)("div",{className:"inline-flex items-center space-x-4 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"PP"})}),(0,r.jsx)("span",{className:"text-default font-semibold",children:"Property Plaza"}),(0,r.jsx)("span",{className:"text-muted",children:"\xd7"}),(0,r.jsx)("span",{className:"text-default font-semibold",children:"Paradise Indonesia"})]})}),(0,r.jsxs)(n.P.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-5xl md:text-7xl lg:text-8xl font-bold mb-6 leading-tight",children:[(0,r.jsx)("span",{className:"gradient-text",children:"Empower"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-default",children:"Property Decisions"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-primary",children:"in Bali"})]}),(0,r.jsxs)(n.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"text-xl md:text-2xl text-muted mb-12 max-w-3xl mx-auto leading-relaxed",children:[(0,r.jsx)("span",{className:"font-semibold text-primary",children:"Transparency."})," ",(0,r.jsx)("span",{className:"font-semibold text-primary",children:"Knowledge."})," ",(0,r.jsx)("span",{className:"font-semibold text-primary",children:"Connection."})," ",(0,r.jsx)("span",{className:"font-semibold text-primary",children:"Empowerment."})]}),(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},children:(0,r.jsxs)("button",{onClick:e,className:"btn-primary text-lg group relative overflow-hidden",children:[(0,r.jsx)("span",{className:"relative z-10",children:"Start the Experience"}),(0,r.jsx)(n.P.div,{className:"absolute inset-0 bg-gradient-to-r from-accent to-primary",initial:{x:"-100%"},whileHover:{x:0},transition:{duration:.3}})]})}),(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:1.2},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,r.jsxs)(n.P.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"flex flex-col items-center text-muted cursor-pointer",onClick:e,children:[(0,r.jsx)("span",{className:"text-sm mb-2 font-medium",children:"Scroll to explore"}),(0,r.jsx)(a,{className:"w-6 h-6"})]})})]})]})}},8014:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o,metadata:()=>a});var r=i(7413);i(2704);var n=i(7995),s=i.n(n);let a={title:"Property Plaza \xd7 Paradise Indonesia | Strategic Partnership Proposal",description:"Empowering Property Decisions in Bali through Transparency, Knowledge, Connection & Trust. A strategic collaboration proposal.",keywords:"Property Plaza, Paradise Indonesia, Bali Real Estate, Property Investment, Legal Transparency",authors:[{name:"Property Plaza"}],openGraph:{title:"Property Plaza \xd7 Paradise Indonesia Partnership",description:"Strategic collaboration proposal for empowering property decisions in Bali",type:"website"}};function o({children:e}){return(0,r.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,r.jsx)("body",{className:s().className,children:e})})}},8509:()=>{},8931:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),n=i(4156),s=i(3635),a=i(3210);let o=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))});var l=i(6524),d=i(5994);let c=()=>{let e=[{icon:s.A,title:"Leads Generated",target:"50+",description:"Qualified property inquiries"},{icon:o,title:"WhatsApp Inquiries",target:"25+",description:"Direct consultation requests"},{icon:l.A,title:"Views & Impressions",target:"10K+",description:"Total content reach"},{icon:d.A,title:"Audience Growth",target:"15%",description:"Instagram/Facebook followers"}];return(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-gray-50 py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-6",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsx)(n.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6",children:"Success Metrics"}),(0,r.jsxs)(n.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight",children:[(0,r.jsx)("span",{className:"gradient-text",children:"Metrics"})," & KPIs"]}),(0,r.jsx)(n.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-xl text-muted max-w-3xl mx-auto",children:"Tracked via Google Data Studio for complete transparency and real-time insights."})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center",children:[(0,r.jsx)(n.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,r.jsx)("div",{className:"grid grid-cols-2 gap-6",children:e.map((e,t)=>(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},whileHover:{scale:1.05},className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:e.target}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-default mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-muted",children:e.description})]},t))})}),(0,r.jsx)(n.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-primary to-accent p-6 text-white",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-2",children:"Campaign Analytics Dashboard"}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:"Real-time performance tracking"})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg h-32 mb-6 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-muted",children:[(0,r.jsx)(d.A,{className:"w-8 h-8 mx-auto mb-2"}),(0,r.jsx)("span",{className:"text-sm",children:"Google Data Studio Integration"})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"↗ 24%"}),(0,r.jsx)("div",{className:"text-sm text-green-700",children:"Engagement Rate"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"8.2K"}),(0,r.jsx)("div",{className:"text-sm text-blue-700",children:"Total Reach"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"42"}),(0,r.jsx)("div",{className:"text-sm text-purple-700",children:"New Leads"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:"18"}),(0,r.jsx)("div",{className:"text-sm text-orange-700",children:"WhatsApp Chats"})]})]})]})]})})]})]})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9271:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=i(5239),n=i(8088),s=i(8170),a=i.n(s),o=i(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,597)),"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,8014)),"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\page.tsx"],u={require:i,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[891],()=>i(9271));module.exports=r})();