(()=>{var e={};e.id=974,e.ids=[974],e.modules={512:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return f},defaultHead:function(){return u}});let r=i(4985),s=i(740),n=i(687),a=s._(i(3210)),o=r._(i(7755)),l=i(4959),d=i(9513),c=i(4604);function u(e){void 0===e&&(e=!1);let t=[(0,n.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,n.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function h(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}i(148);let m=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:i}=t;return e.reduce(h,[]).reverse().concat(u(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,r={};return s=>{let n=!0,a=!1;if(s.key&&"number"!=typeof s.key&&s.key.indexOf("$")>0){a=!0;let t=s.key.slice(s.key.indexOf("$")+1);e.has(t)?n=!1:e.add(t)}switch(s.type){case"title":case"base":t.has(s.type)?n=!1:t.add(s.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(s.props.hasOwnProperty(t))if("charSet"===t)i.has(t)?n=!1:i.add(t);else{let e=s.props[t],i=r[t]||new Set;("name"!==t||!a)&&i.has(e)?n=!1:(i.add(e),r[t]=i)}}}return n}}()).reverse().map((e,t)=>{let r=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!i&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:r})})}let f=function(e){let{children:t}=e,i=(0,a.useContext)(l.AmpStateContext),r=(0,a.useContext)(d.HeadManagerContext);return(0,n.jsx)(o.default,{reduceComponentsToState:p,headManager:r,inAmpMode:(0,c.isInAmpMode)(i),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},597:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>p});var r=i(7413),s=i(677),n=i(2492),a=i(3398),o=i(1199),l=i(4056),d=i(6289),c=i(2117),u=i(2674),h=i(5331),m=i(6151);function p(){return(0,r.jsxs)("main",{className:"relative",children:[(0,r.jsx)(m.default,{}),(0,r.jsx)(s.default,{}),(0,r.jsx)(n.default,{}),(0,r.jsx)(a.default,{}),(0,r.jsx)(o.default,{}),(0,r.jsx)(l.default,{}),(0,r.jsx)(d.default,{}),(0,r.jsx)(c.default,{}),(0,r.jsx)(u.default,{}),(0,r.jsx)(h.default,{})]})}},629:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});var r=i(687),s=i(4156),n=i(1261),a=i.n(n);let o=()=>(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background py-20",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-6 text-center",children:(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsxs)(s.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-3xl sm:text-4xl md:text-5xl font-bold text-default mb-8 md:mb-12 leading-tight",children:["Together We Offer ",(0,r.jsx)("span",{className:"gradient-text",children:"Certainty"}),(0,r.jsx)("br",{className:"hidden sm:block"}),(0,r.jsx)("span",{className:"sm:hidden",children:" "}),"In An Uncertain Market"]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.8,delay:.4},className:"relative",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center mb-8 md:mb-12 gap-4 sm:gap-8",children:[(0,r.jsx)(s.P.div,{className:"flex items-center justify-center w-32 h-24 sm:w-40 sm:h-28 md:w-48 md:h-32",whileHover:{scale:1.05,rotate:2},transition:{duration:.3},children:(0,r.jsx)(a(),{src:"/images/Transparent -02.png",alt:"Property Plaza Logo",width:400,height:400,className:"object-contain drop-shadow-lg"})}),(0,r.jsx)(s.P.div,{className:"text-2xl sm:text-3xl md:text-4xl text-primary flex items-center justify-center",animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0},children:"+"}),(0,r.jsx)(s.P.div,{className:"flex items-center justify-center w-32 h-24 sm:w-40 sm:h-28 md:w-48 md:h-32",whileHover:{scale:1.05,rotate:-2},transition:{duration:.3},children:(0,r.jsx)(a(),{src:"/images/paradise-indonesia.png",alt:"Paradise Indonesia Logo",width:160,height:160,className:"object-contain drop-shadow-lg"})})]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"space-y-2 sm:space-y-3 mb-4 sm:mb-6 max-w-2xl mx-auto px-4",children:[(0,r.jsx)("div",{className:"text-sm sm:text-base text-muted",children:"\uD83D\uDD17 Structure meets speed."}),(0,r.jsx)("div",{className:"text-sm sm:text-base text-muted",children:"\uD83D\uDCF1 Tech meets trust."}),(0,r.jsx)("div",{className:"text-sm sm:text-base text-muted",children:"\uD83E\uDDED Legal certainty meets creative access."})]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:1},className:"text-sm sm:text-base text-muted max-w-2xl mx-auto border-t border-primary/20 pt-4 px-4",children:["When trusted legal foundations meet digital innovation, Bali's property market gets what it's always needed: access without risk.",(0,r.jsx)("br",{className:"hidden sm:block"}),(0,r.jsx)("span",{className:"sm:hidden",children:" "}),(0,r.jsx)("span",{className:"text-primary font-medium",children:"That's what happens when Paradise Indonesia partners with Property Plaza."})]})]})]})})})},677:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Hero.tsx","default")},679:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var r=i(3210);let s=r.forwardRef(function({title:e,titleId:t,...i},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"}))})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},942:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var r=i(3210);let s=r.forwardRef(function({title:e,titleId:t,...i},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},1199:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\AboutPropertyPlaza.tsx","default")},1261:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return o}});let r=i(4985),s=i(4953),n=i(6533),a=r._(i(1933));function o(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=n.Image},1480:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:s,blurDataURL:n,objectFit:a}=e,o=r?40*r:t,l=s?40*s:i,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},1933:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:r,width:s,quality:n}=e,a=n||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+s+"&q="+a+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),i.__next_img_default=!0;let r=i},2117:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\PilotCampaign.tsx","default")},2205:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,7924,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},2492:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\MarketProblem.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\MarketProblem.tsx","default")},2567:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});var r=i(687),s=i(4156);let n=()=>(0,r.jsxs)("section",{className:"snap-section relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-primary/5",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:`radial-gradient(circle at 25% 25%, #b78b4c 2px, transparent 2px),
                           radial-gradient(circle at 75% 75%, #8a6b3c 1px, transparent 1px)`,backgroundSize:"60px 60px"}})}),(0,r.jsx)(s.P.div,{className:"absolute top-10 sm:top-20 left-4 sm:left-20 w-16 h-16 sm:w-32 sm:h-32 rounded-full bg-primary/10 blur-xl hidden sm:block",animate:{y:[0,-20,0],scale:[1,1.1,1]},transition:{duration:6,repeat:1/0,ease:"easeInOut"}}),(0,r.jsx)(s.P.div,{className:"absolute bottom-16 sm:bottom-32 right-4 sm:right-32 w-12 h-12 sm:w-24 sm:h-24 rounded-full bg-accent/10 blur-xl hidden sm:block",animate:{y:[0,20,0],scale:[1,.9,1]},transition:{duration:8,repeat:1/0,ease:"easeInOut"}}),(0,r.jsxs)("div",{className:"relative z-10 text-center max-w-6xl mx-auto px-6",children:[(0,r.jsx)(s.P.div,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"mb-6 sm:mb-8",children:(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 sm:space-x-4 bg-white/80 backdrop-blur-sm rounded-full px-4 sm:px-6 py-2 sm:py-3 shadow-lg",children:[(0,r.jsx)("div",{className:"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-xs sm:text-sm",children:"PP"})}),(0,r.jsx)("span",{className:"text-default font-semibold text-sm sm:text-base",children:"Property Plaza"}),(0,r.jsx)("span",{className:"text-muted text-sm sm:text-base",children:"\xd7"}),(0,r.jsx)("span",{className:"text-default font-semibold text-sm sm:text-base",children:"Paradise Indonesia"})]})}),(0,r.jsxs)(s.P.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-3xl sm:text-5xl md:text-7xl lg:text-8xl font-bold mb-4 sm:mb-6 leading-tight",children:[(0,r.jsx)("span",{className:"gradient-text",children:"Empower"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-default",children:"Property Decisions"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-primary",children:"in Bali"})]}),(0,r.jsxs)(s.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"text-lg sm:text-xl md:text-2xl text-muted mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed px-4",children:[(0,r.jsx)("span",{className:"font-semibold text-primary",children:"Transparency."})," ",(0,r.jsx)("span",{className:"font-semibold text-primary",children:"Knowledge."})," ",(0,r.jsx)("span",{className:"font-semibold text-primary",children:"Connection."})," ",(0,r.jsx)("span",{className:"font-semibold text-primary",children:"Empowerment."})]}),(0,r.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},children:(0,r.jsxs)("button",{onClick:()=>{let e=document.querySelectorAll(".snap-section")[1];e?.scrollIntoView({behavior:"smooth"})},className:"btn-primary text-lg group relative overflow-hidden",children:[(0,r.jsx)("span",{className:"relative z-10",children:"Start the Experience"}),(0,r.jsx)(s.P.div,{className:"absolute inset-0 bg-gradient-to-r from-accent to-primary",initial:{x:"-100%"},whileHover:{x:0},transition:{duration:.3}})]})})]})]})},2623:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),s=i(4156),n=i(6524),a=i(3210);let o=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))});var l=i(679);let d=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))}),c=()=>{let e=[{icon:n.A,title:"Transparency",subtitle:"No surprises, clear ownership, price structure & possibiilities",description:"Every property listing includes clear ownership structure and transparent pricing with no hidden fees.",color:"from-blue-500 to-blue-600",bgColor:"bg-blue-50",textColor:"text-blue-600"},{icon:o,title:"Knowledge",subtitle:"Practical insight into real life and real risks on Bali",description:"From build quality to hiring locals, and from navigating bureaucracy to daily life tips - we guide you through everything you won’t find in legal docs or online searches.",color:"from-green-500 to-green-600",bgColor:"bg-green-50",textColor:"text-green-600"},{icon:l.A,title:"Connection",subtitle:"We link buyers, sellers & experts directly",description:"Direct connections between serious buyers, verified sellers, and trusted legal experts and/or staff, eliminating unnecessary intermediaries.",color:"from-purple-500 to-purple-600",bgColor:"bg-purple-50",textColor:"text-purple-600"},{icon:d,title:"Empowerment",subtitle:"When people understand, they act confidently",description:"Armed with complete information and expert guidance, you can make property decisions with complete confidence. Both buyers/renters and owners.",color:"from-primary to-accent",bgColor:"bg-primary/5",textColor:"text-primary"}];return(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-background py-12 sm:py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,r.jsx)(s.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-3 sm:px-4 py-2 bg-primary/10 text-primary rounded-full text-xs sm:text-sm font-semibold mb-4 sm:mb-6",children:"Our Foundation"}),(0,r.jsxs)(s.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-3xl sm:text-4xl md:text-5xl font-bold text-default mb-4 sm:mb-6",children:["The ",(0,r.jsx)("span",{className:"gradient-text",children:"Four Pillars"})," of Trust"]}),(0,r.jsx)(s.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-lg sm:text-xl text-muted max-w-3xl mx-auto px-4",children:"Our mission is built on four fundamental principles that transform how property decisions are made in Bali."})]}),(0,r.jsx)("div",{className:"grid sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8",children:e.map((e,t)=>(0,r.jsx)(s.P.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},whileHover:{y:-10,scale:1.02},className:"group cursor-pointer",children:(0,r.jsxs)("div",{className:`${e.bgColor} rounded-2xl p-6 sm:p-8 h-full border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-xl`,children:[(0,r.jsx)(s.P.div,{className:`w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br ${e.color} rounded-xl flex items-center justify-center mb-4 sm:mb-6 group-hover:scale-110 transition-transform duration-300`,children:(0,r.jsx)(e.icon,{className:"w-6 h-6 sm:w-8 sm:h-8 text-white"})}),(0,r.jsx)("h3",{className:`text-xl sm:text-2xl font-bold ${e.textColor} mb-2 sm:mb-3`,children:e.title}),(0,r.jsx)("p",{className:"text-default font-semibold mb-3 sm:mb-4 text-xs sm:text-sm",children:e.subtitle}),(0,r.jsx)("p",{className:"text-muted leading-relaxed text-xs sm:text-sm",children:e.description}),(0,r.jsx)(s.P.div,{className:`mt-6 w-full h-1 bg-gradient-to-r ${e.color} rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300`})]})},t))}),(0,r.jsx)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},className:"text-center mt-12 sm:mt-16",children:(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 text-muted",children:[(0,r.jsx)("div",{className:"flex space-x-1",children:e.map((e,t)=>(0,r.jsx)(s.P.div,{className:"w-2 h-2 bg-primary rounded-full",animate:{scale:[1,1.2,1]},transition:{duration:2,delay:.2*t,repeat:1/0}},t))}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Building trust through action"})]})})]})})}},2674:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Metrics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Metrics.tsx","default")},2704:()=>{},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return r}});let i=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},2969:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var r=i(3210);let s=r.forwardRef(function({title:e,titleId:t,...i},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return s}});let r=i(3210);function s(e,t){let i=(0,r.useRef)(null),s=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=i.current;e&&(i.current=null,e());let t=s.current;t&&(s.current=null,t())}else e&&(i.current=n(e,r)),t&&(s.current=n(t,r))},[e,t])}function n(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3206:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),s=i(4156),n=i(942),a=i(3635),o=i(3210);let l=o.forwardRef(function({title:e,titleId:t,...i},r){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))});var d=i(679);let c=()=>{let e=[{icon:n.A,title:"Legal Excellence",subtitle:"20+ years of experience in Bali real estate law",description:"You offer deep legal knowledge, compliance focus and an unmatched reputation in the local market.",color:"from-blue-500 to-blue-600"},{icon:a.A,title:"International Trust",subtitle:"A proven track record with global clients",description:"Your name carries weight — with investors, institutions and regulators alike.",color:"from-green-500 to-green-600"},{icon:l,title:"Ethical Foundation",subtitle:"Independent, client-first and transparent",description:"You don't chase commissions. You protect what matters — ownership and legal clarity.",color:"from-purple-500 to-purple-600"},{icon:d.A,title:"Modern Synergy",subtitle:"Your credibility meets our modern capability",description:"While you bring the legal trust, Property Plaza adds creative reach, fast onboarding, and a tech-driven user journey.",color:"from-primary to-accent"}];return(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-primary/5 py-12 sm:py-16",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-6 sm:mb-8",children:[(0,r.jsx)(s.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-3 sm:px-4 py-2 bg-primary/10 text-primary rounded-full text-xs sm:text-sm font-semibold mb-4 sm:mb-6",children:"\uD83E\uDD1D Strategic Partnership"}),(0,r.jsxs)(s.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-2xl sm:text-3xl md:text-4xl font-bold text-default mb-2 sm:mb-3 leading-tight",children:["Why ",(0,r.jsx)("span",{className:"gradient-text",children:"Paradise Indonesia"}),"?"]}),(0,r.jsx)(s.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-base sm:text-lg text-muted mb-4 sm:mb-6",children:"Legal roots meet digital wings"}),(0,r.jsx)(s.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},className:"text-sm lg:text-base text-muted max-w-3xl mx-auto mb-8 leading-relaxed",children:"In an industry where most players still rely on offline tactics and broker-driven deals, we're building a partnership that bridges tradition and transformation."})]}),(0,r.jsx)("div",{className:"grid lg:grid-cols-2 gap-4 lg:gap-6 mb-8",children:e.map((e,t)=>(0,r.jsx)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:`w-8 h-8 lg:w-10 lg:h-10 bg-gradient-to-br ${e.color} rounded-lg flex items-center justify-center flex-shrink-0`,children:(0,r.jsx)(e.icon,{className:"w-4 h-4 lg:w-5 lg:h-5 text-white"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)("span",{className:"text-primary text-sm lg:text-base mr-2",children:"\uD83D\uDD39"}),(0,r.jsx)("h3",{className:"text-base lg:text-lg font-bold text-default",children:e.title})]}),(0,r.jsx)("p",{className:"text-primary font-semibold mb-1 lg:mb-2 text-xs lg:text-sm",children:e.subtitle}),(0,r.jsx)("p",{className:"text-muted leading-relaxed text-xs lg:text-sm",children:e.description})]})]})},t))}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.6},viewport:{once:!0},className:"bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-6 lg:p-8 border border-primary/20 text-center",children:[(0,r.jsxs)("blockquote",{className:"text-lg lg:text-2xl xl:text-3xl font-bold text-default mb-3 lg:mb-4 leading-relaxed",children:['"You bring ',(0,r.jsx)("span",{className:"gradient-text",children:"trust and structure"}),".",(0,r.jsx)("br",{}),"We bring ",(0,r.jsx)("span",{className:"gradient-text",children:"speed, access and innovation"}),'."']}),(0,r.jsx)("p",{className:"text-sm lg:text-base text-muted max-w-2xl mx-auto",children:"Together, we're not just partnering — we're building a new era in Bali real estate."})]})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3398:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\FourPillars.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\FourPillars.tsx","default")},3418:(e,t,i)=>{Promise.resolve().then(i.bind(i,5617)),Promise.resolve().then(i.bind(i,9736)),Promise.resolve().then(i.bind(i,5990)),Promise.resolve().then(i.bind(i,2623)),Promise.resolve().then(i.bind(i,2567)),Promise.resolve().then(i.bind(i,5097)),Promise.resolve().then(i.bind(i,7180)),Promise.resolve().then(i.bind(i,9394)),Promise.resolve().then(i.bind(i,629)),Promise.resolve().then(i.bind(i,3206))},3635:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var r=i(3210);let s=r.forwardRef(function({title:e,titleId:t,...i},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},3773:()=>{},3873:e=>{"use strict";e.exports=require("path")},4056:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\WhyParadiseIndonesia.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\WhyParadiseIndonesia.tsx","default")},4156:(e,t,i)=>{"use strict";let r;function s(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}i.d(t,{P:()=>nm});let n=e=>Array.isArray(e);function a(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let r=0;r<i;r++)if(t[r]!==e[r])return!1;return!0}function o(e){return"string"==typeof e||Array.isArray(e)}function l(e){let t=[{},{}];return null==e||e.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function d(e,t,i,r){if("function"==typeof t){let[s,n]=l(r);t=t(void 0!==i?i:e.custom,s,n)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[s,n]=l(r);t=t(void 0!==i?i:e.custom,s,n)}return t}function c(e,t,i){let r=e.getProps();return d(r,t,void 0!==i?i:r.custom,e)}let u=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],h=["initial",...u];function m(e){let t;return()=>(void 0===t&&(t=e()),t)}let p=m(()=>void 0!==window.ScrollTimeline);class f{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let i=0;i<this.animations.length;i++)this.animations[i][e]=t}attachTimeline(e,t){let i=this.animations.map(i=>p()&&i.attachTimeline?i.attachTimeline(e):"function"==typeof t?t(i):void 0);return()=>{i.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class x extends f{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function g(e,t){return e?e[t]||e.default||e:void 0}function y(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function v(e){return"function"==typeof e}function b(e,t){e.timeline=t,e.onfinish=null}let w=e=>Array.isArray(e)&&"number"==typeof e[0],P={linearEasing:void 0},j=function(e,t){let i=m(e);return()=>{var e;return null!=(e=P[t])?e:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),A=(e,t,i)=>{let r=t-e;return 0===r?1:(i-e)/r},E=(e,t,i=10)=>{let r="",s=Math.max(Math.round(t/i),2);for(let t=0;t<s;t++)r+=e(A(0,s-1,t))+", ";return`linear(${r.substring(0,r.length-2)})`},T=([e,t,i,r])=>`cubic-bezier(${e}, ${t}, ${i}, ${r})`,k={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:T([0,.65,.55,1]),circOut:T([.55,0,1,.45]),backIn:T([.31,.01,.66,-.59]),backOut:T([.33,1.53,.69,.99])},N={x:!1,y:!1};function C(e,t){let i=function(e,t,i){if(e instanceof Element)return[e];if("string"==typeof e){let t=document.querySelectorAll(e);return t?Array.from(t):[]}return Array.from(e)}(e),r=new AbortController;return[i,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function S(e){return t=>{"touch"===t.pointerType||N.x||N.y||e(t)}}let V=(e,t)=>!!t&&(e===t||V(e,t.parentElement)),M=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,R=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),D=new WeakSet;function I(e){return t=>{"Enter"===t.key&&e(t)}}function L(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let O=(e,t)=>{let i=e.currentTarget;if(!i)return;let r=I(()=>{if(D.has(i))return;L(i,"down");let e=I(()=>{L(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>L(i,"cancel"),t)});i.addEventListener("keydown",r,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),t)};function B(e){return M(e)&&!(N.x||N.y)}let F=e=>1e3*e,z=e=>e/1e3,_=e=>e,W=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],U=new Set(W),$=new Set(["width","height","top","left","right","bottom",...W]),H=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),q=e=>n(e)?e[e.length-1]||0:e,Z={skipAnimations:!1,useManualTiming:!1},Y=["read","resolveKeyframes","update","preRender","render","postRender"];function G(e,t){let i=!1,r=!0,s={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,a=Y.reduce((e,t)=>(e[t]=function(e){let t=new Set,i=new Set,r=!1,s=!1,n=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1};function o(t){n.has(t)&&(l.schedule(t),e()),t(a)}let l={schedule:(e,s=!1,a=!1)=>{let o=a&&r?t:i;return s&&n.add(e),o.has(e)||o.add(e),e},cancel:e=>{i.delete(e),n.delete(e)},process:e=>{if(a=e,r){s=!0;return}r=!0,[t,i]=[i,t],t.forEach(o),t.clear(),r=!1,s&&(s=!1,l.process(e))}};return l}(n),e),{}),{read:o,resolveKeyframes:l,update:d,preRender:c,render:u,postRender:h}=a,m=()=>{let n=Z.useManualTiming?s.timestamp:performance.now();i=!1,s.delta=r?1e3/60:Math.max(Math.min(n-s.timestamp,40),1),s.timestamp=n,s.isProcessing=!0,o.process(s),l.process(s),d.process(s),c.process(s),u.process(s),h.process(s),s.isProcessing=!1,i&&t&&(r=!1,e(m))},p=()=>{i=!0,r=!0,s.isProcessing||e(m)};return{schedule:Y.reduce((e,t)=>{let r=a[t];return e[t]=(e,t=!1,s=!1)=>(i||p(),r.schedule(e,t,s)),e},{}),cancel:e=>{for(let t=0;t<Y.length;t++)a[Y[t]].cancel(e)},state:s,steps:a}}let{schedule:X,cancel:K,state:Q,steps:J}=G("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:_,!0);function ee(){r=void 0}let et={now:()=>(void 0===r&&et.set(Q.isProcessing||Z.useManualTiming?Q.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(ee)}};function ei(e,t){-1===e.indexOf(t)&&e.push(t)}function er(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class es{constructor(){this.subscriptions=[]}add(e){return ei(this.subscriptions,e),()=>er(this.subscriptions,e)}notify(e,t,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,i);else for(let s=0;s<r;s++){let r=this.subscriptions[s];r&&r(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let en=e=>!isNaN(parseFloat(e)),ea={current:void 0};class eo{constructor(e,t={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=et.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=et.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=en(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new es);let i=this.events[e].add(t);return"change"===e?()=>{i(),X.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ea.current&&ea.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=et.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function el(e,t){return new eo(e,t)}let ed=e=>!!(e&&e.getVelocity);function ec(e,t){let i=e.getValue("willChange");if(ed(i)&&i.add)return i.add(t)}let eu=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eh="data-"+eu("framerAppearId"),em={current:!1},ep=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function ef(e,t,i,r){if(e===t&&i===r)return _;let s=t=>(function(e,t,i,r,s){let n,a,o=0;do(n=ep(a=t+(i-t)/2,r,s)-e)>0?i=a:t=a;while(Math.abs(n)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:ep(s(e),t,r)}let ex=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,eg=e=>t=>1-e(1-t),ey=ef(.33,1.53,.69,.99),ev=eg(ey),eb=ex(ev),ew=e=>(e*=2)<1?.5*ev(e):.5*(2-Math.pow(2,-10*(e-1))),eP=e=>1-Math.sin(Math.acos(e)),ej=eg(eP),eA=ex(eP),eE=e=>/^0[^.\s]+$/u.test(e),eT=(e,t,i)=>i>t?t:i<e?e:i,ek={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},eN={...ek,transform:e=>eT(0,1,e)},eC={...ek,default:1},eS=e=>Math.round(1e5*e)/1e5,eV=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eM=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eR=(e,t)=>i=>!!("string"==typeof i&&eM.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),eD=(e,t,i)=>r=>{if("string"!=typeof r)return r;let[s,n,a,o]=r.match(eV);return{[e]:parseFloat(s),[t]:parseFloat(n),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},eI=e=>eT(0,255,e),eL={...ek,transform:e=>Math.round(eI(e))},eO={test:eR("rgb","red"),parse:eD("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:r=1})=>"rgba("+eL.transform(e)+", "+eL.transform(t)+", "+eL.transform(i)+", "+eS(eN.transform(r))+")"},eB={test:eR("#"),parse:function(e){let t="",i="",r="",s="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,i+=i,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}},transform:eO.transform},eF=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ez=eF("deg"),e_=eF("%"),eW=eF("px"),eU=eF("vh"),e$=eF("vw"),eH={...e_,parse:e=>e_.parse(e)/100,transform:e=>e_.transform(100*e)},eq={test:eR("hsl","hue"),parse:eD("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:r=1})=>"hsla("+Math.round(e)+", "+e_.transform(eS(t))+", "+e_.transform(eS(i))+", "+eS(eN.transform(r))+")"},eZ={test:e=>eO.test(e)||eB.test(e)||eq.test(e),parse:e=>eO.test(e)?eO.parse(e):eq.test(e)?eq.parse(e):eB.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eO.transform(e):eq.transform(e)},eY=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eG="number",eX="color",eK=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eQ(e){let t=e.toString(),i=[],r={color:[],number:[],var:[]},s=[],n=0,a=t.replace(eK,e=>(eZ.test(e)?(r.color.push(n),s.push(eX),i.push(eZ.parse(e))):e.startsWith("var(")?(r.var.push(n),s.push("var"),i.push(e)):(r.number.push(n),s.push(eG),i.push(parseFloat(e))),++n,"${}")).split("${}");return{values:i,split:a,indexes:r,types:s}}function eJ(e){return eQ(e).values}function e0(e){let{split:t,types:i}=eQ(e),r=t.length;return e=>{let s="";for(let n=0;n<r;n++)if(s+=t[n],void 0!==e[n]){let t=i[n];t===eG?s+=eS(e[n]):t===eX?s+=eZ.transform(e[n]):s+=e[n]}return s}}let e1=e=>"number"==typeof e?0:e,e2={test:function(e){var t,i;return isNaN(e)&&"string"==typeof e&&((null==(t=e.match(eV))?void 0:t.length)||0)+((null==(i=e.match(eY))?void 0:i.length)||0)>0},parse:eJ,createTransformer:e0,getAnimatableNone:function(e){let t=eJ(e);return e0(e)(t.map(e1))}},e5=new Set(["brightness","contrast","saturate","opacity"]);function e3(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=i.match(eV)||[];if(!r)return e;let s=i.replace(r,""),n=+!!e5.has(t);return r!==i&&(n*=100),t+"("+n+s+")"}let e4=/\b([a-z-]*)\(.*?\)/gu,e6={...e2,getAnimatableNone:e=>{let t=e.match(e4);return t?t.map(e3).join(" "):e}},e9={...ek,transform:Math.round},e8={borderWidth:eW,borderTopWidth:eW,borderRightWidth:eW,borderBottomWidth:eW,borderLeftWidth:eW,borderRadius:eW,radius:eW,borderTopLeftRadius:eW,borderTopRightRadius:eW,borderBottomRightRadius:eW,borderBottomLeftRadius:eW,width:eW,maxWidth:eW,height:eW,maxHeight:eW,top:eW,right:eW,bottom:eW,left:eW,padding:eW,paddingTop:eW,paddingRight:eW,paddingBottom:eW,paddingLeft:eW,margin:eW,marginTop:eW,marginRight:eW,marginBottom:eW,marginLeft:eW,backgroundPositionX:eW,backgroundPositionY:eW,rotate:ez,rotateX:ez,rotateY:ez,rotateZ:ez,scale:eC,scaleX:eC,scaleY:eC,scaleZ:eC,skew:ez,skewX:ez,skewY:ez,distance:eW,translateX:eW,translateY:eW,translateZ:eW,x:eW,y:eW,z:eW,perspective:eW,transformPerspective:eW,opacity:eN,originX:eH,originY:eH,originZ:eW,zIndex:e9,size:eW,fillOpacity:eN,strokeOpacity:eN,numOctaves:e9},e7={...e8,color:eZ,backgroundColor:eZ,outlineColor:eZ,fill:eZ,stroke:eZ,borderColor:eZ,borderTopColor:eZ,borderRightColor:eZ,borderBottomColor:eZ,borderLeftColor:eZ,filter:e6,WebkitFilter:e6},te=e=>e7[e];function tt(e,t){let i=te(e);return i!==e6&&(i=e2),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let ti=new Set(["auto","none","0"]),tr=e=>e===ek||e===eW,ts=(e,t)=>parseFloat(e.split(", ")[t]),tn=(e,t)=>(i,{transform:r})=>{if("none"===r||!r)return 0;let s=r.match(/^matrix3d\((.+)\)$/u);if(s)return ts(s[1],t);{let t=r.match(/^matrix\((.+)\)$/u);return t?ts(t[1],e):0}},ta=new Set(["x","y","z"]),to=W.filter(e=>!ta.has(e)),tl={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:tn(4,13),y:tn(5,14)};tl.translateX=tl.x,tl.translateY=tl.y;let td=new Set,tc=!1,tu=!1;function th(){if(tu){let e=Array.from(td).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return to.forEach(i=>{let r=e.getValue(i);void 0!==r&&(t.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{var r;null==(r=e.getValue(t))||r.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tu=!1,tc=!1,td.forEach(e=>e.complete()),td.clear()}function tm(){td.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tu=!0)})}class tp{constructor(e,t,i,r,s,n=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=r,this.element=s,this.isAsync=n}scheduleResolve(){this.isScheduled=!0,this.isAsync?(td.add(this),tc||(tc=!0,X.read(tm),X.resolveKeyframes(th))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:r}=this;for(let s=0;s<e.length;s++)if(null===e[s])if(0===s){let s=null==r?void 0:r.get(),n=e[e.length-1];if(void 0!==s)e[0]=s;else if(i&&t){let r=i.readValue(t,n);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=n),r&&void 0===s&&r.set(e[0])}else e[s]=e[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),td.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,td.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let tf=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),tx=e=>t=>"string"==typeof t&&t.startsWith(e),tg=tx("--"),ty=tx("var(--"),tv=e=>!!ty(e)&&tb.test(e.split("/*")[0].trim()),tb=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tw=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tP=e=>t=>t.test(e),tj=[ek,eW,e_,ez,e$,eU,{test:e=>"auto"===e,parse:e=>e}],tA=e=>tj.find(tP(e));class tE extends tp{constructor(e,t,i,r,s){super(e,t,i,r,s,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let r=e[i];if("string"==typeof r&&tv(r=r.trim())){let s=function e(t,i,r=1){_(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[s,n]=function(e){let t=tw.exec(e);if(!t)return[,];let[,i,r,s]=t;return[`--${null!=i?i:r}`,s]}(t);if(!s)return;let a=window.getComputedStyle(i).getPropertyValue(s);if(a){let e=a.trim();return tf(e)?parseFloat(e):e}return tv(n)?e(n,i,r+1):n}(r,t.current);void 0!==s&&(e[i]=s),i===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!$.has(i)||2!==e.length)return;let[r,s]=e,n=tA(r),a=tA(s);if(n!==a)if(tr(n)&&tr(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var r;("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||eE(r))&&i.push(t)}i.length&&function(e,t,i){let r,s=0;for(;s<e.length&&!r;){let t=e[s];"string"==typeof t&&!ti.has(t)&&eQ(t).values.length&&(r=e[s]),s++}if(r&&i)for(let s of t)e[s]=tt(i,r)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tl[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(i,r).jump(r,!1)}measureEndState(){var e;let{element:t,name:i,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let s=t.getValue(i);s&&s.jump(this.measuredOrigin,!1);let n=r.length-1,a=r[n];r[n]=tl[i](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),(null==(e=this.removedTransforms)?void 0:e.length)&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tT=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e2.test(e)||"0"===e)&&!e.startsWith("url(")),tk=e=>null!==e;function tN(e,{repeat:t,repeatType:i="loop"},r){let s=e.filter(tk),n=t&&"loop"!==i&&t%2==1?0:s.length-1;return n&&void 0!==r?r:s[n]}class tC{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:n="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=et.now(),this.options={autoplay:e,delay:t,type:i,repeat:r,repeatDelay:s,repeatType:n,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(tm(),th()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=et.now(),this.hasAttemptedResolve=!0;let{name:i,type:r,velocity:s,delay:n,onComplete:a,onUpdate:o,isGenerator:l}=this.options;if(!l&&!function(e,t,i,r){let s=e[0];if(null===s)return!1;if("display"===t||"visibility"===t)return!0;let n=e[e.length-1],a=tT(s,t),o=tT(n,t);return _(a===o,`You are trying to animate ${t} from "${s}" to "${n}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${n} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||v(i))&&r)}(e,i,r,s))if(em.current||!n){o&&o(tN(e,this.options,t)),a&&a(),this.resolveFinishedPromise();return}else this.options.duration=0;let d=this.initPlayback(e,t);!1!==d&&(this._resolved={keyframes:e,finalKeyframe:t,...d},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}let tS=(e,t,i)=>e+(t-e)*i;function tV(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function tM(e,t){return i=>i>0?t:e}let tR=(e,t,i)=>{let r=e*e,s=i*(t*t-r)+r;return s<0?0:Math.sqrt(s)},tD=[eB,eO,eq],tI=e=>tD.find(t=>t.test(e));function tL(e){let t=tI(e);if(_(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===eq&&(i=function({hue:e,saturation:t,lightness:i,alpha:r}){e/=360,i/=100;let s=0,n=0,a=0;if(t/=100){let r=i<.5?i*(1+t):i+t-i*t,o=2*i-r;s=tV(o,r,e+1/3),n=tV(o,r,e),a=tV(o,r,e-1/3)}else s=n=a=i;return{red:Math.round(255*s),green:Math.round(255*n),blue:Math.round(255*a),alpha:r}}(i)),i}let tO=(e,t)=>{let i=tL(e),r=tL(t);if(!i||!r)return tM(e,t);let s={...i};return e=>(s.red=tR(i.red,r.red,e),s.green=tR(i.green,r.green,e),s.blue=tR(i.blue,r.blue,e),s.alpha=tS(i.alpha,r.alpha,e),eO.transform(s))},tB=(e,t)=>i=>t(e(i)),tF=(...e)=>e.reduce(tB),tz=new Set(["none","hidden"]);function t_(e,t){return i=>tS(e,t,i)}function tW(e){return"number"==typeof e?t_:"string"==typeof e?tv(e)?tM:eZ.test(e)?tO:tH:Array.isArray(e)?tU:"object"==typeof e?eZ.test(e)?tO:t$:tM}function tU(e,t){let i=[...e],r=i.length,s=e.map((e,i)=>tW(e)(e,t[i]));return e=>{for(let t=0;t<r;t++)i[t]=s[t](e);return i}}function t$(e,t){let i={...e,...t},r={};for(let s in i)void 0!==e[s]&&void 0!==t[s]&&(r[s]=tW(e[s])(e[s],t[s]));return e=>{for(let t in r)i[t]=r[t](e);return i}}let tH=(e,t)=>{let i=e2.createTransformer(t),r=eQ(e),s=eQ(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?tz.has(e)&&!s.values.length||tz.has(t)&&!r.values.length?function(e,t){return tz.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):tF(tU(function(e,t){var i;let r=[],s={color:0,var:0,number:0};for(let n=0;n<t.values.length;n++){let a=t.types[n],o=e.indexes[a][s[a]],l=null!=(i=e.values[o])?i:0;r[n]=l,s[a]++}return r}(r,s),s.values),i):(_(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tM(e,t))};function tq(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?tS(e,t,i):tW(e)(e,t)}function tZ(e,t,i){var r,s;let n=Math.max(t-5,0);return r=i-e(n),(s=t-n)?1e3/s*r:0}let tY={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tG(e,t){return e*Math.sqrt(1-t*t)}let tX=["duration","bounce"],tK=["stiffness","damping","mass"];function tQ(e,t){return t.some(t=>void 0!==e[t])}function tJ(e=tY.visualDuration,t=tY.bounce){let i,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:s,restDelta:n}=r,a=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:d,damping:c,mass:u,duration:h,velocity:m,isResolvedFromDuration:p}=function(e){let t={velocity:tY.velocity,stiffness:tY.stiffness,damping:tY.damping,mass:tY.mass,isResolvedFromDuration:!1,...e};if(!tQ(e,tK)&&tQ(e,tX))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),r=i*i,s=2*eT(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:tY.mass,stiffness:r,damping:s}}else{let i=function({duration:e=tY.duration,bounce:t=tY.bounce,velocity:i=tY.velocity,mass:r=tY.mass}){let s,n;_(e<=F(tY.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=eT(tY.minDamping,tY.maxDamping,a),e=eT(tY.minDuration,tY.maxDuration,z(e)),a<1?(s=t=>{let r=t*a,s=r*e;return .001-(r-i)/tG(t,a)*Math.exp(-s)},n=t=>{let r=t*a*e,n=Math.pow(a,2)*Math.pow(t,2)*e,o=Math.exp(-r),l=tG(Math.pow(t,2),a);return(r*i+i-n)*o*(-s(t)+.001>0?-1:1)/l}):(s=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),n=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let r=i;for(let i=1;i<12;i++)r-=e(r)/t(r);return r}(s,n,5/e);if(e=F(e),isNaN(o))return{stiffness:tY.stiffness,damping:tY.damping,duration:e};{let t=Math.pow(o,2)*r;return{stiffness:t,damping:2*a*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...i,mass:tY.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-z(r.velocity||0)}),f=m||0,x=c/(2*Math.sqrt(d*u)),g=o-a,v=z(Math.sqrt(d/u)),b=5>Math.abs(g);if(s||(s=b?tY.restSpeed.granular:tY.restSpeed.default),n||(n=b?tY.restDelta.granular:tY.restDelta.default),x<1){let e=tG(v,x);i=t=>o-Math.exp(-x*v*t)*((f+x*v*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===x)i=e=>o-Math.exp(-v*e)*(g+(f+v*g)*e);else{let e=v*Math.sqrt(x*x-1);i=t=>{let i=Math.exp(-x*v*t),r=Math.min(e*t,300);return o-i*((f+x*v*g)*Math.sinh(r)+e*g*Math.cosh(r))/e}}let w={calculatedDuration:p&&h||null,next:e=>{let t=i(e);if(p)l.done=e>=h;else{let r=0;x<1&&(r=0===e?F(f):tZ(i,e,t));let a=Math.abs(o-t)<=n;l.done=Math.abs(r)<=s&&a}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(y(w),2e4),t=E(t=>w.next(e*t).value,e,30);return e+"ms "+t}};return w}function t0({keyframes:e,velocity:t=0,power:i=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:n=500,modifyTarget:a,min:o,max:l,restDelta:d=.5,restSpeed:c}){let u,h,m=e[0],p={done:!1,value:m},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,x=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,g=i*t,y=m+g,v=void 0===a?y:a(y);v!==y&&(g=v-m);let b=e=>-g*Math.exp(-e/r),w=e=>v+b(e),P=e=>{let t=b(e),i=w(e);p.done=Math.abs(t)<=d,p.value=p.done?v:i},j=e=>{f(p.value)&&(u=e,h=tJ({keyframes:[p.value,x(p.value)],velocity:tZ(w,e,p.value),damping:s,stiffness:n,restDelta:d,restSpeed:c}))};return j(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==u||(t=!0,P(e),j(e)),void 0!==u&&e>=u)?h.next(e-u):(t||P(e),p)}}}let t1=ef(.42,0,1,1),t2=ef(0,0,.58,1),t5=ef(.42,0,.58,1),t3=e=>Array.isArray(e)&&"number"!=typeof e[0],t4={linear:_,easeIn:t1,easeInOut:t5,easeOut:t2,circIn:eP,circInOut:eA,circOut:ej,backIn:ev,backInOut:eb,backOut:ey,anticipate:ew},t6=e=>{if(w(e)){_(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,r,s]=e;return ef(t,i,r,s)}return"string"==typeof e?(_(void 0!==t4[e],`Invalid easing type '${e}'`),t4[e]):e};function t9({duration:e=300,keyframes:t,times:i,ease:r="easeInOut"}){var s;let n=t3(r)?r.map(t6):t6(r),a={done:!1,value:t[0]},o=function(e,t,{clamp:i=!0,ease:r,mixer:s}={}){let n=e.length;if(_(n===t.length,"Both input and output ranges must be the same length"),1===n)return()=>t[0];if(2===n&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let r=[],s=i||tq,n=e.length-1;for(let i=0;i<n;i++){let n=s(e[i],e[i+1]);t&&(n=tF(Array.isArray(t)?t[i]||_:t,n)),r.push(n)}return r}(t,r,s),l=o.length,d=i=>{if(a&&i<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(i<e[r+1]);r++);let s=A(e[r],e[r+1],i);return o[r](s)};return i?t=>d(eT(e[0],e[n-1],t)):d}((s=i&&i.length===t.length?i:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let r=1;r<=t;r++){let s=A(0,t,r);e.push(tS(i,1,s))}}(t,e.length-1),t}(t),s.map(t=>t*e)),t,{ease:Array.isArray(n)?n:t.map(()=>n||t5).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=o(t),a.done=t>=e,a)}}let t8=e=>{let t=({timestamp:t})=>e(t);return{start:()=>X.update(t,!0),stop:()=>K(t),now:()=>Q.isProcessing?Q.timestamp:et.now()}},t7={decay:t0,inertia:t0,tween:t9,keyframes:t9,spring:tJ},ie=e=>e/100;class it extends tC{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:t,motionValue:i,element:r,keyframes:s}=this.options,n=(null==r?void 0:r.KeyframeResolver)||tp;this.resolver=new n(s,(e,t)=>this.onKeyframesResolved(e,t),t,i,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){let t,i,{type:r="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:a,velocity:o=0}=this.options,l=v(r)?r:t7[r]||t9;l!==t9&&"number"!=typeof e[0]&&(t=tF(ie,tq(e[0],e[1])),e=[0,100]);let d=l({...this.options,keyframes:e});"mirror"===a&&(i=l({...this.options,keyframes:[...e].reverse(),velocity:-o})),null===d.calculatedDuration&&(d.calculatedDuration=y(d));let{calculatedDuration:c}=d,u=c+n;return{generator:d,mirroredGenerator:i,mapPercentToKeyframes:t,calculatedDuration:c,resolvedDuration:u,totalDuration:u*(s+1)-n}}onPostResolved(){let{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:i}=this;if(!i){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:r,generator:s,mirroredGenerator:n,mapPercentToKeyframes:a,keyframes:o,calculatedDuration:l,totalDuration:d,resolvedDuration:c}=i;if(null===this.startTime)return s.next(0);let{delay:u,repeat:h,repeatType:m,repeatDelay:p,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-d/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let x=this.currentTime-u*(this.speed>=0?1:-1),g=this.speed>=0?x<0:x>d;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let y=this.currentTime,v=s;if(h){let e=Math.min(this.currentTime,d)/c,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,h+1))%2&&("reverse"===m?(i=1-i,p&&(i-=p/c)):"mirror"===m&&(v=n)),y=eT(0,1,i)*c}let b=g?{done:!1,value:o[0]}:v.next(y);a&&(b.value=a(b.value));let{done:w}=b;g||null===l||(w=this.speed>=0?this.currentTime>=d:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&void 0!==r&&(b.value=tN(o,this.options,r)),f&&f(b.value),P&&this.finish(),b}get duration(){let{resolved:e}=this;return e?z(e.calculatedDuration):0}get time(){return z(this.currentTime)}set time(e){e=F(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=z(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=t8,onPlay:t,startTime:i}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let r=this.driver.now();null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=r):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(e=this.currentTime)?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let ii=new Set(["opacity","clipPath","filter","transform"]),ir=m(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),is={anticipate:ew,backInOut:eb,circInOut:eA};class ia extends tC{constructor(e){super(e);let{name:t,motionValue:i,element:r,keyframes:s}=this.options;this.resolver=new tE(s,(e,t)=>this.onKeyframesResolved(e,t),t,i,r),this.resolver.scheduleResolve()}initPlayback(e,t){var i;let{duration:r=300,times:s,ease:n,type:a,motionValue:o,name:l,startTime:d}=this.options;if(!o.owner||!o.owner.current)return!1;if("string"==typeof n&&j()&&n in is&&(n=is[n]),v((i=this.options).type)||"spring"===i.type||!function e(t){return!!("function"==typeof t&&j()||!t||"string"==typeof t&&(t in k||j())||w(t)||Array.isArray(t)&&t.every(e))}(i.ease)){let{onComplete:t,onUpdate:i,motionValue:o,element:l,...d}=this.options,c=function(e,t){let i=new it({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),r={done:!1,value:e[0]},s=[],n=0;for(;!r.done&&n<2e4;)s.push((r=i.sample(n)).value),n+=10;return{times:void 0,keyframes:s,duration:n-10,ease:"linear"}}(e,d);1===(e=c.keyframes).length&&(e[1]=e[0]),r=c.duration,s=c.times,n=c.ease,a="keyframes"}let c=function(e,t,i,{delay:r=0,duration:s=300,repeat:n=0,repeatType:a="loop",ease:o="easeInOut",times:l}={}){let d={[t]:i};l&&(d.offset=l);let c=function e(t,i){if(t)return"function"==typeof t&&j()?E(t,i):w(t)?T(t):Array.isArray(t)?t.map(t=>e(t,i)||k.easeOut):k[t]}(o,s);return Array.isArray(c)&&(d.easing=c),e.animate(d,{delay:r,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:n+1,direction:"reverse"===a?"alternate":"normal"})}(o.owner.current,l,e,{...this.options,duration:r,times:s,ease:n});return c.startTime=null!=d?d:this.calcStartTime(),this.pendingTimeline?(b(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{let{onComplete:i}=this.options;o.set(tN(e,this.options,t)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:s,type:a,ease:n,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return z(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return z(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:i}=t;i.currentTime=F(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:i}=t;i.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}get startTime(){let{resolved:e}=this;if(!e)return null;let{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return _;let{animation:i}=t;b(i,e)}else this.pendingTimeline=e;return _}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:i,duration:r,type:s,ease:n,times:a}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:o,element:l,...d}=this.options,c=new it({...d,keyframes:i,duration:r,type:s,ease:n,times:a,isGenerator:!0}),u=F(this.time);e.setWithVelocity(c.sample(u-10).value,c.sample(u).value,10)}let{onStop:o}=this.options;o&&o(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:i,repeatDelay:r,repeatType:s,damping:n,type:a}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return ir()&&i&&ii.has(i)&&!o&&!l&&!r&&"mirror"!==s&&0!==n&&"inertia"!==a}}let io={type:"spring",stiffness:500,damping:25,restSpeed:10},il=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),id={type:"keyframes",duration:.8},ic={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},iu=(e,{keyframes:t})=>t.length>2?id:U.has(e)?e.startsWith("scale")?il(t[1]):io:ic,ih=(e,t,i,r={},s,n)=>a=>{let o=g(r,e)||{},l=o.delay||r.delay||0,{elapsed:d=0}=r;d-=F(l);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-d,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:n?void 0:s};!function({when:e,delay:t,delayChildren:i,staggerChildren:r,staggerDirection:s,repeat:n,repeatType:a,repeatDelay:o,from:l,elapsed:d,...c}){return!!Object.keys(c).length}(o)&&(c={...c,...iu(e,c)}),c.duration&&(c.duration=F(c.duration)),c.repeatDelay&&(c.repeatDelay=F(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let u=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(u=!0)),(em.current||Z.skipAnimations)&&(u=!0,c.duration=0,c.delay=0),u&&!n&&void 0!==t.get()){let e=tN(c.keyframes,o);if(void 0!==e)return X.update(()=>{c.onUpdate(e),c.onComplete()}),new x([])}return!n&&ia.supports(c)?new ia(c):new it(c)};function im(e,t,{delay:i=0,transitionOverride:r,type:s}={}){var n;let{transition:a=e.getDefaultTransition(),transitionEnd:o,...l}=t;r&&(a=r);let d=[],u=s&&e.animationState&&e.animationState.getState()[s];for(let t in l){let r=e.getValue(t,null!=(n=e.latestValues[t])?n:null),s=l[t];if(void 0===s||u&&function({protectedKeys:e,needsAnimating:t},i){let r=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,r}(u,t))continue;let o={delay:i,...g(a||{},t)},c=!1;if(window.MotionHandoffAnimation){let i=e.props[eh];if(i){let e=window.MotionHandoffAnimation(i,t,X);null!==e&&(o.startTime=e,c=!0)}}ec(e,t),r.start(ih(t,r,s,e.shouldReduceMotion&&$.has(t)?{type:!1}:o,e,c));let h=r.animation;h&&d.push(h)}return o&&Promise.all(d).then(()=>{X.update(()=>{o&&function(e,t){let{transitionEnd:i={},transition:r={},...s}=c(e,t)||{};for(let t in s={...s,...i}){let i=q(s[t]);e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,el(i))}}(e,o)})}),d}function ip(e,t,i={}){var r;let s=c(e,t,"exit"===i.type?null==(r=e.presenceContext)?void 0:r.custom:void 0),{transition:n=e.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let a=s?()=>Promise.all(im(e,s,i)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=n;return function(e,t,i=0,r=0,s=1,n){let a=[],o=(e.variantChildren.size-1)*r,l=1===s?(e=0)=>e*r:(e=0)=>o-e*r;return Array.from(e.variantChildren).sort(ix).forEach((e,r)=>{e.notify("AnimationStart",t),a.push(ip(e,t,{...n,delay:i+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,s+r,a,o,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([a(),o(i.delay)]);{let[e,t]="beforeChildren"===l?[a,o]:[o,a];return e().then(()=>t())}}function ix(e,t){return e.sortNodePosition(t)}let ig=h.length,iy=[...u].reverse(),iv=u.length;function ib(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iw(){return{animate:ib(!0),whileInView:ib(),whileHover:ib(),whileTap:ib(),whileDrag:ib(),whileFocus:ib(),exit:ib()}}class iP{constructor(e){this.isMounted=!1,this.node=e}update(){}}class ij extends iP{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>ip(e,t,i)));else if("string"==typeof t)r=ip(e,t,i);else{let s="function"==typeof t?c(e,t,i.custom):t;r=Promise.all(im(e,s,i))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=iw(),r=!0,l=t=>(i,r)=>{var s;let n=c(e,r,"exit"===t?null==(s=e.presenceContext)?void 0:s.custom:void 0);if(n){let{transition:e,transitionEnd:t,...r}=n;i={...i,...r,...t}}return i};function d(d){let{props:c}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<ig;e++){let r=h[e],s=t.props[r];(o(s)||!1===s)&&(i[r]=s)}return i}(e.parent)||{},m=[],p=new Set,f={},x=1/0;for(let t=0;t<iv;t++){var g,y;let h=iy[t],v=i[h],b=void 0!==c[h]?c[h]:u[h],w=o(b),P=h===d?v.isActive:null;!1===P&&(x=t);let j=b===u[h]&&b!==c[h]&&w;if(j&&r&&e.manuallyAnimateOnMount&&(j=!1),v.protectedKeys={...f},!v.isActive&&null===P||!b&&!v.prevProp||s(b)||"boolean"==typeof b)continue;let A=(g=v.prevProp,"string"==typeof(y=b)?y!==g:!!Array.isArray(y)&&!a(y,g)),E=A||h===d&&v.isActive&&!j&&w||t>x&&w,T=!1,k=Array.isArray(b)?b:[b],N=k.reduce(l(h),{});!1===P&&(N={});let{prevResolvedValues:C={}}=v,S={...C,...N},V=t=>{E=!0,p.has(t)&&(T=!0,p.delete(t)),v.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in S){let t=N[e],i=C[e];if(f.hasOwnProperty(e))continue;let r=!1;(n(t)&&n(i)?a(t,i):t===i)?void 0!==t&&p.has(e)?V(e):v.protectedKeys[e]=!0:null!=t?V(e):p.add(e)}v.prevProp=b,v.prevResolvedValues=N,v.isActive&&(f={...f,...N}),r&&e.blockInitialAnimation&&(E=!1);let M=!(j&&A)||T;E&&M&&m.push(...k.map(e=>({animation:e,options:{type:h}})))}if(p.size){let t={};p.forEach(i=>{let r=e.getBaseTarget(i),s=e.getValue(i);s&&(s.liveStyle=!0),t[i]=null!=r?r:null}),m.push({animation:t})}let v=!!m.length;return r&&(!1===c.initial||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(v=!1),r=!1,v?t(m):Promise.resolve()}return{animateChanges:d,setActive:function(t,r){var s;if(i[t].isActive===r)return Promise.resolve();null==(s=e.variantChildren)||s.forEach(e=>{var i;return null==(i=e.animationState)?void 0:i.setActive(t,r)}),i[t].isActive=r;let n=d(t);for(let e in i)i[e].protectedKeys={};return n},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=iw(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();s(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null==(e=this.unmountControls)||e.call(this)}}let iA=0;class iE extends iP{constructor(){super(...arguments),this.id=iA++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}function iT(e,t,i,r={passive:!0}){return e.addEventListener(t,i,r),()=>e.removeEventListener(t,i)}function ik(e){return{point:{x:e.pageX,y:e.pageY}}}let iN=e=>t=>M(t)&&e(t,ik(t));function iC(e,t,i,r){return iT(e,t,iN(i),r)}let iS=(e,t)=>Math.abs(e-t);class iV{constructor(e,t,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=iD(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(iS(e.x,t.x)**2+iS(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!i)return;let{point:r}=e,{timestamp:s}=Q;this.history.push({...r,timestamp:s});let{onStart:n,onMove:a}=this.handlers;t||(n&&n(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=iM(t,this.transformPagePoint),X.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=iD("pointercancel"===e.type?this.lastMoveEventInfo:iM(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,n),r&&r(e,n)},!M(e))return;this.dragSnapToOrigin=s,this.handlers=t,this.transformPagePoint=i,this.contextWindow=r||window;let n=iM(ik(e),this.transformPagePoint),{point:a}=n,{timestamp:o}=Q;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,iD(n,this.history)),this.removeListeners=tF(iC(this.contextWindow,"pointermove",this.handlePointerMove),iC(this.contextWindow,"pointerup",this.handlePointerUp),iC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),K(this.updatePoint)}}function iM(e,t){return t?{point:t(e.point)}:e}function iR(e,t){return{x:e.x-t.x,y:e.y-t.y}}function iD({point:e},t){return{point:e,delta:iR(e,iI(t)),offset:iR(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,r=null,s=iI(e);for(;i>=0&&(r=e[i],!(s.timestamp-r.timestamp>F(.1)));)i--;if(!r)return{x:0,y:0};let n=z(s.timestamp-r.timestamp);if(0===n)return{x:0,y:0};let a={x:(s.x-r.x)/n,y:(s.y-r.y)/n};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function iI(e){return e[e.length-1]}function iL(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function iO(e){return e.max-e.min}function iB(e,t,i,r=.5){e.origin=r,e.originPoint=tS(t.min,t.max,e.origin),e.scale=iO(i)/iO(t),e.translate=tS(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function iF(e,t,i,r){iB(e.x,t.x,i.x,r?r.originX:void 0),iB(e.y,t.y,i.y,r?r.originY:void 0)}function iz(e,t,i){e.min=i.min+t.min,e.max=e.min+iO(t)}function i_(e,t,i){e.min=t.min-i.min,e.max=e.min+iO(t)}function iW(e,t,i){i_(e.x,t.x,i.x),i_(e.y,t.y,i.y)}function iU(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function i$(e,t){let i=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,r]=[r,i]),{min:i,max:r}}function iH(e,t,i){return{min:iq(e,t),max:iq(e,i)}}function iq(e,t){return"number"==typeof e?e:e[t]||0}let iZ=()=>({translate:0,scale:1,origin:0,originPoint:0}),iY=()=>({x:iZ(),y:iZ()}),iG=()=>({min:0,max:0}),iX=()=>({x:iG(),y:iG()});function iK(e){return[e("x"),e("y")]}function iQ({top:e,left:t,right:i,bottom:r}){return{x:{min:t,max:i},y:{min:e,max:r}}}function iJ(e){return void 0===e||1===e}function i0({scale:e,scaleX:t,scaleY:i}){return!iJ(e)||!iJ(t)||!iJ(i)}function i1(e){return i0(e)||i2(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function i2(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function i5(e,t,i,r,s){return void 0!==s&&(e=r+s*(e-r)),r+i*(e-r)+t}function i3(e,t=0,i=1,r,s){e.min=i5(e.min,t,i,r,s),e.max=i5(e.max,t,i,r,s)}function i4(e,{x:t,y:i}){i3(e.x,t.translate,t.scale,t.originPoint),i3(e.y,i.translate,i.scale,i.originPoint)}function i6(e,t){e.min=e.min+t,e.max=e.max+t}function i9(e,t,i,r,s=.5){let n=tS(e.min,e.max,s);i3(e,t,i,n,r)}function i8(e,t){i9(e.x,t.x,t.scaleX,t.scale,t.originX),i9(e.y,t.y,t.scaleY,t.scale,t.originY)}function i7(e,t){return iQ(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let re=({current:e})=>e?e.ownerDocument.defaultView:null,rt=new WeakMap;class ri{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iX(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iV(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ik(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:r,onDragStart:s}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(N[e])return null;else return N[e]=!0,()=>{N[e]=!1};return N.x||N.y?null:(N.x=N.y=!0,()=>{N.x=N.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iK(e=>{let t=this.getAxisMotionValue(e).get()||0;if(e_.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[e];r&&(t=iO(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),s&&X.postRender(()=>s(e,t)),ec(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:s,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),n&&n(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>iK(e=>{var t;return"paused"===this.getAnimationState(e)&&(null==(t=this.getAxisMotionValue(e).animation)?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:re(this.visualElement)})}stop(e,t){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:s}=this.getProps();s&&X.postRender(()=>s(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:r}=this.getProps();if(!i||!rr(e,r,this.currentDirection))return;let s=this.getAxisMotionValue(e),n=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(n=function(e,{min:t,max:i},r){return void 0!==t&&e<t?e=r?tS(t,e,r.min):Math.max(e,t):void 0!==i&&e>i&&(e=r?tS(i,e,r.max):Math.min(e,i)),e}(n,this.constraints[e],this.elastic[e])),s.set(n)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(e=this.visualElement.projection)?void 0:e.layout,s=this.constraints;t&&iL(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,{top:t,left:i,bottom:r,right:s}){return{x:iU(e.x,i,s),y:iU(e.y,t,r)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:iH(e,"left","right"),y:iH(e,"top","bottom")}}(i),s!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&iK(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!iL(t))return!1;let r=t.current;_(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let n=function(e,t,i){let r=i7(e,i),{scroll:s}=t;return s&&(i6(r.x,s.offset.x),i6(r.y,s.offset.y)),r}(r,s.root,this.visualElement.getTransformPagePoint()),a=(e=s.layout.layoutBox,{x:i$(e.x,n.x),y:i$(e.y,n.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=iQ(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:r,dragTransition:s,dragSnapToOrigin:n,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iK(a=>{if(!rr(a,t,this.currentDirection))return;let l=o&&o[a]||{};n&&(l={min:0,max:0});let d={type:"inertia",velocity:i?e[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(a,d)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return ec(this.visualElement,e),i.start(ih(e,i,0,t,this.visualElement,!1))}stopAnimation(){iK(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){iK(e=>{var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.pause()})}getAnimationState(e){var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){iK(t=>{let{drag:i}=this.getProps();if(!rr(t,i,this.currentDirection))return;let{projection:r}=this.visualElement,s=this.getAxisMotionValue(t);if(r&&r.layout){let{min:i,max:n}=r.layout.layoutBox[t];s.set(e[t]-tS(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!iL(t)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};iK(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();r[e]=function(e,t){let i=.5,r=iO(e),s=iO(t);return s>r?i=A(t.min,t.max-r,e.min):r>s&&(i=A(e.min,e.max-s,t.min)),eT(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iK(t=>{if(!rr(t,e,null))return;let i=this.getAxisMotionValue(t),{min:s,max:n}=this.constraints[t];i.set(tS(s,n,r[t]))})}addListeners(){if(!this.visualElement.current)return;rt.set(this.visualElement,this);let e=iC(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();iL(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),X.read(t);let s=iT(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(iK(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{s(),e(),r(),n&&n()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:s=!1,dragElastic:n=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:r,dragConstraints:s,dragElastic:n,dragMomentum:a}}}function rr(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class rs extends iP{constructor(e){super(e),this.removeGroupControls=_,this.removeListeners=_,this.controls=new ri(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||_}unmount(){this.removeGroupControls(),this.removeListeners()}}let rn=e=>(t,i)=>{e&&X.postRender(()=>e(t,i))};class ra extends iP{constructor(){super(...arguments),this.removePointerDownListener=_}onPointerDown(e){this.session=new iV(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:re(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rn(e),onStart:rn(t),onMove:i,onEnd:(e,t)=>{delete this.session,r&&X.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=iC(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ro,rl,rd=i(687),rc=i(3210);let ru=(0,rc.createContext)(null),rh=(0,rc.createContext)({}),rm=(0,rc.createContext)({}),rp={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let rx={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eW.test(e))return e;else e=parseFloat(e);let i=rf(e,t.target.x),r=rf(e,t.target.y);return`${i}% ${r}%`}},rg={},{schedule:ry,cancel:rv}=G(queueMicrotask,!1);class rb extends rc.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:r}=this.props,{projection:s}=e;Object.assign(rg,rP),s&&(t.group&&t.group.add(s),i&&i.register&&r&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),rp.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:r,isPresent:s}=this.props,n=i.projection;return n&&(n.isPresent=s,r||e.layoutDependency!==t||void 0===t?n.willUpdate():this.safeToRemove(),e.isPresent!==s&&(s?n.promote():n.relegate()||X.postRender(()=>{let e=n.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),ry.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rw(e){let[t,i]=function(e=!0){let t=(0,rc.useContext)(ru);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:r,register:s}=t,n=(0,rc.useId)();(0,rc.useEffect)(()=>{e&&s(n)},[e]);let a=(0,rc.useCallback)(()=>e&&r&&r(n),[n,r,e]);return!i&&r?[!1,a]:[!0]}(),r=(0,rc.useContext)(rh);return(0,rd.jsx)(rb,{...e,layoutGroup:r,switchLayoutGroup:(0,rc.useContext)(rm),isPresent:t,safeToRemove:i})}let rP={borderRadius:{...rx,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rx,borderTopRightRadius:rx,borderBottomLeftRadius:rx,borderBottomRightRadius:rx,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let r=e2.parse(e);if(r.length>5)return e;let s=e2.createTransformer(e),n=+("number"!=typeof r[0]),a=i.x.scale*t.x,o=i.y.scale*t.y;r[0+n]/=a,r[1+n]/=o;let l=tS(a,o,.5);return"number"==typeof r[2+n]&&(r[2+n]/=l),"number"==typeof r[3+n]&&(r[3+n]/=l),s(r)}}},rj=(e,t)=>e.depth-t.depth;class rA{constructor(){this.children=[],this.isDirty=!1}add(e){ei(this.children,e),this.isDirty=!0}remove(e){er(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rj),this.isDirty=!1,this.children.forEach(e)}}function rE(e){let t=ed(e)?e.get():e;return H(t)?t.toValue():t}let rT=["TopLeft","TopRight","BottomLeft","BottomRight"],rk=rT.length,rN=e=>"string"==typeof e?parseFloat(e):e,rC=e=>"number"==typeof e||eW.test(e);function rS(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rV=rR(0,.5,ej),rM=rR(.5,.95,_);function rR(e,t,i){return r=>r<e?0:r>t?1:i(A(e,t,r))}function rD(e,t){e.min=t.min,e.max=t.max}function rI(e,t){rD(e.x,t.x),rD(e.y,t.y)}function rL(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rO(e,t,i,r,s){return e-=t,e=r+1/i*(e-r),void 0!==s&&(e=r+1/s*(e-r)),e}function rB(e,t,[i,r,s],n,a){!function(e,t=0,i=1,r=.5,s,n=e,a=e){if(e_.test(t)&&(t=parseFloat(t),t=tS(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=tS(n.min,n.max,r);e===n&&(o-=t),e.min=rO(e.min,t,i,o,s),e.max=rO(e.max,t,i,o,s)}(e,t[i],t[r],t[s],t.scale,n,a)}let rF=["x","scaleX","originX"],rz=["y","scaleY","originY"];function r_(e,t,i,r){rB(e.x,t,rF,i?i.x:void 0,r?r.x:void 0),rB(e.y,t,rz,i?i.y:void 0,r?r.y:void 0)}function rW(e){return 0===e.translate&&1===e.scale}function rU(e){return rW(e.x)&&rW(e.y)}function r$(e,t){return e.min===t.min&&e.max===t.max}function rH(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rq(e,t){return rH(e.x,t.x)&&rH(e.y,t.y)}function rZ(e){return iO(e.x)/iO(e.y)}function rY(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rG{constructor(){this.members=[]}add(e){ei(this.members,e),e.scheduleRender()}remove(e){if(er(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rX={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},rK="undefined"!=typeof window&&void 0!==window.MotionDebug,rQ=["","X","Y","Z"],rJ={visibility:"hidden"},r0=0;function r1(e,t,i,r){let{latestValues:s}=t;s[e]&&(i[e]=s[e],t.setStaticValue(e,0),r&&(r[e]=0))}function r2({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(e={},i=null==t?void 0:t()){this.id=r0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rK&&(rX.totalNodes=rX.resolvedTargetDeltas=rX.recalculatedProjection=0),this.nodes.forEach(r4),this.nodes.forEach(si),this.nodes.forEach(sr),this.nodes.forEach(r6),rK&&window.MotionDebug.record(rX)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rA)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new es),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:r,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(s||r)&&(this.isLayoutDirty=!0),e){let i,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=et.now(),r=({timestamp:s})=>{let n=s-i;n>=250&&(K(r),e(n-t))};return X.read(r,!0),()=>K(r)}(r,250),rp.hasAnimatedSinceResize&&(rp.hasAnimatedSinceResize=!1,this.nodes.forEach(st))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&n&&(r||s)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||sd,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!rq(this.targetLayout,r)||i,d=!t&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||d||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,d);let t={...g(s,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||st(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,K(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ss),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let r=i.props[eh];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",X,!(e||i))}let{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&e(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r8);return}this.isUpdating||this.nodes.forEach(r7),this.isUpdating=!1,this.nodes.forEach(se),this.nodes.forEach(r5),this.nodes.forEach(r3),this.clearAllSnapshots();let e=et.now();Q.delta=eT(0,1e3/60,e-Q.timestamp),Q.timestamp=e,Q.isProcessing=!0,J.update.process(Q),J.preRender.process(Q),J.render.process(Q),Q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ry.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(r9),this.sharedNodes.forEach(sn)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,X.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){X.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iX(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!s)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rU(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,n=r!==this.prevTransformTemplateValue;e&&(t||i1(this.latestValues)||n)&&(s(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),r=this.removeElementScroll(i);return e&&(r=this.removeTransform(r)),sh((t=r).x),sh(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return iX();let i=t.measureViewportBox();if(!((null==(e=this.scroll)?void 0:e.wasRoot)||this.path.some(sp))){let{scroll:e}=this.root;e&&(i6(i.x,e.offset.x),i6(i.y,e.offset.y))}return i}removeElementScroll(e){var t;let i=iX();if(rI(i,e),null==(t=this.scroll)?void 0:t.wasRoot)return i;for(let t=0;t<this.path.length;t++){let r=this.path[t],{scroll:s,options:n}=r;r!==this.root&&s&&n.layoutScroll&&(s.wasRoot&&rI(i,e),i6(i.x,s.offset.x),i6(i.y,s.offset.y))}return i}applyTransform(e,t=!1){let i=iX();rI(i,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i8(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),i1(r.latestValues)&&i8(i,r.latestValues)}return i1(this.latestValues)&&i8(i,this.latestValues),i}removeTransform(e){let t=iX();rI(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!i1(i.latestValues))continue;i0(i.latestValues)&&i.updateSnapshot();let r=iX();rI(r,i.measurePageBox()),r_(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return i1(this.latestValues)&&r_(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,i,r,s;let n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==n;if(!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:o,layoutId:l}=this.options;if(this.layout&&(o||l)){if(this.resolvedRelativeTargetAt=Q.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iX(),this.relativeTargetOrigin=iX(),iW(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iX(),this.targetWithTransforms=iX()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,r=this.relativeTarget,s=this.relativeParent.target,iz(i.x,r.x,s.x),iz(i.y,r.y,s.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rI(this.target,this.layout.layoutBox),i4(this.target,this.targetDelta)):rI(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iX(),this.relativeTargetOrigin=iX(),iW(this.relativeTargetOrigin,this.target,e.target),rI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rK&&rX.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||i0(this.parent.latestValues)||i2(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),i=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===Q.timestamp&&(r=!1),r)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;rI(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;!function(e,t,i,r=!1){let s,n,a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){n=(s=i[o]).projectionDelta;let{visualElement:a}=s.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&i8(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,i4(e,n)),r&&i1(s.latestValues)&&i8(e,s.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iX());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rL(this.prevProjectionDelta.x,this.projectionDelta.x),rL(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iF(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===a&&this.treeScale.y===o&&rY(this.projectionDelta.x,this.prevProjectionDelta.x)&&rY(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),rK&&rX.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null==(t=this.options.visualElement)||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iY(),this.projectionDelta=iY(),this.projectionDeltaWithTransform=iY()}setAnimationOrigin(e,t=!1){let i,r=this.snapshot,s=r?r.latestValues:{},n={...this.latestValues},a=iY();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=iX(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),d=this.getStack(),c=!d||d.members.length<=1,u=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(sl));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(sa(a.x,e.x,r),sa(a.y,e.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var d,h,m,p,f,x;iW(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,f=o,x=r,so(m.x,p.x,f.x,x),so(m.y,p.y,f.y,x),i&&(d=this.relativeTarget,h=i,r$(d.x,h.x)&&r$(d.y,h.y))&&(this.isProjectionDirty=!1),i||(i=iX()),rI(i,this.relativeTarget)}l&&(this.animationValues=n,function(e,t,i,r,s,n){s?(e.opacity=tS(0,void 0!==i.opacity?i.opacity:1,rV(r)),e.opacityExit=tS(void 0!==t.opacity?t.opacity:1,0,rM(r))):n&&(e.opacity=tS(void 0!==t.opacity?t.opacity:1,void 0!==i.opacity?i.opacity:1,r));for(let s=0;s<rk;s++){let n=`border${rT[s]}Radius`,a=rS(t,n),o=rS(i,n);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||rC(a)===rC(o)?(e[n]=Math.max(tS(rN(a),rN(o),r),0),(e_.test(o)||e_.test(a))&&(e[n]+="%")):e[n]=o)}(t.rotate||i.rotate)&&(e.rotate=tS(t.rotate||0,i.rotate||0,r))}(n,s,this.latestValues,r,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(K(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=X.update(()=>{rp.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,i){let r=ed(0)?0:el(e);return r.start(ih("",r,1e3,i)),r.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:r,latestValues:s}=e;if(t&&i&&r){if(this!==e&&this.layout&&r&&sm(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||iX();let t=iO(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let r=iO(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+r}rI(t,i),i8(t,s),iF(this.projectionDeltaWithTransform,this.layoutCorrected,t,s)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rG),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null==(e=this.getStack())?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null==(e=this.getStack())?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let r={};i.z&&r1("z",e,r,this.animationValues);for(let t=0;t<rQ.length;t++)r1(`rotate${rQ[t]}`,e,r,this.animationValues),r1(`skew${rQ[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){var t,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return rJ;let r={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=rE(null==e?void 0:e.pointerEvents)||"",r.transform=s?s(this.latestValues,""):"none",r;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rE(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!i1(this.latestValues)&&(t.transform=s?s({},""):"none",this.hasProjected=!1),t}let a=n.animationValues||n.latestValues;this.applyTransformsToTarget(),r.transform=function(e,t,i){let r="",s=e.x.translate/t.x,n=e.y.translate/t.y,a=(null==i?void 0:i.z)||0;if((s||n||a)&&(r=`translate3d(${s}px, ${n}px, ${a}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:s,rotateY:n,skewX:a,skewY:o}=i;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),s&&(r+=`rotateX(${s}deg) `),n&&(r+=`rotateY(${n}deg) `),a&&(r+=`skewX(${a}deg) `),o&&(r+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(r+=`scale(${o}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,a),s&&(r.transform=s(a,r.transform));let{x:o,y:l}=this.projectionDelta;for(let e in r.transformOrigin=`${100*o.origin}% ${100*l.origin}% 0`,n.animationValues?r.opacity=n===this?null!=(i=null!=(t=a.opacity)?t:this.latestValues.opacity)?i:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:r.opacity=n===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,rg){if(void 0===a[e])continue;let{correct:t,applyTo:i}=rg[e],s="none"===r.transform?a[e]:t(a[e],n);if(i){let e=i.length;for(let t=0;t<e;t++)r[i[t]]=s}else r[e]=s}return this.options.layoutId&&(r.pointerEvents=n===this?rE(null==e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null==(t=e.currentAnimation)?void 0:t.stop()}),this.root.nodes.forEach(r8),this.root.sharedNodes.clear()}}}function r5(e){e.updateLayout()}function r3(e){var t;let i=(null==(t=e.resumeFrom)?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:s}=e.options,n=i.source!==e.layout.source;"size"===s?iK(e=>{let r=n?i.measuredBox[e]:i.layoutBox[e],s=iO(r);r.min=t[e].min,r.max=r.min+s}):sm(s,i.layoutBox,t)&&iK(r=>{let s=n?i.measuredBox[r]:i.layoutBox[r],a=iO(t[r]);s.max=s.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});let a=iY();iF(a,t,i.layoutBox);let o=iY();n?iF(o,e.applyTransform(r,!0),i.measuredBox):iF(o,t,i.layoutBox);let l=!rU(a),d=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:s,layout:n}=r;if(s&&n){let a=iX();iW(a,i.layoutBox,s.layoutBox);let o=iX();iW(o,t,n.layoutBox),rq(a,o)||(d=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:i,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:d})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function r4(e){rK&&rX.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function r6(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function r9(e){e.clearSnapshot()}function r8(e){e.clearMeasurements()}function r7(e){e.isLayoutDirty=!1}function se(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function st(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function si(e){e.resolveTargetDelta()}function sr(e){e.calcProjection()}function ss(e){e.resetSkewAndRotation()}function sn(e){e.removeLeadSnapshot()}function sa(e,t,i){e.translate=tS(t.translate,0,i),e.scale=tS(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function so(e,t,i,r){e.min=tS(t.min,i.min,r),e.max=tS(t.max,i.max,r)}function sl(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let sd={duration:.45,ease:[.4,0,.1,1]},sc=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),su=sc("applewebkit/")&&!sc("chrome/")?Math.round:_;function sh(e){e.min=su(e.min),e.max=su(e.max)}function sm(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rZ(t)-rZ(i)))}function sp(e){var t;return e!==e.root&&(null==(t=e.scroll)?void 0:t.wasRoot)}let sf=r2({attachResizeListener:(e,t)=>iT(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sx={current:void 0},sg=r2({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!sx.current){let e=new sf({});e.mount(window),e.setOptions({layoutScroll:!0}),sx.current=e}return sx.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function sy(e,t,i){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===i);let s=r["onHover"+i];s&&X.postRender(()=>s(t,ik(t)))}class sv extends iP{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[r,s,n]=C(e,i),a=S(e=>{let{target:i}=e,r=t(e);if("function"!=typeof r||!i)return;let n=S(e=>{r(e),i.removeEventListener("pointerleave",n)});i.addEventListener("pointerleave",n,s)});return r.forEach(e=>{e.addEventListener("pointerenter",a,s)}),n}(e,e=>(sy(this.node,e,"Start"),e=>sy(this.node,e,"End"))))}unmount(){}}class sb extends iP{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tF(iT(this.node.current,"focus",()=>this.onFocus()),iT(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function sw(e,t,i){let{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===i);let s=r["onTap"+("End"===i?"":i)];s&&X.postRender(()=>s(t,ik(t)))}class sP extends iP{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[r,s,n]=C(e,i),a=e=>{let r=e.currentTarget;if(!B(e)||D.has(r))return;D.add(r);let n=t(e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),B(e)&&D.has(r)&&(D.delete(r),"function"==typeof n&&n(e,{success:t}))},o=e=>{a(e,i.useGlobalTarget||V(r,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return r.forEach(e=>{R.has(e.tagName)||-1!==e.tabIndex||null!==e.getAttribute("tabindex")||(e.tabIndex=0),(i.useGlobalTarget?window:e).addEventListener("pointerdown",a,s),e.addEventListener("focus",e=>O(e,s),s)}),n}(e,e=>(sw(this.node,e,"Start"),(e,{success:t})=>sw(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sj=new WeakMap,sA=new WeakMap,sE=e=>{let t=sj.get(e.target);t&&t(e)},sT=e=>{e.forEach(sE)},sk={some:0,all:1};class sN extends iP{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:r="some",once:s}=e,n={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:sk[r]};return function(e,t,i){let r=function({root:e,...t}){let i=e||document;sA.has(i)||sA.set(i,{});let r=sA.get(i),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(sT,{root:e,...t})),r[s]}(t);return sj.set(e,i),r.observe(e),()=>{sj.delete(e),r.unobserve(e)}}(this.node.current,n,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,s&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),n=t?i:r;n&&n(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let sC=(0,rc.createContext)({strict:!1}),sS=(0,rc.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),sV=(0,rc.createContext)({});function sM(e){return s(e.animate)||h.some(t=>o(e[t]))}function sR(e){return!!(sM(e)||e.variants)}function sD(e){return Array.isArray(e)?e.join(" "):e}let sI="undefined"!=typeof window,sL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sO={};for(let e in sL)sO[e]={isEnabled:t=>sL[e].some(e=>!!t[e])};let sB=Symbol.for("motionComponentSymbol"),sF=sI?rc.useLayoutEffect:rc.useEffect,sz=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function s_(e){if("string"!=typeof e||e.includes("-"));else if(sz.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let sW=e=>(t,i)=>{let r=(0,rc.useContext)(sV),n=(0,rc.useContext)(ru),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:i},r,n,a){let o={latestValues:function(e,t,i,r){let n={},a=r(e,{});for(let e in a)n[e]=rE(a[e]);let{initial:o,animate:l}=e,c=sM(e),u=sR(e);t&&u&&!c&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===l&&(l=t.animate));let h=!!i&&!1===i.initial,m=(h=h||!1===o)?l:o;if(m&&"boolean"!=typeof m&&!s(m)){let t=Array.isArray(m)?m:[m];for(let i=0;i<t.length;i++){let r=d(e,t[i]);if(r){let{transitionEnd:e,transition:t,...i}=r;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(r,n,a,e),renderState:t()};return i&&(o.onMount=e=>i({props:r,current:e,...o}),o.onUpdate=e=>i(e)),o})(e,t,r,n);return i?a():function(e){let t=(0,rc.useRef)(null);return null===t.current&&(t.current=e()),t.current}(a)},sU=(e,t)=>t&&"number"==typeof e?t.transform(e):e,s$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sH=W.length;function sq(e,t,i){let{style:r,vars:s,transformOrigin:n}=e,a=!1,o=!1;for(let e in t){let i=t[e];if(U.has(e)){a=!0;continue}if(tg(e)){s[e]=i;continue}{let t=sU(i,e8[e]);e.startsWith("origin")?(o=!0,n[e]=t):r[e]=t}}if(!t.transform&&(a||i?r.transform=function(e,t,i){let r="",s=!0;for(let n=0;n<sH;n++){let a=W[n],o=e[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let e=sU(o,e8[a]);if(!l){s=!1;let t=s$[a]||a;r+=`${t}(${e}) `}i&&(t[a]=e)}}return r=r.trim(),i?r=i(t,s?"":r):s&&(r="none"),r}(t,e.transform,i):r.transform&&(r.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=n;r.transformOrigin=`${e} ${t} ${i}`}}let sZ={offset:"stroke-dashoffset",array:"stroke-dasharray"},sY={offset:"strokeDashoffset",array:"strokeDasharray"};function sG(e,t,i){return"string"==typeof e?e:eW.transform(t+i*e)}function sX(e,{attrX:t,attrY:i,attrScale:r,originX:s,originY:n,pathLength:a,pathSpacing:o=1,pathOffset:l=0,...d},c,u){if(sq(e,d,u),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:m,dimensions:p}=e;h.transform&&(p&&(m.transform=h.transform),delete h.transform),p&&(void 0!==s||void 0!==n||m.transform)&&(m.transformOrigin=function(e,t,i){let r=sG(t,e.x,e.width),s=sG(i,e.y,e.height);return`${r} ${s}`}(p,void 0!==s?s:.5,void 0!==n?n:.5)),void 0!==t&&(h.x=t),void 0!==i&&(h.y=i),void 0!==r&&(h.scale=r),void 0!==a&&function(e,t,i=1,r=0,s=!0){e.pathLength=1;let n=s?sZ:sY;e[n.offset]=eW.transform(-r);let a=eW.transform(t),o=eW.transform(i);e[n.array]=`${a} ${o}`}(h,a,o,l,!1)}let sK=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),sQ=()=>({...sK(),attrs:{}}),sJ=e=>"string"==typeof e&&"svg"===e.toLowerCase();function s0(e,{style:t,vars:i},r,s){for(let n in Object.assign(e.style,t,s&&s.getProjectionStyles(r)),i)e.style.setProperty(n,i[n])}let s1=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function s2(e,t,i,r){for(let i in s0(e,t,void 0,r),t.attrs)e.setAttribute(s1.has(i)?i:eu(i),t.attrs[i])}function s5(e,{layout:t,layoutId:i}){return U.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!rg[e]||"opacity"===e)}function s3(e,t,i){var r;let{style:s}=e,n={};for(let a in s)(ed(s[a])||t.style&&ed(t.style[a])||s5(a,e)||(null==(r=null==i?void 0:i.getValue(a))?void 0:r.liveStyle)!==void 0)&&(n[a]=s[a]);return n}function s4(e,t,i){let r=s3(e,t,i);for(let i in e)(ed(e[i])||ed(t[i]))&&(r[-1!==W.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return r}let s6=["x","y","width","height","cx","cy","r"],s9={useVisualState:sW({scrapeMotionValuesFromProps:s4,createRenderState:sQ,onUpdate:({props:e,prevProps:t,current:i,renderState:r,latestValues:s})=>{if(!i)return;let n=!!e.drag;if(!n){for(let e in s)if(U.has(e)){n=!0;break}}if(!n)return;let a=!t;if(t)for(let i=0;i<s6.length;i++){let r=s6[i];e[r]!==t[r]&&(a=!0)}a&&X.read(()=>{!function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}}(i,r),X.render(()=>{sX(r,s,sJ(i.tagName),e.transformTemplate),s2(i,r)})})}})},s8={useVisualState:sW({scrapeMotionValuesFromProps:s3,createRenderState:sK})};function s7(e,t,i){for(let r in t)ed(t[r])||s5(r,i)||(e[r]=t[r])}let ne=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nt(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||ne.has(e)}let ni=e=>!nt(e);try{!function(e){e&&(ni=t=>t.startsWith("on")?!nt(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}let nr={current:null},ns={current:!1},nn=[...tj,eZ,e2],na=e=>nn.find(tP(e)),no=new WeakMap,nl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nd{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:s,visualState:n},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tp,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=et.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,X.render(this.render,!1,!0))};let{latestValues:o,renderState:l,onUpdate:d}=n;this.onUpdate=d,this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=sM(t),this.isVariantNode=sR(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...u}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in u){let t=u[e];void 0!==o[e]&&ed(t)&&t.set(o[e],!1)}}mount(e){this.current=e,no.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ns.current||function(){if(ns.current=!0,sI)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>nr.current=e.matches;e.addListener(t),t()}else nr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in no.delete(this.current),this.projection&&this.projection.unmount(),K(this.notifyUpdate),K(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=U.has(e),s=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&X.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{s(),n(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in sO){let t=sO[e];if(!t)continue;let{isEnabled:i,Feature:r}=t;if(!this.features[e]&&r&&i(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iX()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<nl.length;t++){let i=nl[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(e,t,i){for(let r in t){let s=t[r],n=i[r];if(ed(s))e.addValue(r,s);else if(ed(n))e.addValue(r,el(s,{owner:e}));else if(n!==s)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(s):t.hasAnimated||t.set(s)}else{let t=e.getStaticValue(r);e.addValue(r,el(void 0!==t?t:s,{owner:e}))}}for(let r in i)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=el(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){var i;let r=void 0===this.latestValues[e]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,e))?i:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(tf(r)||eE(r))?r=parseFloat(r):!na(r)&&e2.test(t)&&(r=tt(e,t)),this.setBaseTarget(e,ed(r)?r.get():r)),ed(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let i,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let s=d(this.props,r,null==(t=this.presenceContext)?void 0:t.custom);s&&(i=s[e])}if(r&&void 0!==i)return i;let s=this.getBaseTargetFromProps(this.props,e);return void 0===s||ed(s)?void 0!==this.initialValues[e]&&void 0===i?void 0:this.baseTarget[e]:s}on(e,t){return this.events[e]||(this.events[e]=new es),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class nc extends nd{constructor(){super(...arguments),this.KeyframeResolver=tE}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;ed(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class nu extends nc{constructor(){super(...arguments),this.type="html",this.renderInstance=s0}readValueFromInstance(e,t){if(U.has(t)){let e=te(t);return e&&e.default||0}{let i=window.getComputedStyle(e),r=(tg(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return i7(e,t)}build(e,t,i){sq(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return s3(e,t,i)}}class nh extends nc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iX}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(U.has(t)){let e=te(t);return e&&e.default||0}return t=s1.has(t)?t:eu(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return s4(e,t,i)}build(e,t,i){sX(e,t,this.isSVGTag,i.transformTemplate)}renderInstance(e,t,i,r){s2(e,t,i,r)}mount(e){this.isSVGTag=sJ(e.tagName),super.mount(e)}}let nm=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(i,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((ro={animation:{Feature:ij},exit:{Feature:iE},inView:{Feature:sN},tap:{Feature:sP},focus:{Feature:sb},hover:{Feature:sv},pan:{Feature:ra},drag:{Feature:rs,ProjectionNode:sg,MeasureLayout:rw},layout:{ProjectionNode:sg,MeasureLayout:rw}},rl=(e,t)=>s_(e)?new nh(t):new nu(t,{allowProjection:e!==rc.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:r,Component:s}){var n,a;function l(e,n){var a,l,d;let c,u={...(0,rc.useContext)(sS),...e,layoutId:function({layoutId:e}){let t=(0,rc.useContext)(rh).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=u,m=function(e){let{initial:t,animate:i}=function(e,t){if(sM(e)){let{initial:t,animate:i}=e;return{initial:!1===t||o(t)?t:void 0,animate:o(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,rc.useContext)(sV));return(0,rc.useMemo)(()=>({initial:t,animate:i}),[sD(t),sD(i)])}(e),p=r(e,h);if(!h&&sI){l=0,d=0,(0,rc.useContext)(sC).strict;let e=function(e){let{drag:t,layout:i}=sO;if(!t&&!i)return{};let r={...t,...i};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==i?void 0:i.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);c=e.MeasureLayout,m.visualElement=function(e,t,i,r,s){var n,a;let{visualElement:o}=(0,rc.useContext)(sV),l=(0,rc.useContext)(sC),d=(0,rc.useContext)(ru),c=(0,rc.useContext)(sS).reducedMotion,u=(0,rc.useRef)(null);r=r||l.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:c}));let h=u.current,m=(0,rc.useContext)(rm);h&&!h.projection&&s&&("html"===h.type||"svg"===h.type)&&function(e,t,i,r){let{layoutId:s,layout:n,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:d}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:s,layout:n,alwaysMeasureLayout:!!a||o&&iL(o),visualElement:e,animationType:"string"==typeof n?n:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:d})}(u.current,i,s,m);let p=(0,rc.useRef)(!1);(0,rc.useInsertionEffect)(()=>{h&&p.current&&h.update(i,d)});let f=i[eh],x=(0,rc.useRef)(!!f&&!(null==(n=window.MotionHandoffIsComplete)?void 0:n.call(window,f))&&(null==(a=window.MotionHasOptimisedAnimation)?void 0:a.call(window,f)));return sF(()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),ry.render(h.render),x.current&&h.animationState&&h.animationState.animateChanges())}),(0,rc.useEffect)(()=>{h&&(!x.current&&h.animationState&&h.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var e;null==(e=window.MotionHandoffMarkAsComplete)||e.call(window,f)}),x.current=!1))}),h}(s,p,u,t,e.ProjectionNode)}return(0,rd.jsxs)(sV.Provider,{value:m,children:[c&&m.visualElement?(0,rd.jsx)(c,{visualElement:m.visualElement,...u}):null,i(s,e,(a=m.visualElement,(0,rc.useCallback)(e=>{e&&p.onMount&&p.onMount(e),a&&(e?a.mount(e):a.unmount()),n&&("function"==typeof n?n(e):iL(n)&&(n.current=e))},[a])),p,h,m.visualElement)]})}e&&function(e){for(let t in e)sO[t]={...sO[t],...e[t]}}(e),l.displayName=`motion.${"string"==typeof s?s:`create(${null!=(a=null!=(n=s.displayName)?n:s.name)?a:""})`}`;let d=(0,rc.forwardRef)(l);return d[sB]=s,d}({...s_(e)?s9:s8,preloadedFeatures:ro,useRender:function(e=!1){return(t,i,r,{latestValues:s},n)=>{let a=(s_(t)?function(e,t,i,r){let s=(0,rc.useMemo)(()=>{let i=sQ();return sX(i,t,sJ(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};s7(t,e.style,e),s.style={...t,...s.style}}return s}:function(e,t){let i={},r=function(e,t){let i=e.style||{},r={};return s7(r,i,e),Object.assign(r,function({transformTemplate:e},t){return(0,rc.useMemo)(()=>{let i=sK();return sq(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,s,n,t),o=function(e,t,i){let r={};for(let s in e)("values"!==s||"object"!=typeof e.values)&&(ni(s)||!0===i&&nt(s)||!t&&!nt(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}(i,"string"==typeof t,e),l=t!==rc.Fragment?{...o,...a,ref:r}:{},{children:d}=i,c=(0,rc.useMemo)(()=>ed(d)?d.get():d,[d]);return(0,rc.createElement)(t,{...l,children:c})}}(t),createVisualElement:rl,Component:e})}))},4604:(e,t)=>{"use strict";function i(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||i&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return i}})},4953:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(148);let r=i(1480),s=i(2756),n=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let d,c,u,{src:h,sizes:m,unoptimized:p=!1,priority:f=!1,loading:x,className:g,quality:y,width:v,height:b,fill:w=!1,style:P,overrideSrc:j,onLoad:A,onLoadingComplete:E,placeholder:T="empty",blurDataURL:k,fetchPriority:N,decoding:C="async",layout:S,objectFit:V,objectPosition:M,lazyBoundary:R,lazyRoot:D,...I}=e,{imgConf:L,showAltText:O,blurComplete:B,defaultLoader:F}=t,z=L||s.imageConfigDefault;if("allSizes"in z)d=z;else{let e=[...z.deviceSizes,...z.imageSizes].sort((e,t)=>e-t),t=z.deviceSizes.sort((e,t)=>e-t),r=null==(i=z.qualities)?void 0:i.sort((e,t)=>e-t);d={...z,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===F)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let _=I.loader||F;delete I.loader,delete I.srcSet;let W="__next_img_default"in _;if(W){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=_;_=t=>{let{config:i,...r}=t;return e(r)}}if(S){"fill"===S&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[S];e&&(P={...P,...e});let t={responsive:"100vw",fill:"100vw"}[S];t&&!m&&(m=t)}let U="",$=o(v),H=o(b);if((l=h)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(h)?h.default:h;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,k=k||e.blurDataURL,U=e.src,!w)if($||H){if($&&!H){let t=$/e.width;H=Math.round(e.height*t)}else if(!$&&H){let t=H/e.height;$=Math.round(e.width*t)}}else $=e.width,H=e.height}let q=!f&&("lazy"===x||void 0===x);(!(h="string"==typeof h?h:U)||h.startsWith("data:")||h.startsWith("blob:"))&&(p=!0,q=!1),d.unoptimized&&(p=!0),W&&!d.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(p=!0);let Z=o(y),Y=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:V,objectPosition:M}:{},O?{}:{color:"transparent"},P),G=B||"empty"===T?null:"blur"===T?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:$,heightInt:H,blurWidth:c,blurHeight:u,blurDataURL:k||"",objectFit:Y.objectFit})+'")':'url("'+T+'")',X=n.includes(Y.objectFit)?"fill"===Y.objectFit?"100% 100%":"cover":Y.objectFit,K=G?{backgroundSize:X,backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:G}:{},Q=function(e){let{config:t,src:i,unoptimized:r,width:s,quality:n,sizes:a,loader:o}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,i){let{deviceSizes:r,allSizes:s}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,a),c=l.length-1;return{sizes:a||"w"!==d?a:"100vw",srcSet:l.map((e,r)=>o({config:t,src:i,quality:n,width:e})+" "+("w"===d?e:r+1)+d).join(", "),src:o({config:t,src:i,quality:n,width:l[c]})}}({config:d,src:h,unoptimized:p,width:$,quality:Z,sizes:m,loader:_});return{props:{...I,loading:q?"lazy":x,fetchPriority:N,width:$,height:H,decoding:C,className:g,style:{...Y,...K},sizes:Q.sizes,srcSet:Q.srcSet,src:j||Q.src},meta:{unoptimized:p,priority:f,placeholder:T,fill:w}}}},4959:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.AmpContext},5097:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),s=i(4156),n=i(3210);let a=n.forwardRef(function({title:e,titleId:t,...i},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))});var o=i(3635);let l=n.forwardRef(function({title:e,titleId:t,...i},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m0-10.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.75c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.25-8.25-3.286Zm0 13.036h.008v.008H12v-.008Z"}))}),d=n.forwardRef(function({title:e,titleId:t,...i},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}),c=()=>{let e=[{icon:a,title:"Legal Confusion",description:"Potential buyers are confused about the leasehold vs ownership rules and (im)possibilities",color:"text-red-500"},{icon:o.A,title:"Limited Access",description:"Sellers lack efficient access to foreign buyers & long-term investors/renters due to language barriers and lack of online presence",color:"text-orange-500"},{icon:l,title:"Trust Issues",description:"Trust in the real estate space is critically low & both parties are afraid to get scammed",color:"text-yellow-500"}];return(0,r.jsxs)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,r.jsx)(s.P.div,{className:"absolute top-1/4 left-1/4 w-96 h-96 border border-muted rounded-lg transform rotate-12",animate:{rotate:[12,15,12]},transition:{duration:8,repeat:1/0}}),(0,r.jsx)(s.P.div,{className:"absolute bottom-1/4 right-1/4 w-64 h-64 border border-muted rounded-lg transform -rotate-6",animate:{rotate:[-6,-9,-6]},transition:{duration:6,repeat:1/0}})]}),(0,r.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6",children:(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 lg:gap-16 items-center",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"mb-6",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-full text-sm font-semibold",children:[(0,r.jsx)(d,{className:"w-4 h-4 mr-2"}),"Market Challenge"]})}),(0,r.jsxs)(s.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-3xl sm:text-4xl md:text-5xl font-bold text-default mb-4 sm:mb-6 leading-tight",children:["The ",(0,r.jsx)("span",{className:"gradient-text",children:"Problem"})," We're Solving"]}),(0,r.jsx)(s.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-lg sm:text-xl text-muted mb-8 sm:mb-12 leading-relaxed",children:"Bali's property market is plagued by inefficiency, uncertainty, confusion, and mistrust. Both buyers and sellers struggle in an environment lacking transparency."}),(0,r.jsx)("div",{className:"space-y-4 sm:space-y-6",children:e.map((e,t)=>(0,r.jsxs)(s.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.5+.1*t},className:"flex items-start space-x-3 sm:space-x-4 p-4 sm:p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow",children:[(0,r.jsx)("div",{className:`flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 ${e.color} bg-gray-50 rounded-lg flex items-center justify-center`,children:(0,r.jsx)(e.icon,{className:"w-5 h-5 sm:w-6 sm:h-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-default mb-1 sm:mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm sm:text-base text-muted",children:e.description})]})]},t))})]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"relative mt-8 lg:mt-0",children:[(0,r.jsxs)("div",{className:"relative",children:[[void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(s.P.div,{className:"absolute inset-0 bg-white border border-gray-200 rounded-lg shadow-lg",style:{transform:`translate(${8*t}px, ${8*t}px) rotate(${2*t}deg)`,zIndex:3-t},animate:{rotate:[2*t,2*t+2,2*t]},transition:{duration:4+t,repeat:1/0,ease:"easeInOut"}},t)),(0,r.jsxs)("div",{className:"relative z-10 bg-white border-2 border-red-200 rounded-lg p-8 h-96",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(a,{className:"w-8 h-8 text-red-500 mr-3"}),(0,r.jsx)("h3",{className:"text-xl font-bold text-default",children:"Legal Document"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-red-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/2"}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d,{className:"w-5 h-5 text-red-500 mr-2"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-red-700 font-semibold text-sm",children:"UNCLEAR TERMS"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d,{className:"w-5 h-5 text-red-500 mr-2"}),(0,r.jsx)("span",{className:"text-red-700 font-semibold text-sm",children:"EFFECTS OF NON-COMPLIANCE"})]})]})]})]})]}),[void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(s.P.div,{className:"absolute w-8 h-8 bg-red-100 text-red-500 rounded-full flex items-center justify-center font-bold text-lg",style:{top:`${20+25*t}%`,right:`${-10+5*t}%`},animate:{y:[0,-10,0],rotate:[0,5,0]},transition:{duration:2+.5*t,repeat:1/0,delay:.3*t},children:"?"},t))]})]})})]})}},5331:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\FinalCTA.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\FinalCTA.tsx","default")},5617:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});var r=i(687),s=i(3210),n=i(4156);let a=()=>{let[e,t]=(0,s.useState)(1);return(0,s.useEffect)(()=>{let e=()=>{let e=document.querySelectorAll(".snap-section"),i=window.scrollY+window.innerHeight/2;e.forEach((e,r)=>{let s=e.offsetTop,n=s+e.offsetHeight;i>=s&&i<n&&t(r+1)})};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,r.jsxs)("div",{className:"fixed right-8 top-1/2 transform -translate-y-1/2 z-50 hidden lg:block",children:[(0,r.jsx)("div",{className:"flex flex-col space-y-3",children:Array.from({length:9},(t,i)=>(0,r.jsx)(n.P.div,{className:`w-3 h-3 rounded-full border-2 border-primary cursor-pointer transition-all duration-300 ${e===i+1?"bg-primary scale-125":"bg-transparent hover:bg-primary/20"}`,whileHover:{scale:1.2},onClick:()=>{let e=document.querySelectorAll(".snap-section")[i];e?.scrollIntoView({behavior:"smooth"})}},i))}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsxs)("span",{className:"text-sm text-muted font-medium",children:[e," / ",9]})})]})}},5700:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var r=i(3210);let s=r.forwardRef(function({title:e,titleId:t,...i},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))})},5709:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},5990:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),s=i(4156),n=i(3210);let a=n.forwardRef(function({title:e,titleId:t,...i},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var o=i(2969);let l=n.forwardRef(function({title:e,titleId:t,...i},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}),d=n.forwardRef(function({title:e,titleId:t,...i},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}),c=()=>(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-accent/10 py-20",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-6 text-center",children:(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsxs)(s.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"text-4xl md:text-6xl font-bold text-default mb-8 leading-tight",children:["Next Step: Align + ",(0,r.jsx)("span",{className:"gradient-text",children:"Kick‑off"})]}),(0,r.jsx)(s.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-xl text-muted mb-12 max-w-3xl mx-auto",children:"Let’s align on objectives & timelines, then start our pilot phase."}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"flex flex-col sm:flex-row gap-6 justify-center mb-16",children:[(0,r.jsxs)(s.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-primary text-m group relative overflow-hidden w-full sm:w-64",children:[(0,r.jsxs)("span",{className:"relative z-10 flex items-center justify-center",children:[(0,r.jsx)(a,{className:"w-6 h-6 mr-2"}),"Start the Pilot Together"]}),(0,r.jsx)(s.P.div,{className:"absolute inset-0 bg-gradient-to-r from-accent to-primary",initial:{x:"-100%"},whileHover:{x:0},transition:{duration:.3}})]}),(0,r.jsx)(s.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn-secondary text-m group w-full sm:w-64",children:(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(o.A,{className:"w-6 h-6 mr-2"}),"Let's align on Details"]})})]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"bg-white rounded-2xl p-8 shadow-xl border border-gray-100 max-w-4xl mx-auto",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-default mb-8",children:"Let’s Talk Details"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,r.jsxs)(s.P.a,{href:"mailto:<EMAIL>",whileHover:{scale:1.05},className:"flex flex-col items-center p-6 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors group",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",children:(0,r.jsx)(l,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("h4",{className:"font-semibold text-default mb-2",children:"Email"}),(0,r.jsx)("p",{className:"text-sm text-muted",children:"<EMAIL>"})]}),(0,r.jsxs)(s.P.a,{href:"https://wa.me/+6282341891047",whileHover:{scale:1.05},className:"flex flex-col items-center p-6 rounded-xl bg-green-50 hover:bg-green-100 transition-colors group",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",children:(0,r.jsx)(d,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("h4",{className:"font-semibold text-default mb-2",children:"WhatsApp"}),(0,r.jsx)("p",{className:"text-sm text-muted",children:"+62 823 4189 1047"})]}),(0,r.jsxs)(s.P.a,{href:"https://calendly.com/propertyplaza",whileHover:{scale:1.05},className:"flex flex-col items-center p-6 rounded-xl bg-primary/5 hover:bg-primary/10 transition-colors group",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("h4",{className:"font-semibold text-default mb-2",children:"Calendly"}),(0,r.jsx)("p",{className:"text-sm text-muted",children:"Schedule a meeting"})]})]})]}),(0,r.jsx)(s.P.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.8},className:"mt-12 text-center",children:(0,r.jsx)("p",{className:"text-muted text-sm",children:"\xa9 2024 Property Plaza \xd7 Paradise Indonesia Strategic Partnership"})})]})})})},6151:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\components\\\\ScrollIndicator.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\components\\ScrollIndicator.tsx","default")},6289:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\Synergy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Synergy.tsx","default")},6524:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var r=i(3210);let s=r.forwardRef(function({title:e,titleId:t,...i},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},6533:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let r=i(4985),s=i(740),n=i(687),a=s._(i(3210)),o=r._(i(1215)),l=r._(i(512)),d=i(4953),c=i(2756),u=i(7903);i(148);let h=i(9148),m=r._(i(1933)),p=i(3038),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function x(e,t,i,r,s,n,a){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&s(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,s=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>s,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{s=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function g(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let y=(0,a.forwardRef)((e,t)=>{let{src:i,srcSet:r,sizes:s,height:o,width:l,decoding:d,className:c,style:u,fetchPriority:h,placeholder:m,loading:f,unoptimized:y,fill:v,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:P,setShowAltText:j,sizesInput:A,onLoad:E,onError:T,...k}=e,N=(0,a.useCallback)(e=>{e&&(T&&(e.src=e.src),e.complete&&x(e,m,b,w,P,y,A))},[i,m,b,w,P,T,y,A]),C=(0,p.useMergedRef)(t,N);return(0,n.jsx)("img",{...k,...g(h),loading:f,width:l,height:o,decoding:d,"data-nimg":v?"fill":"1",className:c,style:u,sizes:s,srcSet:r,src:i,ref:C,onLoad:e=>{x(e.currentTarget,m,b,w,P,y,A)},onError:e=>{j(!0),"empty"!==m&&P(!0),T&&T(e)}})});function v(e){let{isAppRouter:t,imgAttributes:i}=e,r={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...g(i.fetchPriority)};return t&&o.default.preload?(o.default.preload(i.src,r),null):(0,n.jsx)(l.default,{children:(0,n.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...r},"__nimg-"+i.src+i.srcSet+i.sizes)})}let b=(0,a.forwardRef)((e,t)=>{let i=(0,a.useContext)(h.RouterContext),r=(0,a.useContext)(u.ImageConfigContext),s=(0,a.useMemo)(()=>{var e;let t=f||r||c.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),s=t.deviceSizes.sort((e,t)=>e-t),n=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:s,qualities:n}},[r]),{onLoad:o,onLoadingComplete:l}=e,p=(0,a.useRef)(o);(0,a.useEffect)(()=>{p.current=o},[o]);let x=(0,a.useRef)(l);(0,a.useEffect)(()=>{x.current=l},[l]);let[g,b]=(0,a.useState)(!1),[w,P]=(0,a.useState)(!1),{props:j,meta:A}=(0,d.getImgProps)(e,{defaultLoader:m.default,imgConf:s,blurComplete:g,showAltText:w});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(y,{...j,unoptimized:A.unoptimized,placeholder:A.placeholder,fill:A.fill,onLoadRef:p,onLoadingCompleteRef:x,setBlurComplete:b,setShowAltText:P,sizesInput:e.sizes,ref:t}),A.priority?(0,n.jsx)(v,{isAppRouter:!i,imgAttributes:j}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6562:(e,t,i)=>{Promise.resolve().then(i.bind(i,6151)),Promise.resolve().then(i.bind(i,1199)),Promise.resolve().then(i.bind(i,5331)),Promise.resolve().then(i.bind(i,3398)),Promise.resolve().then(i.bind(i,677)),Promise.resolve().then(i.bind(i,2492)),Promise.resolve().then(i.bind(i,2674)),Promise.resolve().then(i.bind(i,2117)),Promise.resolve().then(i.bind(i,6289)),Promise.resolve().then(i.bind(i,4056))},7180:(e,t,i)=>{"use strict";i.d(t,{default:()=>l});var r=i(687),s=i(4156),n=i(5700),a=i(3635),o=i(6524);let l=()=>{let e=[{icon:(0,r.jsx)(n.A,{className:"w-8 h-8"}),title:"Reach Growth",value:"5K+",description:"Average reach per post",trend:""},{icon:(0,r.jsx)(a.A,{className:"w-8 h-8"}),title:"Engagement",value:"400+",description:"Total engagements",trend:""},{icon:(0,r.jsx)(o.A,{className:"w-8 h-8"}),title:"Qualified Leads",value:"75+",description:"Qualified inquiries",trend:""}];return(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-gray-50 py-12 sm:py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,r.jsx)(s.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-3 sm:px-4 py-2 bg-primary/10 text-primary rounded-full text-xs sm:text-sm font-semibold mb-4 sm:mb-6",children:"Results & Timeline"}),(0,r.jsxs)(s.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-3xl sm:text-4xl md:text-5xl font-bold text-default mb-4 sm:mb-6 leading-tight",children:[(0,r.jsx)("span",{className:"gradient-text",children:"Campaign Results"})," & Timeline"]}),(0,r.jsx)(s.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-lg sm:text-xl text-muted max-w-3xl mx-auto px-4",children:"Measurable outcomes and structured timeline for the 12-week campaign."})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6 sm:gap-8 items-stretch",children:[(0,r.jsx)(s.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"lg:col-span-2 flex",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-100 p-6 w-full flex flex-col",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-default mb-6 text-center",children:"12-Week Campaign Timeline"}),(0,r.jsxs)("div",{className:"space-y-6 flex-1",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.1},className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm",children:"1-4"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-xl font-semibold text-default mb-3",children:"Legal & Process Foundation"}),(0,r.jsx)("p",{className:"text-muted mb-4 leading-relaxed",children:"Establish credibility with investors through clear leasehold terms, developer-issued contracts, and legal due diligence."}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm",children:"Leasehold Duration (>50 yr)"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm",children:"Developer-issued"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm",children:"Licensing & Permits"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm",children:"Trust Building"})]})]})]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-accent to-primary rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm",children:"5-8"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-xl font-semibold text-default mb-3",children:"Material & Build Quality"}),(0,r.jsx)("p",{className:"text-muted mb-4 leading-relaxed",children:"Deep-dive into construction quality, materials and design. Showcase expertise in sustainable building practices."}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("span",{className:"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm",children:"Building materials"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm",children:"Roofing Systems"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm",children:"Finishes (inside/outside)"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-accent/10 text-accent rounded-full text-sm",children:"Interior design"})]})]})]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.3},className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold mr-6 text-sm",children:"9-12"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-xl font-semibold text-default mb-3",children:"ROI & Uniqueness"}),(0,r.jsx)("p",{className:"text-muted mb-4 leading-relaxed",children:"Focus on investment returns and all-in one investment solution. Demonstrate proven track record, investor success stories and the uniqueness of Maison Aela."}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm",children:"Rental Yield Growth"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm",children:"Investor Profiles"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm",children:"Unique concept"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm",children:"Unique location"})]})]})]})]})]})}),(0,r.jsx)(s.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.4},className:"lg:col-span-1 flex",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-100 p-6 w-full flex flex-col",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-default mb-6 text-center",children:"Expected Campaign Results"}),(0,r.jsx)("div",{className:"space-y-6 flex-1 flex flex-col justify-center",children:e.map((e,t)=>(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.4,delay:.3+.1*t},className:"text-center p-4 bg-gradient-to-br from-gray-50 to-white rounded-xl border border-gray-100",children:[(0,r.jsx)("div",{className:"text-primary mb-3 flex justify-center",children:e.icon}),(0,r.jsx)("div",{className:"text-2xl font-bold text-default mb-1",children:e.value}),(0,r.jsx)("div",{className:"text-xs text-muted mb-2",children:e.description}),(0,r.jsx)("div",{className:"text-xs font-semibold text-green-600",children:e.trend})]},t))})]})})]})]})})}},7755:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=i(3210),s=()=>{},n=()=>{};function a(e){var t;let{headManager:i,reduceComponentsToState:a}=e;function o(){if(i&&i.mountedInstances){let t=r.Children.toArray(Array.from(i.mountedInstances).filter(Boolean));i.updateHead(a(t,e))}}return null==i||null==(t=i.mountedInstances)||t.add(e.children),o(),s(()=>{var t;return null==i||null==(t=i.mountedInstances)||t.add(e.children),()=>{var t;null==i||null==(t=i.mountedInstances)||t.delete(e.children)}}),s(()=>(i&&(i._pendingUpdate=o),()=>{i&&(i._pendingUpdate=o)})),n(()=>(i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null),()=>{i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null)})),null}},7903:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.ImageConfigContext},8014:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o,metadata:()=>a});var r=i(7413);i(2704);var s=i(7995),n=i.n(s);let a={title:"Property Plaza \xd7 Paradise Indonesia | Strategic Partnership Proposal",description:"Empowering Property Decisions in Bali through Transparency, Knowledge, Connection & Trust. A strategic collaboration proposal.",keywords:"Property Plaza, Paradise Indonesia, Bali Real Estate, Property Investment, Legal Transparency",authors:[{name:"Property Plaza"}],openGraph:{title:"Property Plaza \xd7 Paradise Indonesia Partnership",description:"Strategic collaboration proposal for empowering property decisions in Bali",type:"website"}};function o({children:e}){return(0,r.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,r.jsx)("body",{className:`${n().className} overflow-x-hidden`,children:e})})}},8509:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.RouterContext},9271:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=i(5239),s=i(8088),n=i(8170),a=i.n(n),o=i(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,597)),"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,8014)),"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\page.tsx"],u={require:i,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9394:(e,t,i)=>{"use strict";i.d(t,{default:()=>x});var r=i(687),s=i(4156),n=i(2969),a=i(3210);let o=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}),l=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),d=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"}))}),c=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v17.25m0 0c-1.472 0-2.882.265-4.185.75M12 20.25c1.472 0 2.882.265 4.185.75M18.75 4.97A48.416 48.416 0 0 0 12 4.5c-2.291 0-4.545.16-6.75.47m13.5 0c1.01.143 2.01.317 3 .52m-3-.52 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.988 5.988 0 0 1-2.031.352 5.988 5.988 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L18.75 4.971Zm-16.5.52c.99-.203 1.99-.377 3-.52m0 0 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.989 5.989 0 0 1-2.031.352 5.989 5.989 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L5.25 4.971Z"}))}),u=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}),h=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"}))});var m=i(5700);let p=a.forwardRef(function({title:e,titleId:t,...i},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});var f=i(942);let x=()=>{let e=[{icon:n.A,title:"12 Week Duration",description:"Extended pilot to measure deep market impact and ROI"},{icon:o,title:"4 Posts Per Week",description:"High-quality content across TikTok, Instagram & LinkedIn"},{icon:l,title:"\xb12M IDR/Week",description:"Professional editor + enhanced posting budget"},{icon:d,title:"Leadmagnet",description:"Free Property Investment Guide for email capture, with topics like: Legal Checklist & ROI Guide"}],t=[{icon:c,title:"Legal & Process",topics:["Leasehold vs. freehold","Transfer requirements","Legal fees","Taxes"]},{icon:u,title:"Material & Build Quality",topics:["Timber vs. concrete pros/cons","Roofing options","Energy-proofing","Finishes"]},{icon:h,title:"Cost Breakdown",topics:["Build cost per m\xb2","Hidden costs","Infrastructure","Permits"]},{icon:m.A,title:"Tenant & ROI",topics:["Rental yield vs. capital growth","Case examples","Short vs. long-term rentals"]},{icon:p,title:"Investor Profiles",topics:["Private expats & families","Small investor funds","Minimum investment","Expected returns"]},{icon:f.A,title:"Local Trust Network",topics:["Vetted architects & lawyers","Trusted funders","Builder verification","FAQs"]}];return(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-12 sm:py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,r.jsx)(s.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-3 sm:px-4 py-2 bg-primary/10 text-primary rounded-full text-xs sm:text-sm font-semibold mb-4 sm:mb-6",children:"Content Strategy"}),(0,r.jsxs)(s.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight",children:[(0,r.jsx)("span",{className:"gradient-text",children:"12-Week"})," Content Strategy"]}),(0,r.jsx)(s.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-xl text-muted max-w-3xl mx-auto",children:"Deep content pillars and multi-platform approach to establish market authority and build trust with potential investors."})]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mb-16",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-default text-center mb-8",children:"Campaign Structure"}),(0,r.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-6",children:e.map((e,t)=>(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4 mx-auto",children:(0,r.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-default mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-muted",children:e.description})]},t))})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-start",children:[(0,r.jsx)(s.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},className:"bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-6 border border-primary/20",children:[(0,r.jsx)("h4",{className:"text-xl font-bold text-default mb-6",children:"\uD83D\uDCA1 Content Pillars (Deep & Broad)"}),(0,r.jsx)("div",{className:"space-y-3",children:t.map((e,t)=>(0,r.jsxs)(s.P.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.4,delay:.6+.1*t},className:"border-l-4 border-primary pl-3",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(e.icon,{className:"w-4 h-4 text-primary mr-2"}),(0,r.jsx)("h5",{className:"font-semibold text-default text-sm",children:e.title})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:e.topics.map((e,t)=>(0,r.jsx)("span",{className:"text-xs bg-white/50 text-muted px-2 py-1 rounded-full",children:e},t))})]},t))})]})}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-default mb-8",children:"Content Strategy & Timeline"}),(0,r.jsxs)("div",{className:"flex space-x-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("div",{className:"w-5 h-5 mr-2 bg-white rounded-sm flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-3 h-3 bg-gradient-to-br from-purple-400 via-pink-500 to-orange-500 rounded-sm"})}),(0,r.jsx)("span",{className:"text-sm font-semibold",children:"Instagram"})]}),(0,r.jsxs)("div",{className:"flex items-center bg-black text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("div",{className:"w-5 h-5 mr-2 bg-white rounded-sm flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-3 h-3 bg-black rounded-full"})}),(0,r.jsx)("span",{className:"text-sm font-semibold",children:"TikTok"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-6",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-primary to-accent h-48 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-white",children:[(0,r.jsx)("h4",{className:"text-xl font-bold mb-2",children:"Real Building Costs in Bali"}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:"What €200k really covers according to legal case files & expert insights"})]})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full mr-3"}),(0,r.jsx)("span",{className:"font-semibold text-sm",children:"indonesian_paradise_property"})]}),(0,r.jsxs)("p",{className:"text-sm text-muted",children:["Clients often ask what a €200k budget actually builds in Bali. Our legal experts share real-world cost ranges and hidden expenses to watch out for.",(0,r.jsx)("span",{className:"text-primary",children:" #BaliProperty #BuildCosts #Investment"})]})]})]})]})]})]})})}},9513:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.HeadManagerContext},9736:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var r=i(687),s=i(4156),n=i(3210);let a=n.forwardRef(function({title:e,titleId:t,...i},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),o=n.forwardRef(function({title:e,titleId:t,...i},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))});var l=i(3635);let d=n.forwardRef(function({title:e,titleId:t,...i},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},i),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),c=()=>{let e=[{icon:a,number:"30",label:"Days Since Launch",description:"Early traction with zero paid marketing"},{icon:o,number:"30+",label:"Active Listings",description:"Verified and growing across key Bali regions"},{icon:l.A,number:"350+",label:"Platform Visitors",description:"Growing international interest"},{icon:d,number:"3",label:"Languages Supported",description:"Bahasa Indonesia, Dutch & English — with more to come"}];return(0,r.jsx)("section",{className:"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-12 sm:py-20",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6",children:(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 lg:gap-16 items-center",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,r.jsx)(s.P.span,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"inline-flex items-center px-3 sm:px-4 py-2 bg-primary/10 text-primary rounded-full text-xs sm:text-sm font-semibold mb-4 sm:mb-6",children:"About Us"}),(0,r.jsxs)(s.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-3xl sm:text-4xl md:text-5xl font-bold text-default mb-4 sm:mb-6 leading-tight",children:[(0,r.jsx)("span",{className:"gradient-text",children:"Property Plaza"}),(0,r.jsx)("br",{}),"Early Success Story"]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"space-y-4 sm:space-y-5 text-muted text-base sm:text-lg md:text-xl leading-relaxed mb-6 sm:mb-8 max-w-3xl",children:[(0,r.jsxs)("p",{children:["In just ",(0,r.jsx)("strong",{children:"30 days live"}),", Property Plaza has attracted international interest — with zero paid ads, agencies or influencer campaigns."]}),(0,r.jsxs)("p",{children:["Driven by a mission to remove confusion and risk from Bali's housing market, we introduced a ",(0,r.jsx)("strong",{children:"dual-platform approach"}),": one site for global seekers (",(0,r.jsx)("code",{children:".com"}),") and one for local property owners (",(0,r.jsx)("code",{children:".id"}),")."]}),(0,r.jsxs)("p",{children:["The early response makes it clear: there’s strong demand for ",(0,r.jsx)("strong",{children:"transparent, direct alternatives"})," to traditional broker models — and users are finding us organically."]})]}),(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-default mb-4",children:"Platform Features"}),(0,r.jsxs)("ul",{className:"space-y-3",children:[(0,r.jsxs)("li",{className:"flex items-center text-muted",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Multilingual property descriptions (NL/ID/EN)"]}),(0,r.jsxs)("li",{className:"flex items-center text-muted",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Dual-platform structure for local and global access"]}),(0,r.jsxs)("li",{className:"flex items-center text-muted",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Transparent ownership and pricing model"]}),(0,r.jsxs)("li",{className:"flex items-center text-muted",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Direct owner–seeker contact, no broker fees "]})]})]})]}),(0,r.jsx)(s.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"grid grid-cols-2 gap-6",children:e.map((e,t)=>(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3+.1*t},viewport:{once:!0},whileHover:{scale:1.05},className:"bg-white rounded-xl p-6 text-center shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,r.jsx)(s.P.div,{className:"text-3xl font-bold text-primary mb-2",initial:{scale:0},whileInView:{scale:1},transition:{duration:.5,delay:.5+.1*t},children:e.number}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-default mb-2",children:e.label}),(0,r.jsx)("p",{className:"text-sm text-muted",children:e.description})]},t))})]})})})}}};var t=require("../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[891],()=>i(9271));module.exports=r})();