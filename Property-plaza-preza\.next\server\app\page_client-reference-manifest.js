globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./app/components/ScrollIndicator.tsx":{"*":{"id":"(ssr)/./app/components/ScrollIndicator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/AboutPropertyPlaza.tsx":{"*":{"id":"(ssr)/./app/sections/AboutPropertyPlaza.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/FinalCTA.tsx":{"*":{"id":"(ssr)/./app/sections/FinalCTA.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/FourPillars.tsx":{"*":{"id":"(ssr)/./app/sections/FourPillars.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/Hero.tsx":{"*":{"id":"(ssr)/./app/sections/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/MarketProblem.tsx":{"*":{"id":"(ssr)/./app/sections/MarketProblem.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/Metrics.tsx":{"*":{"id":"(ssr)/./app/sections/Metrics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/PilotCampaign.tsx":{"*":{"id":"(ssr)/./app/sections/PilotCampaign.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/Synergy.tsx":{"*":{"id":"(ssr)/./app/sections/Synergy.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/WhyParadiseIndonesia.tsx":{"*":{"id":"(ssr)/./app/sections/WhyParadiseIndonesia.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\components\\ScrollIndicator.tsx":{"id":"(app-pages-browser)/./app/components/ScrollIndicator.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\AboutPropertyPlaza.tsx":{"id":"(app-pages-browser)/./app/sections/AboutPropertyPlaza.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\FinalCTA.tsx":{"id":"(app-pages-browser)/./app/sections/FinalCTA.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\FourPillars.tsx":{"id":"(app-pages-browser)/./app/sections/FourPillars.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Hero.tsx":{"id":"(app-pages-browser)/./app/sections/Hero.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\MarketProblem.tsx":{"id":"(app-pages-browser)/./app/sections/MarketProblem.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Metrics.tsx":{"id":"(app-pages-browser)/./app/sections/Metrics.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\PilotCampaign.tsx":{"id":"(app-pages-browser)/./app/sections/PilotCampaign.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\Synergy.tsx":{"id":"(app-pages-browser)/./app/sections/Synergy.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\sections\\WhyParadiseIndonesia.tsx":{"id":"(app-pages-browser)/./app/sections/WhyParadiseIndonesia.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\":[],"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\_PRIVATE\\Property Plaza presentation\\Property-plaza-preza\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/ScrollIndicator.tsx":{"*":{"id":"(rsc)/./app/components/ScrollIndicator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/AboutPropertyPlaza.tsx":{"*":{"id":"(rsc)/./app/sections/AboutPropertyPlaza.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/FinalCTA.tsx":{"*":{"id":"(rsc)/./app/sections/FinalCTA.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/FourPillars.tsx":{"*":{"id":"(rsc)/./app/sections/FourPillars.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/Hero.tsx":{"*":{"id":"(rsc)/./app/sections/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/MarketProblem.tsx":{"*":{"id":"(rsc)/./app/sections/MarketProblem.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/Metrics.tsx":{"*":{"id":"(rsc)/./app/sections/Metrics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/PilotCampaign.tsx":{"*":{"id":"(rsc)/./app/sections/PilotCampaign.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/Synergy.tsx":{"*":{"id":"(rsc)/./app/sections/Synergy.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sections/WhyParadiseIndonesia.tsx":{"*":{"id":"(rsc)/./app/sections/WhyParadiseIndonesia.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}