{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.ts"], "sourcesContent": ["import { useEffect } from 'react'\n\nexport function useFocusTrap(\n  rootRef: React.RefObject<HTMLElement | null>,\n  triggerRef: React.RefObject<HTMLButtonElement | null> | null,\n  active: boolean,\n  onOpenFocus?: () => void\n) {\n  useEffect(() => {\n    let rootNode: HTMLElement | null = null\n\n    function onTab(e: KeyboardEvent) {\n      if (e.key !== 'Tab' || rootNode === null) {\n        return\n      }\n\n      const [firstFocusableNode, lastFocusableNode] =\n        getFocusableNodes(rootNode)\n      const activeElement = getActiveElement(rootNode)\n\n      if (e.shiftKey) {\n        if (activeElement === firstFocusableNode) {\n          lastFocusableNode?.focus()\n          e.preventDefault()\n        }\n      } else {\n        if (activeElement === lastFocusableNode) {\n          firstFocusableNode?.focus()\n          e.preventDefault()\n        }\n      }\n    }\n\n    const id = setTimeout(() => {\n      // Grab this on next tick to ensure the content is mounted\n      rootNode = rootRef.current\n      if (active) {\n        if (onOpenFocus) {\n          onOpenFocus()\n        } else {\n          rootNode?.focus()\n        }\n        rootNode?.addEventListener('keydown', onTab)\n      } else {\n        const activeElement = getActiveElement(rootNode)\n        // Only restore focus if the focus was previously on the content.\n        // This avoids us accidentally focusing on mount when the\n        // user could want to interact with their own app instead.\n        if (triggerRef && rootNode?.contains(activeElement)) {\n          triggerRef.current?.focus()\n        }\n      }\n    })\n\n    return () => {\n      clearTimeout(id)\n      rootNode?.removeEventListener('keydown', onTab)\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [active])\n}\n\nfunction getActiveElement(node: HTMLElement | null) {\n  const root = node?.getRootNode()\n  return root instanceof ShadowRoot\n    ? (root?.activeElement as HTMLElement)\n    : null\n}\n\nfunction getFocusableNodes(node: HTMLElement): [HTMLElement, HTMLElement] | [] {\n  const focusableElements = node.querySelectorAll(\n    'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n  )\n  if (!focusableElements) return []\n  return [\n    focusableElements![0] as HTMLElement,\n    focusableElements![focusableElements!.length - 1] as HTMLElement,\n  ]\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nexport function useClickOutside(\n  rootRef: React.RefObject<HTMLElement | null>,\n  triggerRef: React.RefObject<HTMLButtonElement | null>,\n  active: boolean,\n  close: () => void\n) {\n  useEffect(() => {\n    if (!active) {\n      return\n    }\n\n    function handleClickOutside(event: MouseEvent) {\n      if (\n        !(rootRef.current?.getBoundingClientRect()\n          ? event.clientX >= rootRef.current.getBoundingClientRect()!.left &&\n            event.clientX <= rootRef.current.getBoundingClientRect()!.right &&\n            event.clientY >= rootRef.current.getBoundingClientRect()!.top &&\n            event.clientY <= rootRef.current.getBoundingClientRect()!.bottom\n          : false) &&\n        !(triggerRef.current?.getBoundingClientRect()\n          ? event.clientX >= triggerRef.current.getBoundingClientRect()!.left &&\n            event.clientX <=\n              triggerRef.current.getBoundingClientRect()!.right &&\n            event.clientY >= triggerRef.current.getBoundingClientRect()!.top &&\n            event.clientY <= triggerRef.current.getBoundingClientRect()!.bottom\n          : false)\n      ) {\n        close()\n      }\n    }\n\n    function handleKeyDown(event: KeyboardEvent) {\n      if (event.key === 'Escape') {\n        close()\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    document.addEventListener('keydown', handleKeyDown)\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      document.removeEventListener('keydown', handleKeyDown)\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [active])\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nexport const MENU_DURATION_MS = 200\nexport const MENU_CURVE = 'cubic-bezier(0.175, 0.885, 0.32, 1.1)'\n"], "names": ["MENU_CURVE", "MENU_DURATION_MS", "useClickOutside", "useFocusTrap", "rootRef", "triggerRef", "active", "onOpenFocus", "useEffect", "rootNode", "onTab", "e", "key", "firstFocusableNode", "lastFocusableNode", "getFocusableNodes", "activeElement", "getActiveElement", "shift<PERSON>ey", "focus", "preventDefault", "id", "setTimeout", "current", "addEventListener", "contains", "clearTimeout", "removeEventListener", "node", "root", "getRootNode", "ShadowRoot", "focusableElements", "querySelectorAll", "length", "close", "handleClickOutside", "event", "getBoundingClientRect", "clientX", "left", "right", "clientY", "top", "bottom", "handleKeyDown", "document"], "mappings": ";;;;;;;;;;;;;;;;;IAqIaA,UAAU;eAAVA;;IADAC,gBAAgB;eAAhBA;;IAlDGC,eAAe;eAAfA;;IAhFAC,YAAY;eAAZA;;;uBAFU;AAEnB,SAASA,aACdC,OAA4C,EAC5CC,UAA4D,EAC5DC,MAAe,EACfC,WAAwB;IAExBC,IAAAA,gBAAS,EAAC;QACR,IAAIC,WAA+B;QAEnC,SAASC,MAAMC,CAAgB;YAC7B,IAAIA,EAAEC,GAAG,KAAK,SAASH,aAAa,MAAM;gBACxC;YACF;YAEA,MAAM,CAACI,oBAAoBC,kBAAkB,GAC3CC,kBAAkBN;YACpB,MAAMO,gBAAgBC,iBAAiBR;YAEvC,IAAIE,EAAEO,QAAQ,EAAE;gBACd,IAAIF,kBAAkBH,oBAAoB;oBACxCC,qCAAAA,kBAAmBK,KAAK;oBACxBR,EAAES,cAAc;gBAClB;YACF,OAAO;gBACL,IAAIJ,kBAAkBF,mBAAmB;oBACvCD,sCAAAA,mBAAoBM,KAAK;oBACzBR,EAAES,cAAc;gBAClB;YACF;QACF;QAEA,MAAMC,KAAKC,WAAW;YACpB,0DAA0D;YAC1Db,WAAWL,QAAQmB,OAAO;YAC1B,IAAIjB,QAAQ;gBACV,IAAIC,aAAa;oBACfA;gBACF,OAAO;oBACLE,4BAAAA,SAAUU,KAAK;gBACjB;gBACAV,4BAAAA,SAAUe,gBAAgB,CAAC,WAAWd;YACxC,OAAO;gBACL,MAAMM,gBAAgBC,iBAAiBR;gBACvC,iEAAiE;gBACjE,yDAAyD;gBACzD,0DAA0D;gBAC1D,IAAIJ,eAAcI,4BAAAA,SAAUgB,QAAQ,CAACT,iBAAgB;wBACnDX;qBAAAA,sBAAAA,WAAWkB,OAAO,qBAAlBlB,oBAAoBc,KAAK;gBAC3B;YACF;QACF;QAEA,OAAO;YACLO,aAAaL;YACbZ,4BAAAA,SAAUkB,mBAAmB,CAAC,WAAWjB;QAC3C;IACA,uDAAuD;IACzD,GAAG;QAACJ;KAAO;AACb;AAEA,SAASW,iBAAiBW,IAAwB;IAChD,MAAMC,OAAOD,wBAAAA,KAAME,WAAW;IAC9B,OAAOD,gBAAgBE,aAClBF,wBAAAA,KAAMb,aAAa,GACpB;AACN;AAEA,SAASD,kBAAkBa,IAAiB;IAC1C,MAAMI,oBAAoBJ,KAAKK,gBAAgB,CAC7C;IAEF,IAAI,CAACD,mBAAmB,OAAO,EAAE;IACjC,OAAO;QACLA,iBAAkB,CAAC,EAAE;QACrBA,iBAAkB,CAACA,kBAAmBE,MAAM,GAAG,EAAE;KAClD;AACH;AAIO,SAAShC,gBACdE,OAA4C,EAC5CC,UAAqD,EACrDC,MAAe,EACf6B,KAAiB;IAEjB3B,IAAAA,gBAAS,EAAC;QACR,IAAI,CAACF,QAAQ;YACX;QACF;QAEA,SAAS8B,mBAAmBC,KAAiB;gBAEvCjC,kBAMAC;YAPJ,IACE,CAAED,CAAAA,EAAAA,mBAAAA,QAAQmB,OAAO,qBAAfnB,iBAAiBkC,qBAAqB,MACpCD,MAAME,OAAO,IAAInC,QAAQmB,OAAO,CAACe,qBAAqB,GAAIE,IAAI,IAC9DH,MAAME,OAAO,IAAInC,QAAQmB,OAAO,CAACe,qBAAqB,GAAIG,KAAK,IAC/DJ,MAAMK,OAAO,IAAItC,QAAQmB,OAAO,CAACe,qBAAqB,GAAIK,GAAG,IAC7DN,MAAMK,OAAO,IAAItC,QAAQmB,OAAO,CAACe,qBAAqB,GAAIM,MAAM,GAChE,KAAI,KACR,CAAEvC,CAAAA,EAAAA,sBAAAA,WAAWkB,OAAO,qBAAlBlB,oBAAoBiC,qBAAqB,MACvCD,MAAME,OAAO,IAAIlC,WAAWkB,OAAO,CAACe,qBAAqB,GAAIE,IAAI,IACjEH,MAAME,OAAO,IACXlC,WAAWkB,OAAO,CAACe,qBAAqB,GAAIG,KAAK,IACnDJ,MAAMK,OAAO,IAAIrC,WAAWkB,OAAO,CAACe,qBAAqB,GAAIK,GAAG,IAChEN,MAAMK,OAAO,IAAIrC,WAAWkB,OAAO,CAACe,qBAAqB,GAAIM,MAAM,GACnE,KAAI,GACR;gBACAT;YACF;QACF;QAEA,SAASU,cAAcR,KAAoB;YACzC,IAAIA,MAAMzB,GAAG,KAAK,UAAU;gBAC1BuB;YACF;QACF;QAEAW,SAAStB,gBAAgB,CAAC,aAAaY;QACvCU,SAAStB,gBAAgB,CAAC,WAAWqB;QAErC,OAAO;YACLC,SAASnB,mBAAmB,CAAC,aAAaS;YAC1CU,SAASnB,mBAAmB,CAAC,WAAWkB;QAC1C;IACA,uDAAuD;IACzD,GAAG;QAACvC;KAAO;AACb;AAIO,MAAML,mBAAmB;AACzB,MAAMD,aAAa"}