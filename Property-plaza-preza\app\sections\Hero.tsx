"use client";

import { motion } from 'framer-motion';

const Hero = () => {
  const scrollToNext = () => {
    const nextSection = document.querySelectorAll('.snap-section')[1];
    nextSection?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="snap-section relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-primary/5">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #b78b4c 2px, transparent 2px),
                           radial-gradient(circle at 75% 75%, #8a6b3c 1px, transparent 1px)`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      {/* Floating Elements - Hidden on mobile */}
      <motion.div
        className="absolute top-10 sm:top-20 left-4 sm:left-20 w-16 h-16 sm:w-32 sm:h-32 rounded-full bg-primary/10 blur-xl hidden sm:block"
        animate={{
          y: [0, -20, 0],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <motion.div
        className="absolute bottom-16 sm:bottom-32 right-4 sm:right-32 w-12 h-12 sm:w-24 sm:h-24 rounded-full bg-accent/10 blur-xl hidden sm:block"
        animate={{
          y: [0, 20, 0],
          scale: [1, 0.9, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Main Content */}
      <div className="relative z-10 text-center max-w-6xl mx-auto px-6">
        {/* Logo/Brand */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-6 sm:mb-8"
        >
          <div className="inline-flex items-center space-x-2 sm:space-x-4 bg-white/80 backdrop-blur-sm rounded-full px-4 sm:px-6 py-2 sm:py-3 shadow-lg">
            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-xs sm:text-sm">PP</span>
            </div>
            <span className="text-default font-semibold text-sm sm:text-base">Property Plaza</span>
            <span className="text-muted text-sm sm:text-base">×</span>
            <span className="text-default font-semibold text-sm sm:text-base">Paradise Indonesia</span>
          </div>
        </motion.div>

        {/* Main Title */}
        <motion.h1
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-3xl sm:text-5xl md:text-7xl lg:text-8xl font-bold mb-4 sm:mb-6 leading-tight"
        >
          <span className="gradient-text">Empower</span>
          <br />
          <span className="text-default">Property Decisions</span>
          <br />
          <span className="text-primary">in Bali</span>
        </motion.h1>

        {/* Subtitle */}
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-lg sm:text-xl md:text-2xl text-muted mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed px-4"
        >
          <span className="font-semibold text-primary">Transparency.</span>{' '}
          <span className="font-semibold text-primary">Knowledge.</span>{' '}
          <span className="font-semibold text-primary">Connection.</span>{' '}
          <span className="font-semibold text-primary">Empowerment.</span>
        </motion.p>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <button
            onClick={scrollToNext}
            className="btn-primary text-lg group relative overflow-hidden"
          >
            <span className="relative z-10">Start the Experience</span>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-accent to-primary"
              initial={{ x: '-100%' }}
              whileHover={{ x: 0 }}
              transition={{ duration: 0.3 }}
            />
          </button>
        </motion.div>


      </div>
    </section>
  );
};

export default Hero;
