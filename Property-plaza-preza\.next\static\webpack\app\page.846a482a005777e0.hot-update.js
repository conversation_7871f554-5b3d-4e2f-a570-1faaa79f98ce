"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/sections/AboutPropertyPlaza.tsx":
/*!*********************************************!*\
  !*** ./app/sections/AboutPropertyPlaza.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst AboutPropertyPlaza = ()=>{\n    const stats = [\n        {\n            icon: _barrel_optimize_names_ChartBarIcon_ClockIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            number: \"30\",\n            label: \"Days Since Launch\",\n            description: \"Early traction with zero paid marketing\"\n        },\n        {\n            icon: _barrel_optimize_names_ChartBarIcon_ClockIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            number: \"30+\",\n            label: \"Active Listings\",\n            description: \"Verified and growing across key Bali regions\"\n        },\n        {\n            icon: _barrel_optimize_names_ChartBarIcon_ClockIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            number: \"350+\",\n            label: \"Platform Visitors\",\n            description: \"Growing international interest\"\n        },\n        {\n            icon: _barrel_optimize_names_ChartBarIcon_ClockIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            number: \"3\",\n            label: \"Languages Supported\",\n            description: \"Bahasa Indonesia, Dutch & English — with more to come\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-16 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6\",\n                                children: \"About Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                className: \"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Property Plaza\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Early Success Story\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.4\n                                },\n                                className: \"space-y-5 text-muted text-lg md:text-xl leading-relaxed mb-8 max-w-3xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"In just \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"30 days live\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            \", Property Plaza has attracted international interest — with zero paid ads, agencies or influencer campaigns.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 3\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Driven by a mission to remove confusion and risk from Bali's housing market, we introduced a \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"dual-platform approach\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \": one site for global seekers (\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                children: \".com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 6\n                                            }, undefined),\n                                            \") and one for local property owners (\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                children: \".id\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 60\n                                            }, undefined),\n                                            \").\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 3\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"The early response makes it clear: there’s strong demand for \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"transparent, direct alternatives\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 66\n                                            }, undefined),\n                                            \" to traditional broker models — and users are finding us organically.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 3\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 1\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.5\n                                },\n                                className: \"bg-white rounded-xl p-6 shadow-lg border border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-default mb-4\",\n                                        children: \"Platform Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center text-muted\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-primary rounded-full mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Multilingual property descriptions (NL/DE/EN)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center text-muted\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-primary rounded-full mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Dual-platform structure for local and global access\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center text-muted\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-primary rounded-full mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Transparent ownership and pricing model\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center text-muted\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-primary rounded-full mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Direct owner–seeker contact, no broker fees \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"grid grid-cols-2 gap-6\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3 + index * 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                className: \"bg-white rounded-xl p-6 text-center shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        className: \"text-3xl font-bold text-primary mb-2\",\n                                        initial: {\n                                            scale: 0\n                                        },\n                                        whileInView: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.5 + index * 0.1\n                                        },\n                                        children: stat.number\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-default mb-2\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted\",\n                                        children: stat.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\AboutPropertyPlaza.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AboutPropertyPlaza;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutPropertyPlaza);\nvar _c;\n$RefreshReg$(_c, \"AboutPropertyPlaza\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/sections/AboutPropertyPlaza.tsx\n"));

/***/ })

});