@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

/* Enable scroll snap only on larger screens */
@media (min-width: 1024px) {
  html {
    scroll-snap-type: y mandatory;
  }

  .snap-section {
    scroll-snap-align: start;
  }
}

body {
  @apply bg-background text-default;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Scroll Snap Sections */
.snap-section {
  min-height: 100vh;
  width: 100%;
}

/* Mobile viewport adjustments */
@media (max-width: 1023px) {
  .snap-section {
    min-height: 100dvh; /* Use dynamic viewport height on mobile */
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-background;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-accent;
}

/* Smooth Animations */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #b78b4c 0%, #8a6b3c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button Styles */
.btn-primary {
  @apply bg-primary hover:bg-accent text-white font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply border-2 border-primary text-primary hover:bg-primary hover:text-white font-semibold py-4 px-8 rounded-full transition-all duration-300;
}
