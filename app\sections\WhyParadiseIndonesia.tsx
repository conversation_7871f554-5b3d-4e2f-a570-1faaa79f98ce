"use client";

import { motion } from 'framer-motion';
import { ShieldCheckIcon, StarIcon, LinkIcon } from '@heroicons/react/24/outline';

const WhyParadiseIndonesia = () => {
  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-primary/5 py-20">
      <div className="max-w-6xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6"
          >
            Strategic Partnership
          </motion.span>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl md:text-5xl font-bold text-default mb-8 leading-tight"
          >
            Why <span className="gradient-text">Paradise Indonesia</span>?
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white rounded-2xl p-12 shadow-xl border border-gray-100 mb-12"
          >
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <ShieldCheckIcon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-default mb-3">Legal Integrity</h3>
                <p className="text-muted">Known for expertise and market presence in Bali real estate law</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <StarIcon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-default mb-3">Market Reputation</h3>
                <p className="text-muted">Established trust and credibility with international clients</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center mx-auto mb-4">
                  <LinkIcon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-default mb-3">Perfect Synergy</h3>
                <p className="text-muted">They bring reputation, we bring reach and modern technology</p>
              </div>
            </div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-8 border border-primary/20"
            >
              <blockquote className="text-2xl md:text-3xl font-bold text-default mb-4 leading-relaxed">
                "You bring <span className="gradient-text">reputation</span>. 
                <br />We bring <span className="gradient-text">reach</span>."
              </blockquote>
              <p className="text-lg text-muted">
                Together, we create the perfect foundation for trust in Bali's property market.
              </p>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyParadiseIndonesia;
