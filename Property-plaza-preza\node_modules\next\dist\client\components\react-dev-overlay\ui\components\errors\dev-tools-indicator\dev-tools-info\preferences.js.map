{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/preferences.ts"], "sourcesContent": ["import { useState } from 'react'\nimport {\n  STORAGE_KEY_POSITION,\n  STORAGE_KEY_SCALE,\n  STORAGE_KEY_THEME,\n} from '../../../../../shared'\n\nconst INDICATOR_POSITION =\n  (process.env\n    .__NEXT_DEV_INDICATOR_POSITION as typeof window.__NEXT_DEV_INDICATOR_POSITION) ||\n  'bottom-left'\n\nexport type DevToolsIndicatorPosition = typeof INDICATOR_POSITION\n\nexport function getInitialPosition() {\n  if (\n    typeof localStorage !== 'undefined' &&\n    localStorage.getItem(STORAGE_KEY_POSITION)\n  ) {\n    return localStorage.getItem(\n      STORAGE_KEY_POSITION\n    ) as DevToolsIndicatorPosition\n  }\n  return INDICATOR_POSITION\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nconst BASE_SIZE = 16\n\nexport const NEXT_DEV_TOOLS_SCALE = {\n  Small: BASE_SIZE / 14,\n  Medium: BASE_SIZE / 16,\n  Large: BASE_SIZE / 18,\n} as const\n\nexport type DevToolsScale =\n  (typeof NEXT_DEV_TOOLS_SCALE)[keyof typeof NEXT_DEV_TOOLS_SCALE]\n\nfunction getInitialScale() {\n  if (\n    typeof localStorage !== 'undefined' &&\n    localStorage.getItem(STORAGE_KEY_SCALE)\n  ) {\n    return Number(localStorage.getItem(STORAGE_KEY_SCALE)) as DevToolsScale\n  }\n  return NEXT_DEV_TOOLS_SCALE.Medium\n}\n\nexport function useDevToolsScale(): [\n  DevToolsScale,\n  (value: DevToolsScale) => void,\n] {\n  const [scale, setScale] = useState<DevToolsScale>(getInitialScale())\n\n  function set(value: DevToolsScale) {\n    setScale(value)\n    localStorage.setItem(STORAGE_KEY_SCALE, String(value))\n  }\n\n  return [scale, set]\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nexport function getInitialTheme() {\n  if (typeof localStorage === 'undefined') {\n    return 'system'\n  }\n  const theme = localStorage.getItem(STORAGE_KEY_THEME)\n  return theme === 'dark' || theme === 'light' ? theme : 'system'\n}\n"], "names": ["NEXT_DEV_TOOLS_SCALE", "getInitialPosition", "getInitialTheme", "useDevToolsScale", "INDICATOR_POSITION", "process", "env", "__NEXT_DEV_INDICATOR_POSITION", "localStorage", "getItem", "STORAGE_KEY_POSITION", "BASE_SIZE", "Small", "Medium", "Large", "getInitialScale", "STORAGE_KEY_SCALE", "Number", "scale", "setScale", "useState", "set", "value", "setItem", "String", "theme", "STORAGE_KEY_THEME"], "mappings": ";;;;;;;;;;;;;;;;;IA8BaA,oBAAoB;eAApBA;;IAhBGC,kBAAkB;eAAlBA;;IAmDAC,eAAe;eAAfA;;IAhBAC,gBAAgB;eAAhBA;;;uBAjDS;wBAKlB;AAEP,MAAMC,qBACJ,AAACC,QAAQC,GAAG,CACTC,6BAA6B,IAChC;AAIK,SAASN;IACd,IACE,OAAOO,iBAAiB,eACxBA,aAAaC,OAAO,CAACC,4BAAoB,GACzC;QACA,OAAOF,aAAaC,OAAO,CACzBC,4BAAoB;IAExB;IACA,OAAON;AACT;AAEA,sFAAsF;AAEtF,MAAMO,YAAY;AAEX,MAAMX,uBAAuB;IAClCY,OAAOD,YAAY;IACnBE,QAAQF,YAAY;IACpBG,OAAOH,YAAY;AACrB;AAKA,SAASI;IACP,IACE,OAAOP,iBAAiB,eACxBA,aAAaC,OAAO,CAACO,yBAAiB,GACtC;QACA,OAAOC,OAAOT,aAAaC,OAAO,CAACO,yBAAiB;IACtD;IACA,OAAOhB,qBAAqBa,MAAM;AACpC;AAEO,SAASV;IAId,MAAM,CAACe,OAAOC,SAAS,GAAGC,IAAAA,eAAQ,EAAgBL;IAElD,SAASM,IAAIC,KAAoB;QAC/BH,SAASG;QACTd,aAAae,OAAO,CAACP,yBAAiB,EAAEQ,OAAOF;IACjD;IAEA,OAAO;QAACJ;QAAOG;KAAI;AACrB;AAIO,SAASnB;IACd,IAAI,OAAOM,iBAAiB,aAAa;QACvC,OAAO;IACT;IACA,MAAMiB,QAAQjB,aAAaC,OAAO,CAACiB,yBAAiB;IACpD,OAAOD,UAAU,UAAUA,UAAU,UAAUA,QAAQ;AACzD"}