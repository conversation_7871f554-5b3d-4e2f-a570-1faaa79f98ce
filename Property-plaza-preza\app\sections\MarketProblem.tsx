"use client";

import { motion } from 'framer-motion';
import { ExclamationTriangleIcon, DocumentTextIcon, UserGroupIcon, ShieldExclamationIcon } from '@heroicons/react/24/outline';

const MarketProblem = () => {
  const problems = [
    {
      icon: DocumentTextIcon,
      title: "Legal Confusion",
      description: "Buyers are confused by leasehold vs ownership rules",
      color: "text-red-500"
    },
    {
      icon: UserGroupIcon,
      title: "Limited Access",
      description: "Sellers lack access to serious foreign buyers",
      color: "text-orange-500"
    },
    {
      icon: ShieldExclamationIcon,
      title: "Trust Issues",
      description: "Trust in the real estate space is critically low",
      color: "text-yellow-500"
    }
  ];

  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 border border-muted rounded-lg transform rotate-12"
          animate={{ rotate: [12, 15, 12] }}
          transition={{ duration: 8, repeat: Infinity }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-64 h-64 border border-muted rounded-lg transform -rotate-6"
          animate={{ rotate: [-6, -9, -6] }}
          transition={{ duration: 6, repeat: Infinity }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-6"
            >
              <span className="inline-flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-full text-sm font-semibold">
                <ExclamationTriangleIcon className="w-4 h-4 mr-2" />
                Market Challenge
              </span>
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-4xl md:text-5xl font-bold text-default mb-6 leading-tight"
            >
              The <span className="gradient-text">Problem</span> We're Solving
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-xl text-muted mb-12 leading-relaxed"
            >
              Bali's property market is plagued by uncertainty, confusion, and mistrust. 
              Both buyers and sellers struggle in an environment lacking transparency.
            </motion.p>

            <div className="space-y-6">
              {problems.map((problem, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                  className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className={`flex-shrink-0 w-12 h-12 ${problem.color} bg-gray-50 rounded-lg flex items-center justify-center`}>
                    <problem.icon className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-default mb-2">{problem.title}</h3>
                    <p className="text-muted">{problem.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Right Side - Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Document Stack Visual */}
            <div className="relative">
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute inset-0 bg-white border border-gray-200 rounded-lg shadow-lg"
                  style={{
                    transform: `translate(${i * 8}px, ${i * 8}px) rotate(${i * 2}deg)`,
                    zIndex: 3 - i
                  }}
                  animate={{
                    rotate: [i * 2, i * 2 + 2, i * 2],
                  }}
                  transition={{
                    duration: 4 + i,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              ))}
              
              <div className="relative z-10 bg-white border-2 border-red-200 rounded-lg p-8 h-96">
                <div className="flex items-center mb-6">
                  <DocumentTextIcon className="w-8 h-8 text-red-500 mr-3" />
                  <h3 className="text-xl font-bold text-default">Legal Document</h3>
                </div>
                
                <div className="space-y-4">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                  <div className="h-4 bg-red-200 rounded animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
                  
                  <div className="mt-8 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center">
                      <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
                      <span className="text-red-700 font-semibold text-sm">UNCLEAR TERMS</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Question Marks */}
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-8 h-8 bg-red-100 text-red-500 rounded-full flex items-center justify-center font-bold text-lg"
                style={{
                  top: `${20 + i * 25}%`,
                  right: `${-10 + i * 5}%`,
                }}
                animate={{
                  y: [0, -10, 0],
                  rotate: [0, 5, 0],
                }}
                transition={{
                  duration: 2 + i * 0.5,
                  repeat: Infinity,
                  delay: i * 0.3
                }}
              >
                ?
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default MarketProblem;
